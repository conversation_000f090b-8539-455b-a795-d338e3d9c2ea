<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit;
}

// Verifica se é o super administrador
if ($_SESSION['user_pousada_id'] != 0) {
    http_response_code(403);
    echo json_encode(['error' => 'Acesso negado']);
    exit;
}

$cnpj = $_GET['cnpj'] ?? '';
$id_excluir = $_GET['id'] ?? 0; // Para edição, excluir o próprio registro da verificação

if (empty($cnpj)) {
    echo json_encode(['error' => 'CNPJ não informado']);
    exit;
}

// Remove formatação do CNPJ
$cnpj_limpo = preg_replace('/[^0-9]/', '', $cnpj);

// Valida formato do CNPJ
if (strlen($cnpj_limpo) != 14) {
    echo json_encode(['valid' => false, 'message' => 'CNPJ deve ter 14 dígitos']);
    exit;
}

// Valida dígitos verificadores do CNPJ
function validarCNPJ($cnpj) {
    $cnpj = preg_replace('/[^0-9]/', '', $cnpj);
    
    if (strlen($cnpj) != 14) return false;
    
    // Verifica se todos os dígitos são iguais
    if (preg_match('/(\d)\1{13}/', $cnpj)) return false;
    
    // Calcula o primeiro dígito verificador
    $soma = 0;
    $peso = 5;
    for ($i = 0; $i < 12; $i++) {
        $soma += $cnpj[$i] * $peso;
        $peso = ($peso == 2) ? 9 : $peso - 1;
    }
    $resto = $soma % 11;
    $dv1 = ($resto < 2) ? 0 : 11 - $resto;
    
    // Calcula o segundo dígito verificador
    $soma = 0;
    $peso = 6;
    for ($i = 0; $i < 13; $i++) {
        $soma += $cnpj[$i] * $peso;
        $peso = ($peso == 2) ? 9 : $peso - 1;
    }
    $resto = $soma % 11;
    $dv2 = ($resto < 2) ? 0 : 11 - $resto;
    
    return ($cnpj[12] == $dv1 && $cnpj[13] == $dv2);
}

if (!validarCNPJ($cnpj_limpo)) {
    echo json_encode(['valid' => false, 'message' => 'CNPJ inválido']);
    exit;
}

// Verifica se já existe no banco
$sql = "SELECT id, nome FROM pousadas WHERE cnpj = ?";
if ($id_excluir > 0) {
    $sql .= " AND id != ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('si', $cnpj_limpo, $id_excluir);
} else {
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $cnpj_limpo);
}

$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $pousada = $result->fetch_assoc();
    echo json_encode([
        'valid' => false, 
        'message' => 'CNPJ já cadastrado para: ' . $pousada['nome'],
        'exists' => true
    ]);
} else {
    echo json_encode(['valid' => true, 'message' => 'CNPJ válido']);
}

$stmt->close();
?>