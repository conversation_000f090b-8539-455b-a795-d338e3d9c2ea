// Importar DateTimeValidator do módulo
import { DateTimeValidator } from './valida_data_anterior.js';
// Importar gerenciador de tooltips
import { tooltipManager } from './tooltip-manager.js';
class ModalReserva {
    constructor() {
        this.modalReserva = null;
        this.container = null;
        this.dateValidator = new DateTimeValidator();
    }
    carregarFormularioReserva(uh, data, hospedeId, hospedeNome) {
        this.modalReserva = new bootstrap.Modal(document.getElementById('novaReservaModal'));
        this.container = document.getElementById('formularioReservaContainer');
        this.updateModalHeader(uh, data, typeof hospedeId === 'object' ? hospedeId.nome : hospedeNome);
        this.modalReserva.show();
        this.loadFormulario(uh, data, hospedeId, hospedeNome);
    }
    updateModalHeader(uh, data, hospedeNome) {
        const modalUhElement = document.getElementById('modalUhReserva');
        const modalDataElement = document.getElementById('modalDataReserva');
        const modalHospedeElement = document.getElementById('modalHospedeReserva');
        if (modalUhElement)
            modalUhElement.textContent = uh;
        if (modalDataElement)
            modalDataElement.textContent = data;
        if (modalHospedeElement)
            modalHospedeElement.textContent = hospedeNome || 'Reserva Direta';
    }
    loadFormulario(uh, data, hospedeId, hospedeNome) {
        // Verificar se hospedeId é um objeto (novo formato) ou um ID simples (formato antigo)
        let realHospedeId;
        let realHospedeNome;
        if (typeof hospedeId === 'object' && hospedeId !== null) {
            // Novo formato: hospede é um objeto { id, nome }
            realHospedeId = hospedeId.id;
            realHospedeNome = hospedeId.nome;
        }
        else {
            // Formato antigo: hospedeId e hospedeNome são valores separados
            realHospedeId = hospedeId;
            realHospedeNome = hospedeNome;
        }
        // Criar FormData para enviar o nome do hóspede
        const formData = new FormData();
        if (realHospedeNome) {
            formData.append('hospede_nome', realHospedeNome);
        }
        fetch('carregar_formulario_reserva.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then((responseData) => {
            if (responseData.success && this.container && responseData.html) {
                this.container.innerHTML = responseData.html;
                setTimeout(() => {
                    this.setupForm(uh, data, hospedeId, hospedeNome);
                }, 100);
            }
            else if (this.container) {
                this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
            }
        })
            .catch(error => {
            console.error('Erro:', error);
            if (this.container) {
                this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
            }
        });
    }
    setupForm(uh, data, hospedeId, hospedeNome) {
        this.preencherCampos(uh, data, hospedeId, hospedeNome);
        this.setupFormValidation();
        this.setupDisponibilidadeCheck();
    }
    preencherCampos(uh, data, hospedeId, hospedeNome) {
        // Verificar se hospedeId é um objeto (novo formato) ou um ID simples (formato antigo)
        let realHospedeId;
        let realHospedeNome;
        if (typeof hospedeId === 'object' && hospedeId !== null) {
            // Novo formato: hospede é um objeto { id, nome }
            realHospedeId = hospedeId.id;
            realHospedeNome = hospedeId.nome;
        }
        else {
            // Formato antigo: hospedeId e hospedeNome são valores separados
            realHospedeId = hospedeId;
            realHospedeNome = hospedeNome;
        }
        // Preencher campos hidden
        const hospedeIdField = document.getElementById('hospede_id_hidden');
        const hospedeNomeField = document.getElementById('hospede_nome_hidden');
        const uhField = document.getElementById('uh_selecionada_hidden');
        const dataField = document.getElementById('data_selecionada_hidden');
        if (hospedeIdField)
            hospedeIdField.value = realHospedeId || '';
        if (hospedeNomeField)
            hospedeNomeField.value = realHospedeNome || '';
        if (uhField)
            uhField.value = uh;
        if (dataField)
            dataField.value = data;
        // Preencher campo UH no formulário
        const uhFormField = document.querySelector('input[name="uh"]');
        if (uhFormField) {
            uhFormField.value = uh;
            // Adicionar formatação com zeros à esquerda
            uhFormField.addEventListener('blur', function () {
                this.value = this.value.padStart(3, '0');
            });
        }
        // Processar data para formato YYYY-MM-DD se necessário
        const dataFormatada = this.converterDataParaISO(data);
        const dataEntradaField = document.querySelector('input[name="dataentrada"]');
        if (dataEntradaField && dataFormatada) {
            dataEntradaField.value = dataFormatada;
            // Calcular data de saída (próximo dia)
            const dataSaida = new Date(dataFormatada);
            dataSaida.setDate(dataSaida.getDate() + 1);
            const dataSaidaFormatada = dataSaida.toISOString().split('T')[0];
            const dataSaidaField = document.querySelector('input[name="datasaida"]');
            if (dataSaidaField)
                dataSaidaField.value = dataSaidaFormatada;
        }
        // Carregar script func.js para funcionalidades do formulário
        if (!document.querySelector('script[src="custom/js/func.js"]')) {
            const script = document.createElement('script');
            script.src = 'custom/js/func.js';
            document.head.appendChild(script);
        }
    }
    setupFormValidation() {
        const form = document.getElementById('formReservaCompleto');
        if (form && this.container) {
            form.addEventListener('submit', (e) => {
                var _a, _b;
                // Verificar se há conflitos antes de enviar
                const avisosConflito = (_b = (_a = this.container) === null || _a === void 0 ? void 0 : _a.querySelectorAll('.aviso-conflito')) !== null && _b !== void 0 ? _b : [];
                if (avisosConflito.length > 0) {
                    e.preventDefault();
                    alert('Não é possível salvar a reserva devido a conflitos de disponibilidade.');
                    return false;
                }
                e.preventDefault();
                this.salvarReservaCompleta(form);
            });
        }
    }
    setupDisponibilidadeCheck() {
        // Configurar verificação de disponibilidade nos campos
        const uhFormField = document.querySelector('input[name="uh"]');
        const dataEntradaField = document.querySelector('input[name="dataentrada"]');
        const horaEntradaField = document.querySelector('input[name="horaentrada"]');
        const dataSaidaField = document.querySelector('input[name="datasaida"]');
        const horaSaidaField = document.querySelector('input[name="horasaida"]');
        if (uhFormField && dataEntradaField) {
            // Adicionar verificação quando os campos mudarem
            [uhFormField, dataEntradaField, horaEntradaField, dataSaidaField, horaSaidaField].forEach(field => {
                if (field) {
                    field.addEventListener('change', () => {
                        this.verificarDisponibilidadeModal();
                    });
                    field.addEventListener('blur', () => {
                        this.verificarDisponibilidadeModal();
                    });
                }
            });
            // Verificar disponibilidade imediatamente após carregar
            setTimeout(() => {
                this.verificarDisponibilidadeModal();
            }, 200);
        }
        // Inicializar validações de data no quadro único
        setTimeout(() => {
            this.inicializarValidacoesDatas();
        }, 300);
    }
    verificarDisponibilidadeModal() {
        // Função removida - não há mais validações visuais de conflito
        this.ocultarAvisoUnico();
    }
    processarResultadoVerificacao(data, error, dataField) {
        if (error) {
            console.error('Erro na verificação:', error);
            return;
        }
        if (data && !data.disponivel && this.container) {
            // Mostrar aviso no quadro único
            this.mostrarAvisoUnico(data.mensagem || 'UH não disponível no período.');
        }
        else {
            // Ocultar aviso se não há conflito
            this.ocultarAvisoUnico();
        }
    }
    mostrarAvisoUnico(mensagem) {
        const avisoElement = document.getElementById('avisoValidacao');
        const mensagemElement = document.getElementById('mensagemAviso');
        if (avisoElement && mensagemElement) {
            mensagemElement.textContent = mensagem;
            avisoElement.style.display = 'block';
        }
    }
    ocultarAvisoUnico() {
        const avisoElement = document.getElementById('avisoValidacao');
        if (avisoElement) {
            avisoElement.style.display = 'none';
        }
    }
    inicializarValidacoesDatas() {
        const dataEntradaField = document.querySelector('input[name="dataentrada"]');
        const dataSaidaField = document.querySelector('input[name="datasaida"]');
        if (dataEntradaField) {
            dataEntradaField.addEventListener('blur', () => this.validarDatas());
        }
        if (dataSaidaField) {
            dataSaidaField.addEventListener('blur', () => this.validarDatas());
        }
    }
    validarDatas() {
        const dataEntradaField = document.querySelector('input[name="dataentrada"]');
        const dataSaidaField = document.querySelector('input[name="datasaida"]');
        if (!dataEntradaField || !dataSaidaField)
            return;
        const dataEntrada = dataEntradaField.value;
        const dataSaida = dataSaidaField.value;
        const mensagens = [];
        // Verificar se data entrada é anterior a hoje
        if (dataEntrada) {
            const hoje = new Date();
            const hojeStr = hoje.getFullYear() + '-' +
                String(hoje.getMonth() + 1).padStart(2, '0') + '-' +
                String(hoje.getDate()).padStart(2, '0');
            if (dataEntrada < hojeStr) {
                mensagens.push('Data de entrada é anterior à data atual (reserva retroativa)');
            }
        }
        // Verificar se data saída é anterior à data entrada
        if (dataEntrada && dataSaida && dataSaida < dataEntrada) {
            mensagens.push('Data de saída não pode ser anterior à data de entrada');
        }
        // Mostrar mensagens cumulativas ou ocultar se não há erros
        if (mensagens.length > 0) {
            this.mostrarAvisoUnico(mensagens.join(' • '));
        }
        else {
            this.ocultarAvisoUnico();
        }
    }
    salvarReservaCompleta(form) {
        const formData = new FormData(form);
        fetch('reservas_salvar.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.text())
            .then(text => {
            // Verificar se a resposta está vazia
            if (!text.trim()) {
                throw new Error('Resposta vazia do servidor');
            }
            // Tentar fazer parse do JSON
            let data;
            try {
                data = JSON.parse(text);
            }
            catch (e) {
                // Se não for JSON, considerar como sucesso (compatibilidade)
                alert('Reserva criada com sucesso!');
                const modal = bootstrap.Modal.getInstance(document.getElementById('novaReservaModal'));
                if (modal)
                    modal.hide();
                // Recarregar página para atualizar o mapa
                setTimeout(() => location.reload(), 500);
                return;
            }
            if (data.success) {
                alert('Reserva criada com sucesso!');
                const modal = bootstrap.Modal.getInstance(document.getElementById('novaReservaModal'));
                if (modal)
                    modal.hide();
                // Recarregar página para atualizar o mapa
                setTimeout(() => location.reload(), 500);
            }
            else {
                alert('Erro ao salvar reserva: ' + (data.message || 'Erro desconhecido'));
            }
        })
            .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao processar a requisição: ' + error.message);
        });
    }
    // Função para remover avisos de conflito anteriores
    removerAvisosConflito() {
        // Remover tooltips ativos
        if (this.container) {
            const campos = this.container.querySelectorAll('input[data-bs-toggle="tooltip"]');
            campos.forEach(campo => {
                tooltipManager.hideTooltip(campo);
            });
        }
        // Remover avisos antigos (compatibilidade)
        const avisosAnteriores = document.querySelectorAll('.aviso-conflito');
        avisosAnteriores.forEach(aviso => aviso.remove());
    }
    // Função auxiliar para converter data DD/MM/YYYY para YYYY-MM-DD
    converterDataParaISO(dataBrasileira) {
        if (!dataBrasileira)
            return null;
        const partes = dataBrasileira.split('/');
        if (partes.length === 3) {
            return `${partes[2]}-${partes[1].padStart(2, '0')}-${partes[0].padStart(2, '0')}`;
        }
        return null;
    }
}
// Exportar para uso global
// Remover esta linha:
// window.ModalReserva = ModalReserva;
export { ModalReserva };
