<?php

session_start();

require_once 'config.php';



// Função para atualizar o logo no config.php

function updateLogoInConfig($newLogoPath) {

    $configFile = 'config.php';

    

    if (!file_exists($configFile)) {

        return false;

    }

    

    $content = file_get_contents($configFile);

    

    // Verifica se já existe a definição LOGO_PATH

    if (strpos($content, "define('LOGO_PATH'") !== false) {

        // Substitui a linha existente

        $pattern = "/define\('LOGO_PATH',\s*'[^']*'\);/";

        $replacement = "define('LOGO_PATH', '{$newLogoPath}');";

        $newContent = preg_replace($pattern, $replacement, $content);

    } else {

        // Adiciona a nova definição antes do fechamento

        $newLine = "\n// Caminho do logo\ndefine('LOGO_PATH', '{$newLogoPath}');\n";

        $newContent = str_replace('?>', $newLine . '?>', $content);

    }

    

    return file_put_contents($configFile, $newContent) !== false;

}



// Função para fazer upload da imagem

function uploadImage($file) {

    $uploadDir = 'img/';

    

    // Cria o diretório se não existir

    if (!is_dir($uploadDir)) {

        mkdir($uploadDir, 0755, true);

    }

    

    // Validações

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    $maxSize = 5 * 1024 * 1024; // 5MB

    

    if (!in_array($file['type'], $allowedTypes)) {

        throw new Exception('Tipo de arquivo não permitido. Use JPG, PNG, GIF ou WebP.');

    }

    

    if ($file['size'] > $maxSize) {

        throw new Exception('Arquivo muito grande. Máximo 5MB.');

    }

    

    // Gera nome único

    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);

    $filename = 'logo_' . time() . '.' . strtolower($extension);

    $targetPath = $uploadDir . $filename;

    

    if (move_uploaded_file($file['tmp_name'], $targetPath)) {

        return $targetPath;

    }

    

    return false;

}



// Obtém o logo atual 

$currentLogo = defined('LOGO_PATH') ? LOGO_PATH : 'img/Logo_Bom_Viver.png';



$message = '';

$messageType = '';



// Processa o upload

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo'])) {

    try {

        if ($_FILES['logo']['error'] === UPLOAD_ERR_OK) {

            $newLogoPath = uploadImage($_FILES['logo']);

            

            if ($newLogoPath && updateLogoInConfig($newLogoPath)) {

                $currentLogo = $newLogoPath;

                $message = 'Logo atualizado com sucesso!';

                $messageType = 'success';

            } else {

                $message = 'Erro ao salvar o logo.';

                $messageType = 'danger';

            }

        } else {

            $message = 'Erro no upload do arquivo.';

            $messageType = 'danger';

        }

    } catch (Exception $e) {

        $message = $e->getMessage();

        $messageType = 'danger';

    }

}

?>



<!DOCTYPE html>

<html lang="pt-BR">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Gerenciar Logo</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>

        .logo-container {

            border: 2px dashed #dee2e6;

            border-radius: 8px;

            padding: 20px;

            text-align: center;

            background-color: #f8f9fa;

        }

        

        .logo-current, .logo-preview {

            max-width: 100%;

            max-height: 200px;

            border: 1px solid #dee2e6;

            border-radius: 4px;

            background-color: white;

            padding: 10px;

        }

        

        .preview-container {

            min-height: 220px;

            display: flex;

            align-items: center;

            justify-content: center;

        }

        

        .file-input-wrapper {

            position: relative;

            overflow: hidden;

            display: inline-block;

        }

        

        .file-input-wrapper input[type=file] {

            position: absolute;

            left: -9999px;

        }

        

        .file-input-label {

            cursor: pointer;

            display: inline-block;

            padding: 8px 16px;

            background-color: #0d6efd;

            color: white;

            border-radius: 4px;

            transition: background-color 0.2s;

        }

        

        .file-input-label:hover {

            background-color: #0b5ed7;

        }

    </style>

</head>

<body>

    <div class="container mt-5">

        <div class="row justify-content-center">

            <div class="col-md-10">

                <div class="card shadow">

                    <div class="card-header bg-primary text-white">

                        <h4 class="mb-0">

                            <i class="fas fa-image me-2"></i>

                            Gerenciar Logo do Sistema

                        </h4>

                    </div>

                    

                    <div class="card-body">

                        <?php if ($message): ?>

                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">

                                <?php echo htmlspecialchars($message); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>

                            </div>

                        <?php endif; ?>

                        

                        <div class="row">

                            <!-- Logo Atual -->

                            <div class="col-md-6">

                                <div class="logo-container">

                                    <h5 class="mb-3">Logo Atual</h5>

                                    <div class="preview-container">

                                        <?php if (file_exists($currentLogo)): ?>

                                            <img src="<?php echo htmlspecialchars($currentLogo); ?>?v=<?php echo time(); ?>" 

                                                 alt="Logo atual" class="logo-current">

                                        <?php else: ?>

                                            <div class="text-muted">

                                                <i class="fas fa-image fa-3x mb-2"></i>

                                                <p>Logo não encontrado</p>

                                                <small><?php echo htmlspecialchars($currentLogo); ?></small>

                                            </div>

                                        <?php endif; ?>

                                    </div>

                                    <small class="text-muted">

                                        Caminho: <?php echo htmlspecialchars($currentLogo); ?>

                                    </small>

                                </div>

                            </div>

                            

                            <!-- Preview da Nova Imagem -->

                            <div class="col-md-6">

                                <div class="logo-container">

                                    <h5 class="mb-3">Preview da Nova Imagem</h5>

                                    <div class="preview-container">

                                        <div id="preview-area" class="text-muted">

                                            <i class="fas fa-cloud-upload-alt fa-3x mb-2"></i>

                                            <p>Selecione uma imagem para ver o preview</p>

                                        </div>

                                    </div>

                                </div>

                            </div>

                        </div>

                        

                        <hr class="my-4">

                        

                        <!-- Formulário de Upload -->

                        <form method="POST" enctype="multipart/form-data" id="uploadForm">

                            <div class="row align-items-end">

                                <div class="col-md-8">

                                    <label for="logo" class="form-label">Selecionar Nova Imagem</label>

                                    <div class="file-input-wrapper">

                                        <input type="file" id="logo" name="logo" accept="image/*" required>

                                        <label for="logo" class="file-input-label">

                                            <i class="fas fa-folder-open me-2"></i>

                                            Escolher Arquivo

                                        </label>

                                    </div>

                                    <div class="form-text">

                                        Formatos aceitos: JPG, PNG, GIF, WebP (máximo 5MB)

                                    </div>

                                    <div id="file-name" class="text-muted small mt-1"></div>

                                </div>

                                <div class="col-md-4">

                                    <button type="submit" class="btn btn-success w-100" id="uploadBtn" disabled>

                                        <i class="fas fa-upload me-2"></i>

                                        Atualizar Logo

                                    </button>

                                </div>

                            </div>

                        </form>

                    </div>

                </div>

            </div>

        </div>

    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <script>

        document.getElementById('logo').addEventListener('change', function(e) {

            const file = e.target.files[0];

            const previewArea = document.getElementById('preview-area');

            const fileName = document.getElementById('file-name');

            const uploadBtn = document.getElementById('uploadBtn');

            

            if (file) {

                // Validação do tipo de arquivo

                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

                if (!allowedTypes.includes(file.type)) {

                    alert('Tipo de arquivo não permitido. Use JPG, PNG, GIF ou WebP.');

                    e.target.value = '';

                    return;

                }

                

                // Validação do tamanho

                if (file.size > 5 * 1024 * 1024) {

                    alert('Arquivo muito grande. Máximo 5MB.');

                    e.target.value = '';

                    return;

                }

                

                // Cria preview

                const reader = new FileReader();

                reader.onload = function(e) {

                    previewArea.innerHTML = `<img src="${e.target.result}" alt="Preview" class="logo-preview">`;

                };

                reader.readAsDataURL(file);

                

                // Mostra nome do arquivo

                fileName.textContent = `Arquivo selecionado: ${file.name}`;

                

                // Habilita botão

                uploadBtn.disabled = false;

            } else {

                previewArea.innerHTML = `

                    <div class="text-muted">

                        <i class="fas fa-cloud-upload-alt fa-3x mb-2"></i>

                        <p>Selecione uma imagem para ver o preview</p>

                    </div>

                `;

                fileName.textContent = '';

                uploadBtn.disabled = true;

            }

        });

        

        // Feedback visual no upload

        document.getElementById('uploadForm').addEventListener('submit', function() {

            const btn = document.getElementById('uploadBtn');

            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';

            btn.disabled = true;

        });

    </script>

</body>

</html>