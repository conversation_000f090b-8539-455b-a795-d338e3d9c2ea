<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

// Definir período padrão (mês atual)
$data_inicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : date('Y-m-01');
$data_fim = isset($_GET['data_fim']) ? $_GET['data_fim'] : date('Y-m-t');

// Verificar se há um caixa aberto
$sql_caixa = "SELECT id, data_abertura, saldo_inicial FROM caixa_diario 
              WHERE pousada_id = $pousada_id AND status = 'aberto'";
$result_caixa = $conn->query($sql_caixa);
$caixa_aberto = $result_caixa->num_rows > 0 ? $result_caixa->fetch_assoc() : null;

// Obter resumo financeiro do período
$sql_resumo = "SELECT 
               SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) as total_receitas,
               SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as total_despesas,
               SUM(CASE WHEN tipo = 'receita' AND status = 'pendente' THEN valor ELSE 0 END) as total_receitas_pendentes,
               SUM(CASE WHEN tipo = 'despesa' AND status = 'pendente' THEN valor ELSE 0 END) as total_despesas_pendentes
               FROM lancamentos_financeiros
               WHERE pousada_id = $pousada_id 
               AND data_vencimento BETWEEN '$data_inicio' AND '$data_fim'";
$result_resumo = $conn->query($sql_resumo);
$resumo = $result_resumo->fetch_assoc();

$total_receitas = $resumo['total_receitas'] ?? 0;
$total_despesas = $resumo['total_despesas'] ?? 0;
$total_receitas_pendentes = $resumo['total_receitas_pendentes'] ?? 0;
$total_despesas_pendentes = $resumo['total_despesas_pendentes'] ?? 0;
$saldo = $total_receitas - $total_despesas;

// Obter receitas por categoria
$sql_receitas_categorias = "SELECT cf.nome, cf.cor, SUM(lf.valor) as total
                           FROM lancamentos_financeiros lf
                           JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
                           WHERE lf.pousada_id = $pousada_id 
                           AND lf.tipo = 'receita'
                           AND lf.status = 'pago'
                           AND lf.data_vencimento BETWEEN '$data_inicio' AND '$data_fim'
                           GROUP BY lf.categoria_id
                           ORDER BY total DESC";
$result_receitas_categorias = $conn->query($sql_receitas_categorias);

// Obter despesas por categoria
$sql_despesas_categorias = "SELECT cf.nome, cf.cor, SUM(lf.valor) as total
                           FROM lancamentos_financeiros lf
                           JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
                           WHERE lf.pousada_id = $pousada_id 
                           AND lf.tipo = 'despesa'
                           AND lf.status = 'pago'
                           AND lf.data_vencimento BETWEEN '$data_inicio' AND '$data_fim'
                           GROUP BY lf.categoria_id
                           ORDER BY total DESC";
$result_despesas_categorias = $conn->query($sql_despesas_categorias);

// Obter contas a vencer nos próximos 7 dias
$hoje = date('Y-m-d');
$proxima_semana = date('Y-m-d', strtotime('+7 days'));

$sql_contas_vencer = "SELECT lf.*, cf.nome as categoria_nome, cf.cor as categoria_cor
                     FROM lancamentos_financeiros lf
                     JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
                     WHERE lf.pousada_id = $pousada_id 
                     AND lf.status = 'pendente'
                     AND lf.data_vencimento BETWEEN '$hoje' AND '$proxima_semana'
                     ORDER BY lf.data_vencimento ASC";
$result_contas_vencer = $conn->query($sql_contas_vencer);

// Obter contas vencidas
$sql_contas_vencidas = "SELECT lf.*, cf.nome as categoria_nome, cf.cor as categoria_cor
                       FROM lancamentos_financeiros lf
                       JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
                       WHERE lf.pousada_id = $pousada_id 
                       AND lf.status = 'pendente'
                       AND lf.data_vencimento < '$hoje'
                       ORDER BY lf.data_vencimento ASC";
$result_contas_vencidas = $conn->query($sql_contas_vencidas);

// Obter fluxo de caixa diário do período
$sql_fluxo_diario = "SELECT 
                     DATE(data_vencimento) as data,
                     SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) as receitas,
                     SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as despesas
                     FROM lancamentos_financeiros
                     WHERE pousada_id = $pousada_id 
                     AND data_vencimento BETWEEN '$data_inicio' AND '$data_fim'
                     GROUP BY DATE(data_vencimento)
                     ORDER BY data_vencimento ASC";
$result_fluxo_diario = $conn->query($sql_fluxo_diario);

// Preparar dados para o gráfico de fluxo de caixa
$datas = [];
$receitas = [];
$despesas = [];
$saldos = [];

$saldo_acumulado = 0;
while ($row = $result_fluxo_diario->fetch_assoc()) {
    $datas[] = date('d/m', strtotime($row['data']));
    $receitas[] = floatval($row['receitas']);
    $despesas[] = floatval($row['despesas']);
    $saldo_diario = $row['receitas'] - $row['despesas'];
    $saldo_acumulado += $saldo_diario;
    $saldos[] = $saldo_acumulado;
}

// Converter arrays para formato JSON para uso nos gráficos
$datas_json = json_encode($datas);
$receitas_json = json_encode($receitas);
$despesas_json = json_encode($despesas);
$saldos_json = json_encode($saldos);

// Preparar dados para o gráfico de categorias de receitas
$categorias_receitas = [];
$valores_receitas = [];
$cores_receitas = [];

if ($result_receitas_categorias->num_rows > 0) {
    $result_receitas_categorias->data_seek(0);
    while ($row = $result_receitas_categorias->fetch_assoc()) {
        $categorias_receitas[] = $row['nome'];
        $valores_receitas[] = floatval($row['total']);
        $cores_receitas[] = $row['cor'];
    }
}

// Preparar dados para o gráfico de categorias de despesas
$categorias_despesas = [];
$valores_despesas = [];
$cores_despesas = [];

if ($result_despesas_categorias->num_rows > 0) {
    $result_despesas_categorias->data_seek(0);
    while ($row = $result_despesas_categorias->fetch_assoc()) {
        $categorias_despesas[] = $row['nome'];
        $valores_despesas[] = floatval($row['total']);
        $cores_despesas[] = $row['cor'];
    }
}

$categorias_receitas_json = json_encode($categorias_receitas);
$valores_receitas_json = json_encode($valores_receitas);
$cores_receitas_json = json_encode($cores_receitas);

$categorias_despesas_json = json_encode($categorias_despesas);
$valores_despesas_json = json_encode($valores_despesas);
$cores_despesas_json = json_encode($cores_despesas);
?>


<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2 class="mb-3">Dashboard Financeiro</h2>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-lightbulb-fill me-2" style="font-size: 1.2em;"></i>
                <div>
                    <strong>Aqui você:</strong> Visão geral das finanças com resumo de receitas, despesas, saldo e gráficos para análise rápida da situação financeira.
                </div>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <?php if ($caixa_aberto): ?>
                <a href="index.php?page=caixa_diario_detalhes&id=<?= $caixa_aberto['id'] ?>" class="btn btn-success me-2">
                    <i class="bi bi-cash-coin"></i> Caixa Aberto
                </a>
            <?php else: ?>
                <a href="index.php?page=caixa_diario" class="btn btn-outline-success me-2">
                    <i class="bi bi-cash-coin"></i> Abrir Caixa
                </a>
            <?php endif; ?>
            <a href="index.php?page=contas" class="btn btn-primary">
                <i class="bi bi-currency-dollar"></i> Gerenciar Finanças
            </a>
        </div>
    </div>
    
    <!-- Filtro de Período -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-calendar-range"></i> Período de Análise
        </div>
        <div class="card-body">
            <form method="GET" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="dashboard_financeiro">
                
                <div class="col-md-4">
                    <label for="data_inicio" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?= $data_inicio ?>">
                </div>
                
                <div class="col-md-4">
                    <label for="data_fim" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?= $data_fim ?>">
                </div>
                
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> Filtrar
                    </button>
                    <a href="index.php?page=dashboard_financeiro" class="btn btn-secondary">
                        <i class="bi bi-arrow-repeat"></i> Resetar
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Resumo Financeiro -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">Receitas</h5>
                    <h3 class="card-text">R$ <?= number_format($total_receitas, 2, ',', '.') ?></h3>
                    <small>Pendente: R$ <?= number_format($total_receitas_pendentes, 2, ',', '.') ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">Despesas</h5>
                    <h3 class="card-text">R$ <?= number_format($total_despesas, 2, ',', '.') ?></h3>
                    <small>Pendente: R$ <?= number_format($total_despesas_pendentes, 2, ',', '.') ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card <?= $saldo >= 0 ? 'bg-primary' : 'bg-warning text-dark' ?> text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">Saldo</h5>
                    <h3 class="card-text">R$ <?= number_format($saldo, 2, ',', '.') ?></h3>
                    <small>Receitas - Despesas</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <h5 class="card-title">Período</h5>
                    <p class="card-text"><?= date('d/m/Y', strtotime($data_inicio)) ?> a <?= date('d/m/Y', strtotime($data_fim)) ?></p>
                    <small><?= ceil((strtotime($data_fim) - strtotime($data_inicio)) / (60 * 60 * 24)) + 1 ?> dias</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Gráficos -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card h-100">
                <div class="card-header">
                    <i class="bi bi-graph-up"></i> Fluxo de Caixa
                </div>
                <div class="card-body">
                    <canvas id="fluxoCaixaChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header">
                    <i class="bi bi-pie-chart"></i> Distribuição de Receitas
                </div>
                <div class="card-body">
                    <?php if (count($categorias_receitas) > 0): ?>
                        <canvas id="receitasChart"></canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há dados de receitas para exibir no período selecionado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-header">
                    <i class="bi bi-pie-chart"></i> Distribuição de Despesas
                </div>
                <div class="card-body">
                    <?php if (count($categorias_despesas) > 0): ?>
                        <canvas id="despesasChart"></canvas>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há dados de despesas para exibir no período selecionado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <i class="bi bi-exclamation-triangle"></i> Contas a Vencer (Próximos 7 dias)
                </div>
                <div class="card-body">
                    <?php if ($result_contas_vencer && $result_contas_vencer->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Vencimento</th>
                                        <th>Descrição</th>
                                        <th>Categoria</th>
                                        <th>Valor</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($conta = $result_contas_vencer->fetch_assoc()): ?>
                                        <tr>
                                            <td><?= date('d/m/Y', strtotime($conta['data_vencimento'])) ?></td>
                                            <td><?= htmlspecialchars($conta['descricao']) ?></td>
                                            <td>
                                                <span class="badge" style="background-color: <?= $conta['categoria_cor'] ?>">
                                                    <?= htmlspecialchars($conta['categoria_nome']) ?>
                                                </span>
                                            </td>
                                            <td class="<?= $conta['tipo'] == 'receita' ? 'text-success' : 'text-danger' ?> fw-bold">
                                                R$ <?= number_format($conta['valor'], 2, ',', '.') ?>
                                            </td>
                                            <td>
                                                <a href="index.php?page=contas&id=<?= $conta['id'] ?>" class="btn btn-sm btn-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há contas a vencer nos próximos 7 dias.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Contas Vencidas -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <i class="bi bi-exclamation-circle"></i> Contas Vencidas
        </div>
        <div class="card-body">
            <?php if ($result_contas_vencidas && $result_contas_vencidas->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Vencimento</th>
                                <th>Descrição</th>
                                <th>Categoria</th>
                                <th>Valor</th>
                                <th>Dias Vencidos</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($conta = $result_contas_vencidas->fetch_assoc()): 
                                $dias_vencidos = floor((strtotime($hoje) - strtotime($conta['data_vencimento'])) / (60 * 60 * 24));
                            ?>
                                <tr>
                                    <td><?= date('d/m/Y', strtotime($conta['data_vencimento'])) ?></td>
                                    <td><?= htmlspecialchars($conta['descricao']) ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?= $conta['categoria_cor'] ?>">
                                            <?= htmlspecialchars($conta['categoria_nome']) ?>
                                        </span>
                                    </td>
                                    <td class="<?= $conta['tipo'] == 'receita' ? 'text-success' : 'text-danger' ?> fw-bold">
                                        R$ <?= number_format($conta['valor'], 2, ',', '.') ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger"><?= $dias_vencidos ?> dias</span>
                                    </td>
                                    <td>
                                        <a href="index.php?page=contas&id=<?= $conta['id'] ?>" class="btn btn-sm btn-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    Não há contas vencidas. Parabéns!
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Scripts para os gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de Fluxo de Caixa
    const ctxFluxo = document.getElementById('fluxoCaixaChart');
    if (ctxFluxo) {
        new Chart(ctxFluxo, {
            type: 'line',
            data: {
                labels: <?= $datas_json ?>,
                datasets: [
                    {
                        label: 'Receitas',
                        data: <?= $receitas_json ?>,
                        backgroundColor: 'rgba(40, 167, 69, 0.2)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: 'Despesas',
                        data: <?= $despesas_json ?>,
                        backgroundColor: 'rgba(220, 53, 69, 0.2)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: 'Saldo Acumulado',
                        data: <?= $saldos_json ?>,
                        backgroundColor: 'rgba(0, 123, 255, 0.2)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': R$ ' + context.raw.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Gráfico de Receitas por Categoria
    const ctxReceitas = document.getElementById('receitasChart');
    if (ctxReceitas && <?= count($categorias_receitas) > 0 ? 'true' : 'false' ?>) {
        new Chart(ctxReceitas, {
            type: 'doughnut',
            data: {
                labels: <?= $categorias_receitas_json ?>,
                datasets: [{
                    data: <?= $valores_receitas_json ?>,
                    backgroundColor: <?= $cores_receitas_json ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Gráfico de Despesas por Categoria
    const ctxDespesas = document.getElementById('despesasChart');
    if (ctxDespesas && <?= count($categorias_despesas) > 0 ? 'true' : 'false' ?>) {
        new Chart(ctxDespesas, {
            type: 'doughnut',
            data: {
                labels: <?= $categorias_despesas_json ?>,
                datasets: [{
                    data: <?= $valores_despesas_json ?>,
                    backgroundColor: <?= $cores_despesas_json ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>