<?php
// Reportar todos os erros do PHP
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;

}

include_once("config.php");

// Verificar se o método de requisição é POST
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    $_SESSION['mensagem'] = "Método de requisição inválido.";
    $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
}

// Obter a ação a ser executada
$acao = $_POST['acao'] ?? '';

// Processar de acordo com a ação
switch ($acao) {
    case 'abrir':
        abrirCaixa($conn, $user_id, $pousada_id);
        break;
    case 'fechar':
        fecharCaixa($conn, $user_id, $pousada_id);
        break;
    case 'suprimento':
        registrarSuprimento($conn, $user_id, $pousada_id);
        break;
    case 'sangria':
        registrarSangria($conn, $user_id, $pousada_id);
        break;
    default:
        $_SESSION['mensagem'] = "Ação inválida.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
}

// Função para abrir um novo caixa
function abrirCaixa($conn, $user_id, $pousada_id) {
    // Verificar se já existe um caixa aberto
    $stmt = $conn->prepare("SELECT id FROM caixa_diario WHERE pousada_id = ? AND status = 'aberto'");
    $stmt->bind_param("i", $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $_SESSION['mensagem'] = "Já existe um caixa aberto. Feche o caixa atual antes de abrir um novo.";
        $_SESSION['tipo_mensagem'] = "warning";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Obter e validar os dados do formulário
    $saldo_inicial = isset($_POST['saldo_inicial']) ? str_replace(',', '.', $_POST['saldo_inicial']) : 0;
    $observacao = $_POST['observacao'] ?? '';
    
    // Validar saldo inicial
    if (!is_numeric($saldo_inicial) || $saldo_inicial < 0) {
        $_SESSION['mensagem'] = "O saldo inicial deve ser um número positivo.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Data e hora atual
    $data_abertura = date('Y-m-d H:i:s');
    
    // Inserir o novo caixa no banco de dados
    $stmt = $conn->prepare("INSERT INTO caixa_diario (pousada_id, data_abertura, saldo_inicial, usuario_abertura_id, status, observacao) VALUES (?, ?, ?, ?, 'aberto', ?)");
    $stmt->bind_param("isdis", $pousada_id, $data_abertura, $saldo_inicial, $user_id, $observacao);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Caixa aberto com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao abrir caixa: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    


    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
}

// Função para fechar um caixa
function fecharCaixa($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $caixa_id = isset($_POST['caixa_id']) ? intval($_POST['caixa_id']) : 0;
    $saldo_final = isset($_POST['saldo_final']) ? str_replace(',', '.', $_POST['saldo_final']) : 0;
    $observacao = $_POST['observacao'] ?? '';
    
    // Validar ID do caixa
    if ($caixa_id <= 0) {
        $_SESSION['mensagem'] = "ID do caixa inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;

    }
    
    // Validar saldo final
    if (!is_numeric($saldo_final) || $saldo_final < 0) {
        $_SESSION['mensagem'] = "O saldo final deve ser um número positivo.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Verificar se o caixa existe, está aberto e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM caixa_diario WHERE id = ? AND pousada_id = ? AND status = 'aberto'");
    $stmt->bind_param("ii", $caixa_id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Caixa não encontrado ou já está fechado.";
        $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
    }
    
    $caixa = $result->fetch_assoc();
    

//--------------------------
    // Calcular saldo atual baseado nas movimentações
    $stmt = $conn->prepare("SELECT
                        SUM(CASE WHEN tipo IN ('entrada', 'suprimento') THEN valor ELSE 0 END) as entradas,
                        SUM(CASE WHEN tipo IN ('saida', 'sangria') THEN valor ELSE 0 END) as saidas
                        FROM movimentacoes_caixa
                        WHERE caixa_id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $caixa_id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $movimentacoes = $result->fetch_assoc();

    $entradas = $movimentacoes['entradas'] ?? 0;
    $saidas = $movimentacoes['saidas'] ?? 0;

    // O saldo calculado é o saldo inicial mais entradas menos saídas
    $saldo_calculado = $caixa['saldo_inicial'] + $entradas - $saidas;

//--------------------------
    // Calcular a diferença entre o saldo calculado e o informado
    $diferenca = $saldo_final - $saldo_calculado;
    
    // Se houver diferença, registrar como suprimento ou sangria
    if (abs($diferenca) > 0.01) { // Tolerância de 1 centavo para evitar problemas de arredondamento
        $tipo = $diferenca > 0 ? 'suprimento' : 'sangria';
        $valor = abs($diferenca);
        $descricao = "Ajuste de fechamento de caixa";
        
        $stmt = $conn->prepare("INSERT INTO movimentacoes_caixa
                               (caixa_id, pousada_id, tipo, valor, descricao, data_hora, usuario_id)
                               VALUES (?, ?, ?, ?, ?, NOW(), ?)");
        $stmt->bind_param("iisdsi", $caixa_id, $pousada_id, $tipo, $valor, $descricao, $user_id);
        $stmt->execute();
    }
    
    // Atualizar o caixa para fechado
    $data_fechamento = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("UPDATE caixa_diario 
                           SET status = 'fechado', 
                               data_fechamento = ?, 
                               usuario_fechamento_id = ?, 
                               saldo_final = ?, 
                               observacao = CONCAT(observacao, '\n\nObservações de fechamento: ', ?) 
                           WHERE id = ?");
    $stmt->bind_param("sidsi", $data_fechamento, $user_id, $saldo_final, $observacao, $caixa_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Caixa fechado com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao fechar caixa: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
}

// Função para registrar um suprimento
function registrarSuprimento($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $caixa_id = isset($_POST['caixa_id']) ? intval($_POST['caixa_id']) : 0;
    $valor = isset($_POST['valor']) ? str_replace(',', '.', $_POST['valor']) : 0;
    $descricao = $_POST['descricao'] ?? '';
    
    // Validar ID do caixa
    if ($caixa_id <= 0) {
        $_SESSION['mensagem'] = "ID do caixa inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Validar valor
    if (!is_numeric($valor) || $valor <= 0) {
        $_SESSION['mensagem'] = "O valor deve ser um número positivo.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Validar descrição
    if (empty($descricao)) {
        $_SESSION['mensagem'] = "A descrição é obrigatória.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Verificar se o caixa existe, está aberto e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM caixa_diario WHERE id = ? AND pousada_id = ? AND status = 'aberto'");
    $stmt->bind_param("ii", $caixa_id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Caixa não encontrado ou já está fechado.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Registrar o suprimento
    $data_hora = date('Y-m-d H:i:s');
    $tipo = 'suprimento';
    
    $stmt = $conn->prepare("INSERT INTO movimentacoes_caixa
                           (caixa_id, pousada_id, tipo, valor, descricao, data_hora, usuario_id)
                           VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("iisdssi", $caixa_id, $pousada_id, $tipo, $valor, $descricao, $data_hora, $user_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Suprimento registrado com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao registrar suprimento: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
}

// Função para registrar uma sangria
function registrarSangria($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $caixa_id = isset($_POST['caixa_id']) ? intval($_POST['caixa_id']) : 0;
    $valor = isset($_POST['valor']) ? str_replace(',', '.', $_POST['valor']) : 0;
    $descricao = $_POST['descricao'] ?? '';
    
    // Validar ID do caixa
    if ($caixa_id <= 0) {
        $_SESSION['mensagem'] = "ID do caixa inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Validar valor
    if (!is_numeric($valor) || $valor <= 0) {
        $_SESSION['mensagem'] = "O valor deve ser um número positivo.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Validar descrição
    if (empty($descricao)) {
        $_SESSION['mensagem'] = "A descrição é obrigatória.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Verificar se o caixa existe, está aberto e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM caixa_diario WHERE id = ? AND pousada_id = ? AND status = 'aberto'");
    $stmt->bind_param("ii", $caixa_id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Caixa não encontrado ou já está fechado.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Verificar se há saldo suficiente no caixa
    $caixa = $result->fetch_assoc();
    
    // Calcular saldo atual
    $stmt = $conn->prepare("SELECT 
                           SUM(CASE WHEN tipo IN ('entrada', 'suprimento') THEN valor ELSE 0 END) as entradas,
                           SUM(CASE WHEN tipo IN ('saida', 'sangria') THEN valor ELSE 0 END) as saidas
                           FROM movimentacoes_caixa 
                           WHERE caixa_id = ?");
    $stmt->bind_param("i", $caixa_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $movimentacoes = $result->fetch_assoc();
    
    $entradas = $movimentacoes['entradas'] ?? 0;
    $saidas = $movimentacoes['saidas'] ?? 0;
    
    $saldo_atual = $caixa['saldo_inicial'] + $entradas - $saidas;
    
    if ($saldo_atual < $valor) {
        $_SESSION['mensagem'] = "Saldo insuficiente para realizar esta sangria. Saldo atual: R$ " . number_format($saldo_atual, 2, ',', '.');
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
        return;
    }
    
    // Registrar a sangria
    $data_hora = date('Y-m-d H:i:s');
    $tipo = 'sangria';
    $stmt = $conn->prepare("INSERT INTO movimentacoes_caixa (caixa_id, pousada_id, tipo, valor, descricao, data_hora, usuario_id) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("iisdssi", $caixa_id, $pousada_id, $tipo, $valor, $descricao, $data_hora, $user_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Sangria registrada com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao registrar sangria: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
}

// Função para registrar movimentação no caixa a partir de um lançamento financeiro
function registrarMovimentacaoCaixa($conn, $lancamento_id, $tipo, $valor, $descricao, $user_id, $pousada_id) {
    // Verificar se existe um caixa aberto
    $stmt = $conn->prepare("SELECT id FROM caixa_diario WHERE pousada_id = ? AND status = 'aberto'");
    $stmt->bind_param("i", $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        // Não há caixa aberto, não registra movimentação
        return false;
    }
    
    $caixa_id = $result->fetch_assoc()['id'];
    $tipo_movimentacao = ($tipo == 'receita') ? 'entrada' : 'saida';
    $data_hora = date('Y-m-d H:i:s');
    
    $stmt = $conn->prepare("INSERT INTO movimentacoes_caixa (caixa_id, pousada_id, lancamento_id, tipo, valor, descricao, data_hora, usuario_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("iiisdssi", $caixa_id, $pousada_id, $lancamento_id, $tipo_movimentacao, $valor, $descricao, $data_hora, $user_id);
    
    return $stmt->execute();
}
?>