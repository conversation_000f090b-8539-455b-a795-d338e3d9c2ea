<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se o ID foi fornecido
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id <= 0) {
    echo '<div class="alert alert-danger">ID da forma de pagamento inválido.</div>';
    exit();
}

// Buscar dados da forma de pagamento - VERIFICAR se inclui afeta_caixa
$stmt = $conn->prepare("SELECT id, nome, descricao, afeta_caixa, is_active FROM formas_pagamento WHERE id = ? AND pousada_id = ?");
$stmt->bind_param("ii", $id, $pousada_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo '<div class="alert alert-danger">Forma de pagamento não encontrada.</div>';
    exit();
}

$forma = $result->fetch_assoc();
?>

<input type="hidden" name="acao" value="editar">
<input type="hidden" name="id" value="<?php echo $forma['id']; ?>">

<div class="mb-3">
    <label for="edit_nome" class="form-label">Nome*</label>
    <input type="text" class="form-control" id="edit_nome" name="nome" 
           value="<?php echo htmlspecialchars($forma['nome']); ?>" required>
</div>

<div class="mb-3">
    <label for="edit_descricao" class="form-label">Descrição</label>
    <textarea class="form-control" id="edit_descricao" name="descricao" rows="3"><?php echo htmlspecialchars($forma['descricao'] ?? ''); ?></textarea>
</div>

<div class="mb-3 form-check">
    <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active" value="1" 
           <?php echo $forma['is_active'] ? 'checked' : ''; ?>>
    <label class="form-check-label" for="edit_is_active">Ativo</label>
</div>

<div class="mb-3 form-check">
    <input type="checkbox" class="form-check-input" id="edit_afeta_caixa" name="afeta_caixa" value="1" 
           <?php echo $forma['afeta_caixa'] ? 'checked' : ''; ?>>
    <label class="form-check-label" for="edit_afeta_caixa">Afeta Caixa</label>
    <small class="form-text text-muted">Marque se esta forma de pagamento movimenta o caixa físico</small>
</div>

<div class="alert alert-info">
    <i class="bi bi-info-circle"></i> Desativar uma forma de pagamento a tornará indisponível para novos lançamentos, mas manterá os registros existentes.
</div>