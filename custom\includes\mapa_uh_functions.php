<?php

/**
 * Função principal para determinar o status de ocupação de uma UH em uma data específica
 */
function isUhOcupada($uh, $data, $reservas) {
    $reservas_do_dia = [];

    foreach ($reservas as $reserva) {
        if ($reserva['uh'] == $uh) {
            $tipo = classificarTipoReserva($reserva, $data);
            if ($tipo) {
                $reservas_do_dia[] = [
                    'reserva' => $reserva,
                    'tipo' => $tipo
                ];
            }
        }
    }

    $resultado = processarReservasDodia($reservas_do_dia);

    // Adicionar contagem de reservas e todas as reservas do dia
    $resultado['total_reservas'] = count($reservas_do_dia);
    $resultado['todas_reservas'] = $reservas_do_dia;

    return $resultado;
}

/**
 * Classifica o tipo de uma reserva em relação a uma data específica
 */
function classificarTipoReserva($reserva, $data) {
    $dataEntrada = new DateTime($reserva['dataentrada']);
    $dataSaida = new DateTime($reserva['datasaida']);
    $dataAlvo = substr($data, 0, 10); // Garantir formato Y-m-d

    // Reserva atravessa o dia (entrada antes, saída depois)
    if ($dataEntrada->format('Y-m-d') < $dataAlvo && $dataSaida->format('Y-m-d') > $dataAlvo) {
        return 'permanencia';
    }

    // Checkout (saída no dia, entrada anterior)
    if ($dataSaida->format('Y-m-d') == $dataAlvo && $dataEntrada->format('Y-m-d') < $dataAlvo) {
        return 'checkout';
    }

    // Checkin (entrada no dia, saída posterior)
    if ($dataEntrada->format('Y-m-d') == $dataAlvo && $dataSaida->format('Y-m-d') > $dataAlvo) {
        return 'checkin';
    }

    // Diária (entrada e saída no mesmo dia)
    if ($dataEntrada->format('Y-m-d') == $dataAlvo && $dataSaida->format('Y-m-d') == $dataAlvo) {
        return 'diaria';
    }

    return null;
}

/**
 * Processa as reservas do dia e determina o status final da UH
 */
function processarReservasDodia($reservas_do_dia) {
    if (empty($reservas_do_dia)) {
        return ['status' => 'empty', 'reserva' => null];
    }

    $tipos = array_column($reservas_do_dia, 'tipo');

    // Se há permanência, UH ocupada o dia todo
    if (in_array('permanencia', $tipos)) {
        $reserva_permanencia = array_filter($reservas_do_dia, fn($r) => $r['tipo'] === 'permanencia');
        $reserva_permanencia = reset($reserva_permanencia);
        return ['status' => 'full', 'reserva' => $reserva_permanencia['reserva']];
    }

    // Separar por tipos
    $checkouts = array_filter($reservas_do_dia, fn($r) => $r['tipo'] === 'checkout');
    $checkins = array_filter($reservas_do_dia, fn($r) => $r['tipo'] === 'checkin');
    $diarias = array_filter($reservas_do_dia, fn($r) => $r['tipo'] === 'diaria');

    // Determinar checkout e checkin finais
    $reserva_checkout = determinarReservaCheckout($checkouts, $diarias);
    $reserva_checkin = determinarReservaCheckin($checkins, $diarias);

    // Lógica de retorno baseada no que foi encontrado
    if ($reserva_checkout && $reserva_checkin) {
        return [
            'status' => 'double_occupied',
            'reserva_checkout' => $reserva_checkout,
            'reserva_checkin' => $reserva_checkin
        ];
    } elseif ($reserva_checkout) {
        return ['status' => 'morning', 'reserva' => $reserva_checkout];
    } elseif ($reserva_checkin) {
        return ['status' => 'afternoon', 'reserva' => $reserva_checkin];
    }

    // Se só há diárias, tratar como ocupação baseada no horário
    if (!empty($diarias)) {
        $diaria = reset($diarias);
        $horaEntrada = new DateTime($diaria['reserva']['dataentrada'] . ' ' . $diaria['reserva']['horaentrada']);

        if ($horaEntrada->format('H') < 12) {
            return ['status' => 'morning', 'reserva' => $diaria['reserva']];
        } else {
            return ['status' => 'afternoon', 'reserva' => $diaria['reserva']];
        }
    }

    return ['status' => 'empty', 'reserva' => null];
}

/**
 * Determina qual reserva representa o checkout do dia
 */
function determinarReservaCheckout($checkouts, $diarias) {
    // Priorizar checkouts reais
    if (!empty($checkouts)) {
        $checkout = reset($checkouts);
        return $checkout['reserva'];
    }

    // Se não há checkout real, verificar diárias matinais
    foreach ($diarias as $diaria) {
        $horaEntrada = new DateTime($diaria['reserva']['dataentrada'] . ' ' . $diaria['reserva']['horaentrada']);
        if ($horaEntrada->format('H') < 12) {
            return $diaria['reserva'];
        }
    }

    return null;
}

/**
 * Determina qual reserva representa o checkin do dia
 */
function determinarReservaCheckin($checkins, $diarias) {
    // Priorizar checkins reais
    if (!empty($checkins)) {
        $checkin = reset($checkins);
        return $checkin['reserva'];
    }

    // Se não há checkin real, verificar diárias vespertinas
    foreach ($diarias as $diaria) {
        $horaEntrada = new DateTime($diaria['reserva']['dataentrada'] . ' ' . $diaria['reserva']['horaentrada']);
        if ($horaEntrada->format('H') >= 12) {
            return $diaria['reserva'];
        }
    }

    return null;
}

?>