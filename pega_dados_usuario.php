<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");
include_once("func.php");

// Verificar se o usuário está logado e é administrador
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Não autorizado']);
    exit();
}

$isAdmin = isAdmin($conn, $_SESSION['user_id']);
if (!$isAdmin) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Acesso negado']);
    exit();
}

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'ID não fornecido']);
    exit();
}

$id = intval($_GET['id']);

// Buscar dados do usuário
$stmt = $conn->prepare("SELECT id, nome, email, is_admin FROM usuarios WHERE id = ?");
$stmt->bind_param('i', $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não encontrado']);
    exit();
}

$usuario = $result->fetch_assoc();

//RETIRE, APENAS TEMPORÁRIO
// Debug: Verificar os dados antes de retornar
error_log("Dados do usuário retornados: " . print_r($usuario, true));
//FINAL DO TEMPORÁRIO

// Retornar dados em formato JSON
header('Content-Type: application/json');
echo json_encode($usuario);
