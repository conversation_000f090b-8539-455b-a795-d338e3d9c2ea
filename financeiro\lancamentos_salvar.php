<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";        
    return;
}

include_once("config.php");

// Função para validar e sanitizar os dados
function sanitizarDados($dados) {
    $dados = trim($dados);
    $dados = stripslashes($dados);
    $dados = htmlspecialchars($dados);
    return $dados;
}

// Verificar se foi enviado um formulário
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $acao = $_POST["acao"] ?? "";
    
    // Cadastrar novo lançamento
    if ($acao == "cadastrar") {
        $tipo = sanitizarDados($_POST["tipo"] ?? "");
        $categoria_id = intval($_POST["categoria_id"] ?? 0);
        $descricao = sanitizarDados($_POST["descricao"] ?? "");
        $valor = floatval(str_replace(',', '.', $_POST["valor"] ?? 0));
        $data_lancamento = date('Y-m-d'); // Data atual
        $data_vencimento = sanitizarDados($_POST["data_vencimento"] ?? "");
        $status = sanitizarDados($_POST["status"] ?? "pendente");
        $observacao = sanitizarDados($_POST["observacao"] ?? "");
        $reserva_id = intval($_POST["reserva_id"] ?? 0);
        
        // Campos opcionais para status "pago"
        $data_pagamento = null;
        $forma_pagamento = null;
        
        if ($status == "pago") {
            $data_pagamento = sanitizarDados($_POST["data_pagamento"] ?? "");
            $forma_pagamento = sanitizarDados($_POST["forma_pagamento"] ?? "");
        }
        
        // Validar dados
        if (empty($tipo) || empty($descricao) || $valor <= 0 || empty($data_vencimento) || $categoria_id <= 0) {
            $_SESSION['mensagem'] = "Erro: Todos os campos obrigatórios devem ser preenchidos corretamente.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        // Verificar se a categoria pertence à pousada e tem o tipo correto
        $stmt = $conn->prepare("SELECT id, tipo FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $categoria_id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Erro: Categoria inválida.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        $categoria = $result->fetch_assoc();
        if ($categoria['tipo'] != $tipo) {
            $_SESSION['mensagem'] = "Erro: A categoria selecionada não é compatível com o tipo de lançamento.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        // Verificar se a reserva existe e pertence à pousada (se informada)
        if ($reserva_id > 0) {
            $stmt = $conn->prepare("SELECT id FROM reservas WHERE id = ? AND pousada_id = ?");
            $stmt->bind_param("ii", $reserva_id, $pousada_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows == 0) {
                $_SESSION['mensagem'] = "Erro: Reserva inválida.";
                echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";            
                return;
            }
        } else {
            $reserva_id = null;
        }
        
        // Inserir novo lançamento
        $stmt = $conn->prepare("INSERT INTO lancamentos_financeiros 
                               (pousada_id, reserva_id, tipo, categoria_id, descricao, valor, 
                                data_lancamento, data_vencimento, data_pagamento, status, 
                                forma_pagamento, observacao, usuario_id) 
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $stmt->bind_param("iisisdssssssi", 
                         $pousada_id, $reserva_id, $tipo, $categoria_id, $descricao, $valor, 
                         $data_lancamento, $data_vencimento, $data_pagamento, $status, 
                         $forma_pagamento, $observacao, $user_id);
        
        if ($stmt->execute()) {
            $lancamento_id = $conn->insert_id;
            
            // Se o status for "pago", registrar a movimentação no caixa (se houver caixa aberto)
            if ($status == "pago") {
                // Verificar se existe um caixa aberto
                $stmt_caixa = $conn->prepare("SELECT id FROM caixa_diario 
                                             WHERE pousada_id = ? AND status = 'aberto' 
                                             ORDER BY data_abertura DESC LIMIT 1");
                $stmt_caixa->bind_param("i", $pousada_id);
                $stmt_caixa->execute();
                $result_caixa = $stmt_caixa->get_result();
                
                if ($result_caixa->num_rows > 0) {
                    $caixa = $result_caixa->fetch_assoc();
                    $caixa_id = $caixa['id'];
                    
                    // Determinar o tipo de movimentação com base no tipo de lançamento
                    $tipo_movimentacao = ($tipo == 'receita') ? 'entrada' : 'saida';
                    
                    // Registrar a movimentação no caixa
                    $stmt_mov = $conn->prepare("INSERT INTO movimentacoes_caixa
                                              (caixa_id, pousada_id, lancamento_id, tipo, valor, descricao, data_hora, usuario_id)
                                              VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)");
                    $stmt_mov->bind_param("iiisdsi",
                                        $caixa_id, $pousada_id, $lancamento_id, $tipo_movimentacao, $valor,
                                        $descricao, $user_id);
                    $stmt_mov->execute();
                }
            }
            
            $_SESSION['mensagem'] = "Lançamento cadastrado com sucesso!";
        } else {
            $_SESSION['mensagem'] = "Erro ao cadastrar lançamento: " . $conn->error;
        }
        
        echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";    
        return;
    }
    
    // Editar lançamento existente
    elseif ($acao == "editar") {
        $id = intval($_POST["id"] ?? 0);
        $categoria_id = intval($_POST["categoria_id"] ?? 0);
        $descricao = sanitizarDados($_POST["descricao"] ?? "");
        $valor = floatval(str_replace(',', '.', $_POST["valor"] ?? 0));
        $data_vencimento = sanitizarDados($_POST["data_vencimento"] ?? "");
        $status = sanitizarDados($_POST["status"] ?? "pendente");
        $observacao = sanitizarDados($_POST["observacao"] ?? "");
        
        // Campos opcionais para status "pago"
        $data_pagamento = null;
        $forma_pagamento = null;
        
        if ($status == "pago") {
            $data_pagamento = sanitizarDados($_POST["data_pagamento"] ?? "");
            $forma_pagamento = sanitizarDados($_POST["forma_pagamento"] ?? "");
        }
        
        // Validar dados
        if ($id <= 0 || empty($descricao) || $valor <= 0 || empty($data_vencimento) || $categoria_id <= 0) {
            $_SESSION['mensagem'] = "Erro: Todos os campos obrigatórios devem ser preenchidos corretamente.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        // Verificar se o lançamento existe e pertence à pousada
        $stmt = $conn->prepare("SELECT tipo, status FROM lancamentos_financeiros WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Erro: Lançamento não encontrado.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        $lancamento = $result->fetch_assoc();
        $tipo_original = $lancamento['tipo'];
        $status_original = $lancamento['status'];
        
        // Verificar se a categoria pertence à pousada e tem o tipo correto
        $stmt = $conn->prepare("SELECT id, tipo FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $categoria_id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Erro: Categoria inválida.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        $categoria = $result->fetch_assoc();
        if ($categoria['tipo'] != $tipo_original) {
            $_SESSION['mensagem'] = "Erro: A categoria selecionada não é compatível com o tipo de lançamento.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        // Atualizar lançamento
        $stmt = $conn->prepare("UPDATE lancamentos_financeiros 
                               SET categoria_id = ?, descricao = ?, valor = ?, 
                                   data_vencimento = ?, data_pagamento = ?, status = ?, 
                                   forma_pagamento = ?, observacao = ? 
                               WHERE id = ? AND pousada_id = ?");
        
        $stmt->bind_param("isdsssssii", 
                         $categoria_id, $descricao, $valor, 
                         $data_vencimento, $data_pagamento, $status, 
                         $forma_pagamento, $observacao, $id, $pousada_id);
        
        if ($stmt->execute()) {
            // Se o status mudou para "pago", registrar a movimentação no caixa (se houver caixa aberto)
            if ($status == "pago" && $status_original != "pago") {
                // Verificar se existe um caixa aberto
                $stmt_caixa = $conn->prepare("SELECT id FROM caixa_diario 
                                             WHERE pousada_id = ? AND status = 'aberto' 
                                             ORDER BY data_abertura DESC LIMIT 1");
                $stmt_caixa->bind_param("i", $pousada_id);
                $stmt_caixa->execute();
                $result_caixa = $stmt_caixa->get_result();
                
                if ($result_caixa->num_rows > 0) {
                    $caixa = $result_caixa->fetch_assoc();
                    $caixa_id = $caixa['id'];
                    
                    // Determinar o tipo de movimentação com base no tipo de lançamento
                    $tipo_movimentacao = ($tipo_original == 'receita') ? 'entrada' : 'saida';
                    
                    // Registrar a movimentação no caixa
                    $stmt_mov = $conn->prepare("INSERT INTO movimentacoes_caixa
                                              (caixa_id, pousada_id, lancamento_id, tipo, valor, descricao, data_hora, usuario_id)
                                              VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)");
                    $stmt_mov->bind_param("iiisdsi",
                                        $caixa_id, $pousada_id, $id, $tipo_movimentacao, $valor,
                                        $descricao, $user_id);
                    $stmt_mov->execute();
                }
            }
            
            $_SESSION['mensagem'] = "Lançamento atualizado com sucesso!";
        } else {
            $_SESSION['mensagem'] = "Erro ao atualizar lançamento: " . $conn->error;
        }
        
        echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";
    }
    
    // Registrar pagamento de um lançamento
    elseif ($acao == "pagar") {
        $id = intval($_POST["id"] ?? 0);
        $data_pagamento = sanitizarDados($_POST["data_pagamento"] ?? "");
        $forma_pagamento = sanitizarDados($_POST["forma_pagamento"] ?? "");
        $observacao = sanitizarDados($_POST["observacao"] ?? "");
        
        // Validar dados
        if ($id <= 0 || empty($data_pagamento) || empty($forma_pagamento)) {
            $_SESSION['mensagem'] = "Erro: Todos os campos obrigatórios devem ser preenchidos.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";       
            return;
        }
        
        // Verificar se o lançamento existe, pertence à pousada e está pendente
        $stmt = $conn->prepare("SELECT id, tipo, descricao, valor, observacao FROM lancamentos_financeiros 
                               WHERE id = ? AND pousada_id = ? AND status = 'pendente'");
        $stmt->bind_param("ii", $id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Erro: Lançamento não encontrado ou não está pendente.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        $lancamento = $result->fetch_assoc();
        $tipo = $lancamento['tipo'];
        $descricao = $lancamento['descricao'];
        $valor = $lancamento['valor'];
        
        // Atualizar observação se fornecida
        $observacao_final = $lancamento['observacao'];
        if (!empty($observacao)) {
            $observacao_final = empty($observacao_final) ? $observacao : $observacao_final . "\n" . $observacao;
        }
        
        // Atualizar lançamento para status "pago"
        $stmt = $conn->prepare("UPDATE lancamentos_financeiros 
                               SET status = 'pago', data_pagamento = ?, 
                                   forma_pagamento = ?, observacao = ? 
                               WHERE id = ? AND pousada_id = ?");
        
        $stmt->bind_param("sssii", 
                         $data_pagamento, $forma_pagamento, $observacao_final, 
                         $id, $pousada_id);
        
        if ($stmt->execute()) {
            // Verificar se existe um caixa aberto
            $stmt_caixa = $conn->prepare("SELECT id FROM caixa_diario 
                                         WHERE pousada_id = ? AND status = 'aberto' 
                                         ORDER BY data_abertura DESC LIMIT 1");
            $stmt_caixa->bind_param("i", $pousada_id);
            $stmt_caixa->execute();
            $result_caixa = $stmt_caixa->get_result();
            
            if ($result_caixa->num_rows > 0) {
                $caixa = $result_caixa->fetch_assoc();
                $caixa_id = $caixa['id'];
                
                // Determinar o tipo de movimentação com base no tipo de lançamento
                $tipo_movimentacao = ($tipo == 'receita') ? 'entrada' : 'saida';
                
                // Registrar a movimentação no caixa
                $stmt_mov = $conn->prepare("INSERT INTO movimentacoes_caixa 
                                          (caixa_id, lancamento_id, tipo, valor, descricao, data_hora, usuario_id) 
                                          VALUES (?, ?, ?, ?, ?, NOW(), ?)");
                $stmt_mov->bind_param("iisdsi", 
                                    $caixa_id, $id, $tipo_movimentacao, $valor, 
                                    $descricao, $user_id);
                $stmt_mov->execute();
            }
            
            $_SESSION['mensagem'] = "Pagamento registrado com sucesso!";
        } else {
            $_SESSION['mensagem'] = "Erro ao registrar pagamento: " . $conn->error;
        }
        
        echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";    
        return;
    }
    
    // Cancelar um lançamento
    elseif ($acao == "cancelar") {
        $id = intval($_POST["id"] ?? 0);
        
        // Validar dados
        if ($id <= 0) {
            $_SESSION['mensagem'] = "Erro: ID do lançamento inválido.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        // Verificar se o lançamento existe e pertence à pousada
        $stmt = $conn->prepare("SELECT id, status FROM lancamentos_financeiros 
                               WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Erro: Lançamento não encontrado.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        $lancamento = $result->fetch_assoc();
        
        // Verificar se o lançamento já está cancelado
        if ($lancamento['status'] == 'cancelado') {
            $_SESSION['mensagem'] = "Erro: Este lançamento já está cancelado.";
            echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";        
            return;
        }
        
        // Atualizar lançamento para status "cancelado"
        $stmt = $conn->prepare("UPDATE lancamentos_financeiros 
                               SET status = 'cancelado' 
                               WHERE id = ? AND pousada_id = ?");
        
        $stmt->bind_param("ii", $id, $pousada_id);
        
        if ($stmt->execute()) {
            // Se houver movimentações de caixa relacionadas a este lançamento, cancelá-las
            $stmt_mov = $conn->prepare("UPDATE movimentacoes_caixa 
                                       SET tipo = CASE 
                                                   WHEN tipo = 'entrada' THEN 'saida' 
                                                   WHEN tipo = 'saida' THEN 'entrada' 
                                                   ELSE tipo 
                                                 END, 
                                           descricao = CONCAT('ESTORNO: ', descricao) 
                                       WHERE lancamento_id = ?");
            $stmt_mov->bind_param("i", $id);
            $stmt_mov->execute();
            
            $_SESSION['mensagem'] = "Lançamento cancelado com sucesso!";
        } else {
            $_SESSION['mensagem'] = "Erro ao cancelar lançamento: " . $conn->error;
        }
        
        echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";    
        return;
    }
    
    // Ação não reconhecida
    else {
        $_SESSION['mensagem'] = "Erro: Ação inválida.";
        echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";    
        return;
    }
} else {
    // Acesso direto ao script sem POST
    echo "<script>window.location.href = 'index.php?page=lancamentos';</script>";
    return;


}