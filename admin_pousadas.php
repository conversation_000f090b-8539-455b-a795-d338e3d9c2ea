<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include_once("config.php");
include_once("func.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    echo "<script>window.location.href = 'login.php';</script>";
    return;
}

// Verifica se o usuário é o super administrador (pousada_id = 0)
if ($_SESSION['user_pousada_id'] != 0) {
    echo "<h1><center>Apenas o Super Administrador pode cadastrar pousadas</center></h1>";
    echo "<script>
            setTimeout(function(){
                window.location.href = 'index.php';
            }, 3000); // Redireciona após 3 segundos (3000 milissegundos)
          </script>";
    exit();
}

// Processa as ações do formulário
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $action = $_POST['action'] ?? '';
    $nome = $_POST['nome'] ?? '';
    $cnpj = $_POST['cnpj'] ?? '';
    $rua = $_POST['rua'] ?? '';
    $numero = $_POST['numero'] ?? '';
    $complemento = $_POST['complemento'] ?? '';
    $bairro = $_POST['bairro'] ?? '';
    $cidade = $_POST['cidade'] ?? '';
    $estado = $_POST['estado'] ?? '';
    $cep = $_POST['cep'] ?? '';
    $pais = $_POST['pais'] ?? 'Brasil';
    $telefone = $_POST['telefone'] ?? '';
    $email = $_POST['email'] ?? '';
    $id = $_POST['id'] ?? 0;
    
    // Dados do administrador da pousada
    $admin_nome = $_POST['admin_nome'] ?? '';
    $admin_email = $_POST['admin_email'] ?? '';
    $admin_senha = $_POST['admin_senha'] ?? '';

    switch ($action) {
        case 'cadastrar':
            // Verifica se o CNPJ já existe
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM pousadas WHERE cnpj = ?", 
                's', 
                $cnpj
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este CNPJ já está cadastrado.";
                break;
            }

            // Verifica se o email da pousada já existe
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM pousadas WHERE email = ?", 
                's', 
                $email
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este email já está cadastrado para outra pousada.";
                break;
            }

            // Verifica se o email do administrador já existe
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM usuarios WHERE email = ?", 
                's', 
                $admin_email
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este email de administrador já está cadastrado.";
                break;
            }

            // Inicia uma transação para garantir que todas as operações sejam concluídas
            $conn->begin_transaction();
            
            try {
                // Cadastra a nova pousada
                $stmt = prepareAndExecute($conn, 
                    "INSERT INTO pousadas (nome, cnpj, rua, numero, complemento, bairro, cidade, estado, cep, pais, telefone, email) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", 
                    'ssssssssssss', 
                    $nome, $cnpj, $rua, $numero, $complemento, $bairro, $cidade, $estado, $cep, $pais, $telefone, $email
                );
                
                if ($stmt->affected_rows <= 0) {
                    throw new Exception("Erro ao cadastrar pousada.");
                }
                
                // Obtém o ID da pousada recém-cadastrada
                $pousada_id = $conn->insert_id;
                
                // Cadastra o administrador da pousada
                $senha_hash = password_hash($admin_senha, PASSWORD_DEFAULT);
                $stmt = prepareAndExecute($conn, 
                    "INSERT INTO usuarios (nome, email, senha, pousada_id, is_admin) VALUES (?, ?, ?, ?, 1)", 
                    'sssi', 
                    $admin_nome, $admin_email, $senha_hash, $pousada_id
                );
                
                if ($stmt->affected_rows <= 0) {
                    throw new Exception("Erro ao cadastrar administrador.");
                }
                
                // Inicializa dados padrão para a nova pousada
                if (!inicializarDadosPadraoNovaPousada($conn, $pousada_id)) {
                    throw new Exception("Erro ao inicializar dados padrão para a pousada.");
                }
                
                // Cria um contrato básico para a pousada (plano padrão)
                $stmt = prepareAndExecute($conn, 
                    "INSERT INTO contratos (pousada_id, plano_id, data_contratacao, data_expiracao, status) 
                     VALUES (?, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'ativo')", 
                    'i', 
                    $pousada_id
                );
                
                if ($stmt->affected_rows <= 0) {
                    throw new Exception("Erro ao criar contrato para a pousada.");
                }
                
                // Confirma todas as operações
                $conn->commit();
                $_SESSION['success'] = "Pousada e administrador cadastrados com sucesso.";
                
            } catch (Exception $e) {
                // Reverte todas as operações em caso de erro
                $conn->rollback();
                $_SESSION['error'] = "Erro: " . $e->getMessage();
            }
            break;

        case 'alterar':
            // Verifica se o CNPJ já existe para outra pousada
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM pousadas WHERE cnpj = ? AND id != ?", 
                'si', 
                $cnpj, $id
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este CNPJ já está cadastrado para outra pousada.";
                break;
            }

            // Verifica se o email já existe para outra pousada
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM pousadas WHERE email = ? AND id != ?", 
                'si', 
                $email, $id
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este email já está cadastrado para outra pousada.";
                break;
            }

            // Atualiza a pousada
            $stmt = prepareAndExecute($conn, 
                "UPDATE pousadas SET nome = ?, cnpj = ?, rua = ?, numero = ?, complemento = ?, 
                 bairro = ?, cidade = ?, estado = ?, cep = ?, pais = ?, telefone = ?, email = ? 
                 WHERE id = ?", 
                'ssssssssssssi', 
                $nome, $cnpj, $rua, $numero, $complemento, $bairro, $cidade, $estado, $cep, $pais, $telefone, $email, $id
            );
            
            if ($stmt->affected_rows > 0) {
                $_SESSION['success'] = "Pousada alterada com sucesso.";
            } else {
                $_SESSION['error'] = "Erro ao alterar pousada ou nenhum dado foi modificado.";
            }
            break;

        case 'excluir':
            // Verifica se existem usuários associados à pousada
            $stmt = prepareAndExecute($conn, 
                "SELECT COUNT(*) as total FROM usuarios WHERE pousada_id = ?", 
                'i', 
                $id
            );
            $result = $stmt->get_result();
            $total_usuarios = $result->fetch_assoc()['total'];
            
            if ($total_usuarios > 0) {
                $_SESSION['error'] = "Não é possível excluir a pousada pois existem usuários associados a ela.";
                break;
            }
            
            // Inicia uma transação
            $conn->begin_transaction();
            
            try {
                // Exclui os contratos da pousada
                $stmt = prepareAndExecute($conn, 
                    "DELETE FROM contratos WHERE pousada_id = ?", 
                    'i', 
                    $id
                );
                
                // Exclui as formas de pagamento da pousada
                $stmt = prepareAndExecute($conn, 
                    "DELETE FROM formas_pagamento WHERE pousada_id = ?", 
                    'i', 
                    $id
                );
                
                // Exclui as categorias financeiras da pousada
                $stmt = prepareAndExecute($conn, 
                    "DELETE FROM categorias_financeiras WHERE pousada_id = ?", 
                    'i', 
                    $id
                );
                
                // Exclui a pousada
                $stmt = prepareAndExecute($conn, 
                    "DELETE FROM pousadas WHERE id = ?", 
                    'i', 
                    $id
                );
                
                if ($stmt->affected_rows <= 0) {
                    throw new Exception("Erro ao excluir pousada.");
                }
                
                // Confirma todas as operações
                $conn->commit();
                $_SESSION['success'] = "Pousada excluída com sucesso.";
                
            } catch (Exception $e) {
                // Reverte todas as operações em caso de erro
                $conn->rollback();
                $_SESSION['error'] = "Erro: " . $e->getMessage();
            }
            break;
    }

    // Redireciona para evitar reenvio do formulário
    echo "<script>window.location.href = '?page=admin_pousadas';</script>";
    return;
}

// Obtém a lista de pousadas (exceto a pousada 0 que é do super administrador)
$sql = "SELECT p.id, p.nome, p.cnpj, p.cidade, p.estado, p.email, p.telefone, 
        (SELECT COUNT(*) FROM usuarios WHERE pousada_id = p.id) as total_usuarios 
        FROM pousadas p 
        WHERE p.id != 0 
        ORDER BY p.nome";

$result = $conn->query($sql);

if ($result === false) {
    die('Query failed: ' . $conn->error);
}

$pousadas = $result; // Use $result ao invés de $stmt->get_result()
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administração de Pousadas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
    <link rel="stylesheet" href="custom/css/admin-financeiro.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Administração de Pousadas</h2>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
        <?php endif; ?>

        <div class="alert alert-info">
            <strong>Importante:</strong> Ao cadastrar uma nova pousada, você também deve cadastrar um administrador para ela.
        </div>
      
        <!-- Botão para abrir o modal de cadastro -->
        <div class="mb-4">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#cadastrarModal">
                <i class="bi bi-building-add"></i> Nova Pousada
            </button>
        </div>

        <!-- Lista de Pousadas -->
        <div class="card">
            <div class="card-header">
                <h5>Pousadas Cadastradas</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>CNPJ</th>
                                <th>Cidade/UF</th>
                                <th>Email</th>
                                <th>Telefone</th>
                                <th>Usuários</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pousadas as $pousada): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($pousada['nome']); ?></td>
                                    <td><?php echo htmlspecialchars($pousada['cnpj']); ?></td>
                                    <td><?php echo htmlspecialchars($pousada['cidade'] . '/' . $pousada['estado']); ?></td>
                                    <td><?php echo htmlspecialchars($pousada['email']); ?></td>
                                    <td><?php echo htmlspecialchars($pousada['telefone']); ?></td>
                                    <td><?php echo htmlspecialchars($pousada['total_usuarios']); ?></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" 
                                                onclick="editarPousada(<?php echo $pousada['id']; ?>)">
                                            Editar
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="excluirPousada(<?php echo $pousada['id']; ?>)">
                                            Excluir
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Cadastro -->
    <div class="modal fade" id="cadastrarModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cadastrar Nova Pousada</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="cadastrarForm" method="POST" action="">
                        <input type="hidden" name="action" value="cadastrar">
                        
                        <h6 class="mb-3">Dados da Pousada</h6>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nome" class="form-label">Nome da Pousada</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cnpj" class="form-label">CNPJ</label>
                                <input type="text" class="form-control" id="cnpj" name="cnpj" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="rua" class="form-label">Rua</label>
                                <input type="text" class="form-control" id="rua" name="rua" required>
                            </div>
                            <div class="col-md-2">
                                <label for="numero" class="form-label">Número</label>
                                <input type="text" class="form-control" id="numero" name="numero" required>
                            </div>
                            <div class="col-md-4">
                                <label for="complemento" class="form-label">Complemento</label>
                                <input type="text" class="form-control" id="complemento" name="complemento">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="bairro" class="form-label">Bairro</label>
                                <input type="text" class="form-control" id="bairro" name="bairro" required>
                            </div>
                            <div class="col-md-4">
                                <label for="cidade" class="form-label">Cidade</label>
                                <input type="text" class="form-control" id="cidade" name="cidade" required>
                            </div>
                            <div class="col-md-2">
                                <label for="estado" class="form-label">Estado</label>
                                <input type="text" class="form-control" id="estado" name="estado" maxlength="2" required>
                            </div>
                            <div class="col-md-2">
                                <label for="cep" class="form-label">CEP</label>
                                <input type="text" class="form-control" id="cep" name="cep" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="pais" class="form-label">País</label>
                                <input type="text" class="form-control" id="pais" name="pais" value="Brasil" required>
                            </div>
                            <div class="col-md-4">
                                <label for="telefone" class="form-label">Telefone</label>
                                <input type="text" class="form-control" id="telefone" name="telefone" required>
                            </div>
                            <div class="col-md-4">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h6 class="mb-3">Dados do Administrador</h6>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="admin_nome" class="form-label">Nome</label>
                                <input type="text" class="form-control" id="admin_nome" name="admin_nome" required>
                            </div>
                            <div class="col-md-4">
                                <label for="admin_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                            </div>
                            <div class="col-md-4">
                                <label for="admin_senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="admin_senha" name="admin_senha" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="cadastrarForm" class="btn btn-primary">Cadastrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div class="modal fade" id="editarModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Editar Pousada</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editarForm" method="POST" action="">
                        <input type="hidden" name="action" value="alterar">
                        <input type="hidden" name="id" id="edit_id">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_nome" class="form-label">Nome da Pousada</label>
                                <input type="text" class="form-control" id="edit_nome" name="nome" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_cnpj" class="form-label">CNPJ</label>
                                <input type="text" class="form-control" id="edit_cnpj" name="cnpj" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_rua" class="form-label">Rua</label>
                                <input type="text" class="form-control" id="edit_rua" name="rua" required>
                            </div>
                            <div class="col-md-2">
                                <label for="edit_numero" class="form-label">Número</label>
                                <input type="text" class="form-control" id="edit_numero" name="numero" required>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_complemento" class="form-label">Complemento</label>
                                <input type="text" class="form-control" id="edit_complemento" name="complemento">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="edit_bairro" class="form-label">Bairro</label>
                                <input type="text" class="form-control" id="edit_bairro" name="bairro" required>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_cidade" class="form-label">Cidade</label>
                                <input type="text" class="form-control" id="edit_cidade" name="cidade" required>
                            </div>
                            <div class="col-md-2">
                                <label for="edit_estado" class="form-label">Estado</label>
                                <input type="text" class="form-control" id="edit_estado" name="estado" maxlength="2" required>
                            </div>
                            <div class="col-md-2">
                                <label for="edit_cep" class="form-label">CEP</label>
                                <input type="text" class="form-control" id="edit_cep" name="cep" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="edit_pais" class="form-label">País</label>
                                <input type="text" class="form-control" id="edit_pais" name="pais" required>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_telefone" class="form-label">Telefone</label>
                                <input type="text" class="form-control" id="edit_telefone" name="telefone" required>
                            </div>
                            <div class="col-md-4">
                                <label for="edit_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="edit_email" name="email" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="editarForm" class="btn btn-primary">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div class="modal fade" id="excluirModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir esta pousada? Esta ação não pode ser desfeita.</p>
                    <form id="excluirForm" method="POST" action="">
                        <input type="hidden" name="action" value="excluir">
                        <input type="hidden" name="id" id="excluir_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="excluirForm" class="btn btn-danger">Excluir</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarPousada(id) {
            fetch(`pega_dados_pousada.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }
                    
                    // Preencher os campos do formulário
                    document.getElementById('edit_id').value = data.id;
                    document.getElementById('edit_nome').value = data.nome;
                    document.getElementById('edit_cnpj').value = data.cnpj;
                    document.getElementById('edit_rua').value = data.rua;
                    document.getElementById('edit_numero').value = data.numero;
                    document.getElementById('edit_complemento').value = data.complemento || '';
                    document.getElementById('edit_bairro').value = data.bairro;
                    document.getElementById('edit_cidade').value = data.cidade;
                    document.getElementById('edit_estado').value = data.estado;
                    document.getElementById('edit_cep').value = data.cep;
                    document.getElementById('edit_pais').value = data.pais;
                    document.getElementById('edit_telefone').value = data.telefone || '';
                    document.getElementById('edit_email').value = data.email;
                    
                    // Mostrar o modal
                    new bootstrap.Modal(document.getElementById('editarModal')).show();
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao carregar dados da pousada');
                });
        }

        function excluirPousada(id) {
            document.getElementById('excluir_id').value = id;
            new bootstrap.Modal(document.getElementById('excluirModal')).show();
        }
    </script>

    <script>
// Função para formatar CNPJ
function formatarCNPJ(cnpj) {
    cnpj = cnpj.replace(/\D/g, '');
    cnpj = cnpj.replace(/^(\d{2})(\d)/, '$1.$2');
    cnpj = cnpj.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
    cnpj = cnpj.replace(/\.(\d{3})(\d)/, '.$1/$2');
    cnpj = cnpj.replace(/(\d{4})(\d)/, '$1-$2');
    return cnpj;
}

// Função para validar CNPJ
function validarCNPJ(cnpj, idExcluir = 0) {
    const cnpjInput = document.querySelector('#' + cnpj.id);
    const feedbackDiv = cnpjInput.parentNode.querySelector('.cnpj-feedback') || 
                       createFeedbackDiv(cnpjInput);
    
    // Limpar feedback anterior
    feedbackDiv.innerHTML = '';
    feedbackDiv.className = 'cnpj-feedback';
    cnpjInput.classList.remove('is-valid', 'is-invalid');
    
    const cnpjValue = cnpj.value.replace(/\D/g, '');
    
    if (cnpjValue.length === 0) {
        return;
    }
    
    if (cnpjValue.length !== 14) {
        showFeedback(feedbackDiv, cnpjInput, 'CNPJ deve ter 14 dígitos', 'error');
        return;
    }
    
    // Mostrar loading
    feedbackDiv.innerHTML = '<small class="text-info"><i class="spinner-border spinner-border-sm me-1"></i>Verificando...</small>';
    
    // Fazer requisição AJAX
    fetch(`verificar_cnpj.php?cnpj=${encodeURIComponent(cnpjValue)}&id=${idExcluir}`)
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                showFeedback(feedbackDiv, cnpjInput, data.message, 'success');
            } else {
                showFeedback(feedbackDiv, cnpjInput, data.message, 'error');
            }
        })
        .catch(error => {
            showFeedback(feedbackDiv, cnpjInput, 'Erro ao verificar CNPJ', 'error');
            console.error('Erro:', error);
        });
}

function createFeedbackDiv(input) {
    const feedbackDiv = document.createElement('div');
    feedbackDiv.className = 'cnpj-feedback';
    input.parentNode.appendChild(feedbackDiv);
    return feedbackDiv;
}

function showFeedback(feedbackDiv, input, message, type) {
    const iconClass = type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-circle-fill';
    const textClass = type === 'success' ? 'text-success' : 'text-danger';
    const inputClass = type === 'success' ? 'is-valid' : 'is-invalid';
    
    feedbackDiv.innerHTML = `<small class="${textClass}"><i class="bi ${iconClass} me-1"></i>${message}</small>`;
    input.classList.remove('is-valid', 'is-invalid');
    input.classList.add(inputClass);
}

// Aplicar eventos aos campos CNPJ quando o documento carregar
document.addEventListener('DOMContentLoaded', function() {
    // Para o modal de cadastro
    const cnpjCadastro = document.getElementById('cnpj');
    if (cnpjCadastro) {
        cnpjCadastro.addEventListener('input', function() {
            this.value = formatarCNPJ(this.value);
        });
        
        cnpjCadastro.addEventListener('blur', function() {
            validarCNPJ(this);
        });
    }
    
    // Para o modal de edição
    const cnpjEdicao = document.getElementById('edit_cnpj');
    if (cnpjEdicao) {
        cnpjEdicao.addEventListener('input', function() {
            this.value = formatarCNPJ(this.value);
        });
        
        cnpjEdicao.addEventListener('blur', function() {
            const idPousada = document.getElementById('edit_id').value;
            validarCNPJ(this, idPousada);
        });
    }
});
</script>

<?php
    echo '<br>';
    echo '<button onclick="location.href=\'index.php\';" class="btn btn-success">Home</button>';
?>

</body>
</html>