/**
 * Função TypeScript baseada na lógica PHP de verificar_disponibilidade.php
 * Permite verificação local sem requisições ao servidor
 */

// Interfaces para tipagem
interface Reserva {
    id: number;
    uh: string;
    dataentrada: string;
    horaentrada: string;
    datasaida: string;
    horasaida: string;
    hospede_nome: string;
    hospede_id?: number;
}

interface ConflitosDetalhes {
    id: number;
    hospede_nome: string;
    data_entrada_formatada: string;
    data_saida_formatada: string;
    dataentrada: string;  // Adicionar campo de data original
    datasaida: string;    // Adicionar campo de data original
    horaentrada: string;
    horasaida: string;
}

interface ResultadoConflito {
    conflito: boolean;
    detalhes: ConflitosDetalhes[];
}

interface ResultadoVerificacao {
    disponivel: boolean;
    reserva_id?: number;
    mensagem?: string;
    fonte: 'local' | 'servidor' | 'local-fallback' | 'validacao';
    id_excluido?: number;
    erro?: string;
}

interface RespostaCarregarReservas {
    sucesso: boolean;
    reservas: Reserva[];
    erro?: string;
}

type CallbackVerificacao = (data: ResultadoVerificacao | null, error: string | null) => void;

class VerificacaoDisponibilidadeLocal {
    private reservasCache: Map<string, Reserva[]>;
    private ultimaAtualizacao: number | null;
    private readonly CACHE_VALIDO_MS: number = 30000; // 30 segundos

    constructor() {
        this.reservasCache = new Map<string, Reserva[]>();
        this.ultimaAtualizacao = null;
    }

    /**
     * Carrega reservas do servidor para cache local
     */
    public async carregarReservas(uh: string | null = null, forcarAtualizacao: boolean = false): Promise<void> {
        const agora: number = new Date().getTime();

        if (!forcarAtualizacao && this.ultimaAtualizacao &&
            (agora - this.ultimaAtualizacao) < this.CACHE_VALIDO_MS) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('acao', 'carregar_reservas');
            if (uh) {
                formData.append('uh', uh);
            }

            const response: Response = await fetch('custom/includes/carregar_reservas.php', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: RespostaCarregarReservas = await response.json();

            if (data.sucesso && data.reservas) {
                // Limpar cache se for atualização forçada
                if (forcarAtualizacao) {
                    this.reservasCache.clear();
                }

                // Organizar reservas por UH
                data.reservas.forEach((reserva: Reserva) => {
                    if (!this.reservasCache.has(reserva.uh)) {
                        this.reservasCache.set(reserva.uh, []);
                    }
                    this.reservasCache.get(reserva.uh)!.push(reserva);
                });

                this.ultimaAtualizacao = agora;
            } else {
                console.warn('Falha ao carregar reservas:', data.erro || 'Resposta inválida');
            }
        } catch (error) {
            console.error('Erro ao carregar reservas:', error);
            throw error;
        }
    }

    /**
     * Verifica conflito de período - baseado na lógica PHP
     */
    public verificarConflitoLocal(
        dataEntrada: string,
        horaEntrada: string,
        dataSaida: string,
        horaSaida: string,
        uh: string,
        reservaIdExcluir: number | null = null
    ): ResultadoConflito {
        const reservasUH: Reserva[] = this.reservasCache.get(uh) || [];

        // Combinar data e hora
        const datetimeEntrada: string = `${dataEntrada} ${horaEntrada}`;
        const datetimeSaida: string = `${dataSaida} ${horaSaida}`;

        const tsEntradaNova: number = new Date(datetimeEntrada).getTime();
        const tsSaidaNova: number = new Date(datetimeSaida).getTime();

        // Validar timestamps
        if (isNaN(tsEntradaNova) || isNaN(tsSaidaNova)) {
            console.error('Datas inválidas fornecidas:', { dataEntrada, horaEntrada, dataSaida, horaSaida });
            return { conflito: false, detalhes: [] };
        }

        if (tsEntradaNova >= tsSaidaNova) {
            console.warn('Data/hora de entrada deve ser anterior à saída');
            return {
                conflito: true,
                detalhes: [{
                    id: -1, // ID especial para erro de validação
                    hospede_nome: 'Erro de Validação',
                    data_entrada_formatada: this.formatarData(dataEntrada),
                    data_saida_formatada: this.formatarData(dataSaida),
                    dataentrada: dataEntrada,  // Adicionar campo original
                    datasaida: dataSaida,      // Adicionar campo original
                    horaentrada: horaEntrada,
                    horasaida: horaSaida
                }]
            };
        }

        const conflitos: ConflitosDetalhes[] = [];

        for (const reserva of reservasUH) {
            // Pular se for a própria reserva sendo editada
            if (reservaIdExcluir && reserva.id === reservaIdExcluir) {
                continue;
            }

            const entradaExistente: string = `${reserva.dataentrada} ${reserva.horaentrada || '13:00:00'}`;
            const saidaExistente: string = `${reserva.datasaida} ${reserva.horasaida || '23:59:59'}`;

            const tsEntradaExistente: number = new Date(entradaExistente).getTime();
            const tsSaidaExistente: number = new Date(saidaExistente).getTime();

            // Verificar se as datas existentes são válidas
            if (isNaN(tsEntradaExistente) || isNaN(tsSaidaExistente)) {
                console.warn('Reserva com datas inválidas ignorada:', reserva);
                continue;
            }

            // Verificar sobreposição: entrada_nova < saida_existente E saida_nova > entrada_existente
            if (tsEntradaNova < tsSaidaExistente && tsSaidaNova > tsEntradaExistente) {
                conflitos.push({
                    id: reserva.id,
                    hospede_nome: reserva.hospede_nome,
                    data_entrada_formatada: this.formatarData(reserva.dataentrada),
                    data_saida_formatada: this.formatarData(reserva.datasaida),
                    dataentrada: reserva.dataentrada,  // Adicionar campo original
                    datasaida: reserva.datasaida,      // Adicionar campo original
                    horaentrada: reserva.horaentrada,
                    horasaida: reserva.horasaida
                });
            }
        }

        return {
            conflito: conflitos.length > 0,
            detalhes: conflitos
        };
    }

    /**
     * Formata mensagem de conflito de forma mais concisa
     */
    private formatarMensagemConflito(conflito: ConflitosDetalhes, uh: string): string {
        // Usar os campos de data originais (YYYY-MM-DD) em vez dos formatados
        const dataEntrada = new Date(conflito.dataentrada);
        const dataSaida = new Date(conflito.datasaida);

        // Verificar se são datas diferentes
        const sameDay = dataEntrada.toDateString() === dataSaida.toDateString();

        if (sameDay) {
            // Mesmo dia: "Reservada em 24/06 de 13:00 às 15:00"
            const data = dataEntrada.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            const horaEntrada = conflito.horaentrada.substring(0, 5); // Remove segundos
            const horaSaida = conflito.horasaida.substring(0, 5);
            return `Reservada em ${data} de ${horaEntrada} às ${horaSaida}`;
        } else {
            // Datas diferentes: "Reservada entre 24/06 e 25/06"
            const dataIni = dataEntrada.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            const dataFim = dataSaida.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
            return `Reservada entre ${dataIni} e ${dataFim}`;
        }
    }

    /**
     * Função principal para verificação híbrida (local + servidor)
     */
    public async verificarDisponibilidade(
        uh: string,
        dataEntrada: string,
        horaEntrada: string,
        dataSaida: string,
        horaSaida: string,
        reservaIdExcluir: number | null = null
    ): Promise<ResultadoVerificacao> {
        // Validar parâmetros obrigatórios
        if (!uh || !dataEntrada) {
            throw new Error('UH e data de entrada são obrigatórios');
        }

        try {
            // 1. Tentar verificação local primeiro
            await this.carregarReservas(uh);

            const resultadoLocal: ResultadoConflito = this.verificarConflitoLocal(
                dataEntrada, horaEntrada, dataSaida, horaSaida, uh, reservaIdExcluir
            );

            // 2. Se houver conflito local, retornar imediatamente
            if (resultadoLocal.conflito && resultadoLocal.detalhes.length > 0) {
                const conflito: ConflitosDetalhes = resultadoLocal.detalhes[0];

                // Caso especial: erro de validação de datas
                if (conflito.id === -1) {
                    return {
                        disponivel: false,
                        mensagem: 'Data/hora de entrada deve ser anterior à saída',
                        fonte: 'validacao'
                    };
                }

                return {
                    disponivel: false,
                    reserva_id: conflito.id,
                    mensagem: this.formatarMensagemConflito(conflito, uh),
                    fonte: 'local'
                };
            }

            // 3. Se não houver conflito local, fazer verificação no servidor para confirmar
            return await this.verificarNoServidor(uh, dataEntrada, horaEntrada, dataSaida, horaSaida, reservaIdExcluir);

        } catch (error) {
            console.error('Erro na verificação de disponibilidade:', error);
            return {
                disponivel: false,
                mensagem: `Erro na verificação: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
                fonte: 'local-fallback'
            };
        }
    }

    /**
     * Verificação no servidor
     */
    private async verificarNoServidor(
        uh: string,
        dataEntrada: string,
        horaEntrada: string,
        dataSaida: string,
        horaSaida: string,
        reservaIdExcluir: number | null
    ): Promise<ResultadoVerificacao> {
        try {
            const formData = new FormData();
            formData.append('uh', uh);
            formData.append('dataEntrada', dataEntrada);
            formData.append('horaEntrada', horaEntrada);
            formData.append('dataSaida', dataSaida);
            formData.append('horaSaida', horaSaida);

            if (reservaIdExcluir !== null) {
                formData.append('reserva_id_excluir', reservaIdExcluir.toString());
            }

            const response: Response = await fetch('verificar_disponibilidade.php', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const resultado: ResultadoVerificacao = await response.json();
            resultado.fonte = 'servidor';

            // Atualizar cache se servidor encontrou conflito que o local não viu
            if (!resultado.disponivel) {
                await this.carregarReservas(uh, true); // Forçar atualização do cache
            }

            return resultado;

        } catch (error) {
            console.error('Erro na verificação do servidor:', error);
            // Retornar resultado local em caso de erro do servidor
            return {
                disponivel: true,
                mensagem: 'Verificação local apenas (servidor indisponível)',
                fonte: 'local-fallback'
            };
        }
    }

    /**
     * Formatar data para exibição
     */
    private formatarData(data: string): string {
        try {
            const d = new Date(data);
            if (isNaN(d.getTime())) {
                return data; // Retornar string original se não conseguir converter
            }
            return d.toLocaleDateString('pt-BR');
        } catch (error) {
            console.warn('Erro ao formatar data:', data, error);
            return data;
        }
    }

    /**
     * Limpar cache (útil após criar/editar/excluir reservas)
     */
    public limparCache(): void {
        this.reservasCache.clear();
        this.ultimaAtualizacao = null;
    }

    /**
     * Obter estatísticas do cache
     */
    public obterEstatisticasCache(): { totalUHs: number; totalReservas: number; ultimaAtualizacao: Date | null } {
        let totalReservas = 0;
        this.reservasCache.forEach(reservas => {
            totalReservas += reservas.length;
        });

        return {
            totalUHs: this.reservasCache.size,
            totalReservas,
            ultimaAtualizacao: this.ultimaAtualizacao ? new Date(this.ultimaAtualizacao) : null
        };
    }
}

// Instância global
const verificadorLocal: VerificacaoDisponibilidadeLocal = new VerificacaoDisponibilidadeLocal();

/**
 * Função compatível com o código existente
 */
async function verificarDisponibilidadeUHMelhorada(
    uh: string,
    dataEntrada: string,
    horaEntrada: string,
    callback: CallbackVerificacao,
    reservaIdExcluir?: number | null,
    dataSaida?: string,
    horaSaida?: string
): Promise<void> {
    try {
        const resultado: ResultadoVerificacao = await verificadorLocal.verificarDisponibilidade(
            uh,
            dataEntrada,
            horaEntrada || '13:00',
            dataSaida || dataEntrada,
            horaSaida || '12:00',
            reservaIdExcluir || null
        );

        callback(resultado, null);
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        callback(null, errorMessage);
    }
}

// Exportações ES6
export {
    VerificacaoDisponibilidadeLocal,
    verificadorLocal,
    verificarDisponibilidadeUHMelhorada
};