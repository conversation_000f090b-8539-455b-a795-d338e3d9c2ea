Limitações da Integração Atual

Analisando o código, identifiquei algumas limitações na integração atual:



Não há uma verificação explícita da forma de pagamento para determinar se um lançamento deve afetar o caixa (por exemplo, pagamentos com cartão não deveriam afetar o caixa em dinheiro)

Não há tratamento para situações em que um lançamento financeiro é editado ou cancelado após já ter gerado uma movimentação no caixa

Não há uma funcionalidade clara para reconciliar diferenças entre o saldo do caixa e os lançamentos financeiros





A cópia do HM para Pousada:

-Me certifiquei de que não há referência direta a www.hospedagemax.com.br;

-Passei todos os arquivos de hospedamax para pousada;

-alterei config.php indicando outro banco de dados;

-alterei form_fnrh.css duas cores de fundo



Alterações realizadas



(ok) 19/05/2025 Index.php - reorganização do menu (atualizar em HM)



(ok) 24/05/2025 - hospedes_novo.php - após cadastro vai direto par listar o hospede, sem passar pela hospedes_procurar.php.



(ok) 27/05/2025 - Reaproveitamento de formulários Hospedes e reservas



(ok) Em reservas_editar.php funcionou, faz as duas funções de identificar e impedir salvar, mas, em reservas_novo.php não faz ambas as funções.



(ok) Completar com zeros à esquerda UH no cadastro, edição e mapa_uh de reservas.



(ok) Ao Salvar nova reserva está com erro



(ok) fazer Enter = Tab nos formulários



(ok) Em reserva_editar Data de saida tem que ser maior ou igual à data de entrada. Mas, em novo já compara corretamente.



(ok) Após apagar um hospede em #hospedes_listar quero que retornar para hospedes_listar.php



(ok) A função de CPF em func.js não verificar se há menos ou mais caracteres além de 11.



(ok) Ao fazer um novo cadastro com cpf repetido de outro hospede: está idenficando, e mostrando apenas "CPF invalido" em vermelho abaixo do campo; e em mensagem flutuante mostra: o nome do hospede que detém o CPF inicialmente. Quero que esta mesagem apareça no lugar daquela em vermelho abaixo do campo.



(ok) Na tela de hospede_editar está dizendo que o cpf é invalido também, pois está comparando com a própria pessoa.



(ok) Comparação se aquele cpf já foi cadastrado por outro hospede.



(ok)Passo 1:

Quero transferir todo o css existente em mapa_uh.php para um novo arquivo que você deve criar: mapa_uh.css e que deve ficar dentro da hierarquia abaixo:



custom/

├── css/

│   ├── externo.css

│   ├── form_fnrh.css

│   └── mapa_uh.css





(ok)passo 2:

Quero transferir o arquivo func.js para dentro da estrutura abaixo:



custom/

├── css/

├── includes/

└── js/

    ├── func.js



(ok)passo 3:

Quero trasferir todas as funções js que existam em mapa_uh.php para um arquivo que você deve criar: mapa_uh.js na estrutura abaixo:

(ok)Agora chega a mostrar a mensagem de reserva registrada com sucesso, mas não guarda no BD.



custom/

├── css/

├── includes/

└── js/

    ├── func.js

    ├── mapa_uh-main.js

    ├── mmodal-detalhes.js

    ├── modal-hospede.js

    └── modal-reserva.js



(ok) Erro: Conflito de reserva detectado! A UH 003 já está reservada para Adércio Caverzan no período selecionado.



(ok)passo 4:



Quero transferir funções php que estejam dentro de #mapa_uh.php para um arquivo que você deve criar: mapa_uh_functions.php



custom/

├── css/

├── includes/

│   ├── mapa_uh_functions.php





(ok) passo 5:

Quero trasferir toda parte de modais para um arquivo que você deve criar: modals_mapa_uh.php



custom/

├── css/

├── includes/

│   ├── mapa_uh_functions.php

│   └── modals_mapa_uh.php



(ok) Na implemtação de TypeScript preciso criar e editar o tsconfig.json e criar a estrutura de pastas

==============================

{

  "compilerOptions": {

    "target": "ES5",

    "module": "CommonJS",

    "outDir": "./dist",

    "rootDir": "./src",

    "strict": true,

    "esModuleInterop": true,

    "skipLibCheck": true,

    "forceConsistentCasingInFileNames": true,

    "lib": ["DOM", "ES2015", "ES2016", "ES2017"]

  },

  "include": ["src/**/*"],

  "exclude": ["node_modules"]

}



mkdir src

mkdir src\js

==============================



(ok) Em modal novo hospede à partir de mapa_uh.php não verifica se cpf estiver incompleto



(ok) após o modal hospede novo,o modal reserva novo mostra conflito de data de entrada sem ter.



================================

(ok) Quanto ao funcionamento da comparação:

-No celular aparecem três tipos de mensagem:

1-Sobre o campo: !Reserva em data passada (Quero manter)

2-Sob a o campo: !Data não pode ser anterior ao momento atual (Não quero manter e nem é verdade, pois pretendo permitir lançamentos em datas passadas;

3- Em rosa, sob  o campo: Conflito! UH 001 já reservada para Lucivandro de Castro Leitão de 24/06/2025 13:00:00 até 24/06/2025 15:00:00 (acho muito comprida) Vamos reduzir para: Reservada entre 24/06 e 25/06 (quando for datas difrentes)

Reservada em 24/06 de 13:00 às 15:00.

-No computador apenas o item 3 aparece.

================================



(ok) Mudar mensagens para tooltip 

=================================================

#verificacao-disponibilidade-local.ts

#verificar_disponibilidade.php

"Reservada em" (exemplo: "Reservada em 25/06 de 13:00 às 15:00")



#verificacao-disponibilidade-local.ts

"Data/hora de entrada deve ser anterior à saída" 



#mapa_uh.css

"Data Antiga" (em amarelo sobre o campo)

===================================================



(ok) Em Detalhes da Unidade Habitacional deve aparecer também as horas de entrada e saída junto às datas respectivas;



===================================================

(ok)Considernado duas reservas no mesmo dia em uma UH: No BD quando estão guardados de forma invertida, ou seja, a reserva de data anterior está registrada na tabela reservas com id maior e a reserva de data posterior com id menor. Nessa situação específica, no modal detalhes de unidade habitacional, estão sendo mostrados duas vezes apenas os dados da reserva de id maior. Os dados abaixo ilustram:

SELECT id hospede_id,pousada_id,uh,numacomp,dataentrada,horaentrada,datasaida,horasaida FROM reservas WHERE dataentrada = "2025-07-13" AND uh="003"



hospede_id,pousada_id,uh,numacomp,dataentrada,horaentrada,datasaida,horasaida

657,2,003,1,2025-07-13,13:00:00,2025-07-14,12:00:00

658,2,003,1,2025-07-13,09:00:00,2025-07-13,11:00:00

===================================================



(ok) Ao clicar em uma célula vazia com data passada, surge o botão "Reserva Retroativa" mas, que não produz qualquer resultado ao ser clicada.



(ok) Em @mapa_uh.php Detalhes da Unidade Habitacional deve mostrar as reservas em ordem de data.



(ok) Em @mapa_uh.php Detalhes da Unidade Habitacional está exibindo o botão de nova reserva ou reserva retroativa apaenas para células com uma reserva, quero que seja permitido novas reservas mesmo que existam outras



(ok) Essa lógica de célula livre, ocupada, dupla coupação, creio que já não faz mais sentido, já que estamos permitindo qualquer quantidade de reservas. Analise suas implicações para retirar do código caso possível.



(ok) Em @mapa_uh.php Detalhes da Unidade Habitacional está exibindo o botão de nova reserva ou reserva retroativa apenas para células com uma reserva. Quero que sejam permitidas novas reservas mesmo que existam outras.



(ok) Criar lógica de separação de CSS para desketop e mobile para tornar fácil ajusar cada um.



(ok)No modal Selecionar Hóspede para Nova Reserva fazer procurar por qualquer quantidade de caracteres até vazio e fazer procurar por CPF.



(ok) estudar juntar CSS espalhado em arquivos



(ok) Retitar completamente mecaniscmo de cmparação de conflito de datas em novo/editar reservas



(ok) Mudar tema ( Pares de cores )



(ok) Fazer forma de carregar logotipo personalizado em configrações.



(ok) Fazer cor do formulário e logo ser guardado no BD de cada pousada.

======================================================================

( ) Página de controle debackup:

-Mostrar uma lista de pontos de backup;

-Permitir restaurar: 

-Banco de dados completo (todos os cliente):

-Permitir procurar dados expecíficos por pousada, nome, cnpj;

-restaurar um Hospede, suas Reservas, seus Lançamentos em Caixa, etc.

=========================================================================

( ) Criar um modal para editar reservas ao invés de reservas_editar.php



( ) Após Cancelar o modal de Selecionar Hóspede para Nova Reserva, a tela com o mapa_uh fica congelada.



( ) Quero que a opção de Administração de Pousadas permita administrar também os Super Administradores.



( ) Criar uma página para o caso de esquecer a senha. Alterar em pousada e CSsis



( ) retirar mensagens excessivas de comunicação de sucesso. Deixar apenas confirmação de exclusão.



( ) Em CS.com ao compensar um boleto de cliente de pousada, copiar seu email e senha ar ao banco da pousada ou liberar o acesso se estiver atrasado.



( ) Estudar colocar icones das funções principais na tela home.



( ) compreender a real função do QRCode (pensar no checkin online)



( ) Desabiliar error_reporting(E_ALL) nos scripts







**** Verificar Formas_pagamento para acolher se o tipo de pagamento afeta o caixa, já que foi alterada a tabela e os demais scripts de caixa 



Pensamentos e Respostas em pt-br;

-Faça as alterações sempre no TS;

-Não proponha testes nem tente acionar o navegador para isso;

-Não gere scritps de teste e de documentação exceto sob pedido.

-Meu nome é Claudio, me chame pelo nome;

-Sempre identifique a qual script se refere ao aplicar alterações;

-Se possível identifique as linhas a que se refere;

-Analise minhas sugestões quanto a viabilidade, praticidade, conformidade, segurança e boas práticas e, caso necessário, contraponha sugestões;

-Ao final, forneça uma lista sucinta dos arquivos alterados.

-Lista sucinta: o nome de cada arquivo em uma linha, sem explicações.



