<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se o ID foi fornecido
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$modo = isset($_GET['modo']) ? $_GET['modo'] : 'editar';

if ($id <= 0) {
    echo '<div class="alert alert-danger">ID do lançamento inválido.</div>';
    exit();
}

// Buscar dados do lançamento
$stmt = $conn->prepare("SELECT l.*, c.nome as categoria_nome, c.tipo as categoria_tipo 
                        FROM lancamentos_financeiros l
                        LEFT JOIN categorias_financeiras c ON l.categoria_id = c.id
                        WHERE l.id = ? AND l.pousada_id = ?");
$stmt->bind_param("ii", $id, $pousada_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo '<div class="alert alert-danger">Lançamento não encontrado.</div>';
    exit();
}

$lancamento = $result->fetch_assoc();

// Se for modo pagamento, exibir apenas o formulário de pagamento
if ($modo == 'pagamento') {
    ?>
    <input type="hidden" name="acao" value="registrar_pagamento">
    <input type="hidden" name="id" value="<?php echo $lancamento['id']; ?>">
    
    <div class="mb-3">
        <label class="form-label">Descrição</label>
        <p class="form-control-static"><?php echo htmlspecialchars($lancamento['descricao']); ?></p>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Valor</label>
        <p class="form-control-static">R$ <?php echo number_format($lancamento['valor'], 2, ',', '.'); ?></p>
    </div>
    
    <div class="mb-3">
        <label class="form-label">Vencimento</label>
        <p class="form-control-static"><?php echo date('d/m/Y', strtotime($lancamento['data_vencimento'])); ?></p>
    </div>
    
    <div class="mb-3">
        <label for="data_pagamento" class="form-label">Data do Pagamento*</label>
        <input type="date" class="form-control" id="data_pagamento" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>" required>
    </div>
    
    <div class="mb-3">
        <label for="forma_pagamento" class="form-label">Forma de Pagamento*</label>
        <select class="form-select" id="forma_pagamento" name="forma_pagamento" required>
            <option value="">Selecione</option>
            <?php 
            // Buscar formas de pagamento ativas
            $stmt = $conn->prepare("SELECT id, nome FROM formas_pagamento WHERE pousada_id = ? AND is_active = 1 ORDER BY nome");
            $stmt->bind_param("i", $pousada_id);
            $stmt->execute();
            $formas = $stmt->get_result();
            
            while ($forma = $formas->fetch_assoc()): 
            ?>
                <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                    <?php echo htmlspecialchars($forma['nome']); ?>
                </option>
            <?php endwhile; ?>
        </select>
    </div>
    
    <div class="mb-3">
        <label for="observacao_pagamento" class="form-label">Observação do Pagamento</label>
        <textarea class="form-control" id="observacao_pagamento" name="observacao_pagamento" rows="3"></textarea>
    </div>
    <?php
} else {
    // Modo edição - exibir formulário completo
    ?>
    <input type="hidden" name="acao" value="editar">
    <input type="hidden" name="id" value="<?php echo $lancamento['id']; ?>">
    <input type="hidden" id="status_lancamento" value="<?php echo $lancamento['status']; ?>">
    
    <div class="mb-3">
        <label for="tipo" class="form-label">Tipo*</label>
        <select class="form-select" id="tipo" name="tipo" required <?php echo $lancamento['status'] == 'pago' ? 'disabled' : ''; ?>>
            <option value="receita" <?php echo $lancamento['tipo'] == 'receita' ? 'selected' : ''; ?>>Receita</option>
            <option value="despesa" <?php echo $lancamento['tipo'] == 'despesa' ? 'selected' : ''; ?>>Despesa</option>
        </select>
        <?php if ($lancamento['status'] == 'pago'): ?>
            <input type="hidden" name="tipo" value="<?php echo $lancamento['tipo']; ?>">
        <?php endif; ?>
    </div>
    
    <div class="mb-3">
        <label for="categoria_id" class="form-label">Categoria*</label>
        <select class="form-select" id="categoria_id" name="categoria_id" required>
            <option value="">Selecione uma categoria</option>
            <?php 
            // Buscar categorias
            $stmt = $conn->prepare("SELECT id, nome, tipo FROM categorias_financeiras WHERE pousada_id = ? ORDER BY nome");
            $stmt->bind_param("i", $pousada_id);
            $stmt->execute();
            $categorias = $stmt->get_result();
            
            $categorias_receita = [];
            $categorias_despesa = [];
            
            while ($categoria = $categorias->fetch_assoc()) {
                if ($categoria['tipo'] == 'receita') {
                    $categorias_receita[] = $categoria;
                } else {
                    $categorias_despesa[] = $categoria;
                }
            }
            ?>
            
            <optgroup label="Receitas">
                <?php foreach ($categorias_receita as $categoria): ?>
                    <option value="<?php echo $categoria['id']; ?>" 
                            <?php echo $lancamento['categoria_id'] == $categoria['id'] ? 'selected' : ''; ?>
                            class="categoria-receita">
                        <?php echo htmlspecialchars($categoria['nome']); ?>
                    </option>
                <?php endforeach; ?>
            </optgroup>
            
            <optgroup label="Despesas">
                <?php foreach ($categorias_despesa as $categoria): ?>
                    <option value="<?php echo $categoria['id']; ?>" 
                            <?php echo $lancamento['categoria_id'] == $categoria['id'] ? 'selected' : ''; ?>
                            class="categoria-despesa">
                        <?php echo htmlspecialchars($categoria['nome']); ?>
                    </option>
                <?php endforeach; ?>
            </optgroup>
        </select>
    </div>
    
    <div class="mb-3">
        <label for="descricao" class="form-label">Descrição*</label>
        <input type="text" class="form-control" id="descricao" name="descricao" 
               value="<?php echo htmlspecialchars($lancamento['descricao']); ?>" required>
    </div>
    
    <div class="mb-3">
        <label for="valor" class="form-label">Valor*</label>
        <div class="input-group">
            <span class="input-group-text">R$</span>
            <input type="number" class="form-control" id="valor" name="valor" step="0.01" min="0.01" 
                   value="<?php echo $lancamento['valor']; ?>" required
                   <?php echo $lancamento['status'] == 'pago' ? 'readonly' : ''; ?>>
        </div>
        <?php if ($lancamento['status'] == 'pago'): ?>
            <small class="text-muted">O valor não pode ser alterado pois o lançamento já foi pago.</small>
        <?php endif; ?>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="mb-3">
                <label for="data_lancamento" class="form-label">Data do Lançamento*</label>
                <input type="date" class="form-control" id="data_lancamento" name="data_lancamento" 
                       value="<?php echo $lancamento['data_lancamento']; ?>" required>
            </div>
        </div>
        <div class="col-md-6">
            <div class="mb-3">
                <label for="data_vencimento" class="form-label">Data de Vencimento*</label>
                <input type="date" class="form-control" id="data_vencimento" name="data_vencimento" 
                       value="<?php echo $lancamento['data_vencimento']; ?>" required>
            </div>
        </div>
    </div>
    
    <?php if ($lancamento['reserva_id']): ?>
        <?php
        // Buscar informações da reserva
        $stmt = $conn->prepare("SELECT r.id, r.uh, h.nome 
                               FROM reservas r 
                               JOIN hospedes h ON r.hospede_id = h.id 
                               WHERE r.id = ?");
        $stmt->bind_param("i", $lancamento['reserva_id']);
        $stmt->execute();
        $reserva = $stmt->get_result()->fetch_assoc();
        ?>
        
        <div class="mb-3">
            <label class="form-label">Reserva</label>
            <p class="form-control-static">
                <?php echo htmlspecialchars($reserva['nome'] . ' - UH ' . $reserva['uh']); ?>
                <input type="hidden" name="reserva_id" value="<?php echo $lancamento['reserva_id']; ?>">
            </p>
        </div>
    <?php else: ?>
        <div class="mb-3">
            <label for="reserva_id" class="form-label">Reserva (opcional)</label>
            <select class="form-select" id="reserva_id" name="reserva_id">
                <option value="">Nenhuma reserva</option>
                <?php 
                // Buscar reservas ativas
                $stmt = $conn->prepare("SELECT r.id, r.uh, h.nome 
                                       FROM reservas r 
                                       JOIN hospedes h ON r.hospede_id = h.id 
                                       WHERE r.pousada_id = ? 
                                       AND r.datasaida >= CURDATE() 
                                       ORDER BY r.dataentrada DESC");
                $stmt->bind_param("i", $pousada_id);
                $stmt->execute();
                $reservas = $stmt->get_result();
                
                while ($reserva = $reservas->fetch_assoc()): 
                ?>
                    <option value="<?php echo $reserva['id']; ?>">
                        <?php echo htmlspecialchars($reserva['nome'] . ' - UH ' . $reserva['uh']); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
    <?php endif; ?>
    
    <div class="mb-3">
        <label for="observacao" class="form-label">Observação</label>
        <textarea class="form-control" id="observacao" name="observacao" rows="3"><?php echo htmlspecialchars($lancamento['observacao'] ?? ''); ?></textarea>
    </div>
    
    <?php if ($lancamento['status'] == 'pendente'): ?>
        <div class="mb-3 form-check">
            <input type="checkbox" class="form-check-input" id="marcar_como_pago" name="marcar_como_pago" value="1">
            <label class="form-check-label" for="marcar_como_pago">Marcar como pago</label>
        </div>
        
        <div id="div_pagamento_editar" style="display: none;">
            <div class="mb-3">
                <label for="data_pagamento" class="form-label">Data do Pagamento*</label>
                <input type="date" class="form-control" id="data_pagamento" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>">
            </div>
            
            <div class="mb-3">
                <label for="forma_pagamento" class="form-label">Forma de Pagamento*</label>
                <select class="form-select" id="forma_pagamento" name="forma_pagamento">
                    <option value="">Selecione</option>
                    <?php 
                    // Buscar formas de pagamento ativas
                    $stmt = $conn->prepare("SELECT id, nome FROM formas_pagamento WHERE pousada_id = ? AND is_active = 1 ORDER BY nome");
                    $stmt->bind_param("i", $pousada_id);
                    $stmt->execute();
                    $formas = $stmt->get_result();
                    
                    while ($forma = $formas->fetch_assoc()): 
                    ?>
                        <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                            <?php echo htmlspecialchars($forma['nome']); ?>
                        </option>
                    <?php endwhile; ?>
                </select>
            </div>
        </div>
    <?php else: ?>
        <div class="mb-3">
            <label class="form-label">Status</label>
            <p class="form-control-static">
                <?php 
                if ($lancamento['status'] == 'pago') {
                    echo '<span class="badge bg-success">Pago</span>';
                    echo ' em ' . date('d/m/Y', strtotime($lancamento['data_pagamento']));
                    echo ' via ' . htmlspecialchars($lancamento['forma_pagamento']);
                } else {
                    echo '<span class="badge bg-danger">Cancelado</span>';
                }
                ?>
            </p>
        </div>
    <?php endif; ?>
    
    <script>
        // Mostrar/ocultar campos de pagamento ao marcar checkbox
        document.getElementById('marcar_como_pago').addEventListener('change', function() {
            document.getElementById('div_pagamento_editar').style.display = this.checked ? 'block' : 'none';
        });
        
        // Filtrar categorias com base no tipo selecionado
        document.getElementById('tipo').addEventListener('change', function() {
            const tipo = this.value;
            const categoriasReceita = document.querySelectorAll('.categoria-receita');
            const categoriasDespesa = document.querySelectorAll('.categoria-despesa');
            
            if (tipo === 'receita') {
                categoriasReceita.forEach(opt => opt.style.display = '');
                categoriasDespesa.forEach(opt => opt.style.display = 'none');
            } else {
                categoriasReceita.forEach(opt => opt.style.display = 'none');
                categoriasDespesa.forEach(opt => opt.style.display = '');
            }
            
            // Limpar seleção atual
            document.getElementById('categoria_id').value = '';
        });
        
        // Executar filtro inicial
        const tipoInicial = document.getElementById('tipo').value;
        const categoriasReceita = document.querySelectorAll('.categoria-receita');
        const categoriasDespesa = document.querySelectorAll('.categoria-despesa');
        
        if (tipoInicial === 'receita') {
            categoriasReceita.forEach(opt => opt.style.display = '');
            categoriasDespesa.forEach(opt => opt.style.display = 'none');
        } else {
            categoriasReceita.forEach(opt => opt.style.display = 'none');
            categoriasDespesa.forEach(opt => opt.style.display = '');
        }
    </script>
    <?php
}
?>