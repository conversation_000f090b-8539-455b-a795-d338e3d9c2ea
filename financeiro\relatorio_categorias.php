<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

// Definir período padrão (mês atual)
$data_inicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : date('Y-m-01');
$data_fim = isset($_GET['data_fim']) ? $_GET['data_fim'] : date('Y-m-t');
$tipo = isset($_GET['tipo']) ? $_GET['tipo'] : 'todos';
$campo_data = isset($_GET['campo_data']) ? $_GET['campo_data'] : 'vencimento';

// Construir a consulta SQL base
$sql_base = "FROM lancamentos_financeiros lf
            JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
            WHERE lf.pousada_id = $pousada_id 
            AND lf.status = 'pago'
            AND lf.data_$campo_data BETWEEN '$data_inicio' AND '$data_fim'";

// Adicionar filtro de tipo se necessário
if ($tipo != 'todos') {
    $sql_base .= " AND lf.tipo = '$tipo'";
}

// Obter dados por categoria
$sql_categorias = "SELECT cf.id, cf.nome, cf.tipo, cf.cor, 
                  SUM(lf.valor) as total,
                  COUNT(lf.id) as quantidade
                  $sql_base
                  GROUP BY cf.id
                  ORDER BY cf.tipo, total DESC";
$result_categorias = $conn->query($sql_categorias);

// Obter totais
$sql_totais = "SELECT 
              SUM(CASE WHEN lf.tipo = 'receita' THEN lf.valor ELSE 0 END) as total_receitas,
              SUM(CASE WHEN lf.tipo = 'despesa' THEN lf.valor ELSE 0 END) as total_despesas,
              COUNT(CASE WHEN lf.tipo = 'receita' THEN 1 ELSE NULL END) as qtd_receitas,
              COUNT(CASE WHEN lf.tipo = 'despesa' THEN 1 ELSE NULL END) as qtd_despesas
              $sql_base";
$result_totais = $conn->query($sql_totais);
$totais = $result_totais->fetch_assoc();

$total_receitas = $totais['total_receitas'] ?? 0;
$total_despesas = $totais['total_despesas'] ?? 0;
$qtd_receitas = $totais['qtd_receitas'] ?? 0;
$qtd_despesas = $totais['qtd_despesas'] ?? 0;
$saldo = $total_receitas - $total_despesas;

// Preparar dados para os gráficos
$categorias_receitas = [];
$valores_receitas = [];
$cores_receitas = [];

$categorias_despesas = [];
$valores_despesas = [];
$cores_despesas = [];

if ($result_categorias && $result_categorias->num_rows > 0) {
    $result_categorias->data_seek(0);
    while ($row = $result_categorias->fetch_assoc()) {
        if ($row['tipo'] == 'receita') {
            $categorias_receitas[] = $row['nome'];
            $valores_receitas[] = floatval($row['total']);
            $cores_receitas[] = $row['cor'];
        } else {
            $categorias_despesas[] = $row['nome'];
            $valores_despesas[] = floatval($row['total']);
            $cores_despesas[] = $row['cor'];
        }
    }
}

// Converter arrays para formato JSON para uso nos gráficos
$categorias_receitas_json = json_encode($categorias_receitas);
$valores_receitas_json = json_encode($valores_receitas);
$cores_receitas_json = json_encode($cores_receitas);

$categorias_despesas_json = json_encode($categorias_despesas);
$valores_despesas_json = json_encode($valores_despesas);
$cores_despesas_json = json_encode($cores_despesas);
?>

<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2 class="mb-3">Relatório por Categorias</h2>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-lightbulb-fill me-2" style="font-size: 1.2em;"></i>
                <div>
                    <strong>Aqui você:</strong>Analisa como o dinheiro está sendo distribuído entre diferentes categorias de receitas e despesas.
                </div>
            </div>
         </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-primary" onclick="window.print()">
                <i class="bi bi-printer"></i> Imprimir
            </button>
        </div>
    </div>
    
    <!-- Filtros -->
    <div class="card mb-4 no-print">
        <div class="card-header">
            <i class="bi bi-funnel"></i> Filtros
        </div>
        <div class="card-body">
            <form method="GET" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="relatorio_categorias">
                
                <div class="col-md-3">
                    <label for="data_inicio" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?= $data_inicio ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="data_fim" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?= $data_fim ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="tipo" class="form-label">Tipo</label>
                    <select class="form-select" id="tipo" name="tipo">
                        <option value="todos" <?= $tipo == 'todos' ? 'selected' : '' ?>>Todos</option>
                        <option value="receita" <?= $tipo == 'receita' ? 'selected' : '' ?>>Receitas</option>
                        <option value="despesa" <?= $tipo == 'despesa' ? 'selected' : '' ?>>Despesas</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="campo_data" class="form-label">Campo de Data</label>
                    <select class="form-select" id="campo_data" name="campo_data">
                        <option value="vencimento" <?= $campo_data == 'vencimento' ? 'selected' : '' ?>>Vencimento</option>
                        <option value="pagamento" <?= $campo_data == 'pagamento' ? 'selected' : '' ?>>Pagamento</option>
                        <option value="lancamento" <?= $campo_data == 'lancamento' ? 'selected' : '' ?>>Lançamento</option>
                    </select>
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> Filtrar
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Resumo -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-info-circle"></i> Resumo do Período: <?= date('d/m/Y', strtotime($data_inicio)) ?> a <?= date('d/m/Y', strtotime($data_fim)) ?>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 text-center">
                    <h5 class="text-success">Total de Receitas</h5>
                    <h3>R$ <?= number_format($total_receitas, 2, ',', '.') ?></h3>
                    <small><?= $qtd_receitas ?> lançamentos</small>
                </div>
                <div class="col-md-4 text-center">
                    <h5 class="text-danger">Total de Despesas</h5>
                    <h3>R$ <?= number_format($total_despesas, 2, ',', '.') ?></h3>
                    <small><?= $qtd_despesas ?> lançamentos</small>
                </div>
                <div class="col-md-4 text-center">
                    <h5 class="<?= $saldo >= 0 ? 'text-primary' : 'text-warning' ?>">Saldo do Período</h5>
                    <h3>R$ <?= number_format($saldo, 2, ',', '.') ?></h3>
                    <small><?= $qtd_receitas + $qtd_despesas ?> lançamentos totais</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Gráficos -->
    <div class="row mb-4">
        <?php if ($tipo != 'despesa'): ?>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <i class="bi bi-pie-chart"></i> Receitas por Categoria
                </div>
                <div class="card-body">
                    <?php if (count($categorias_receitas) > 0): ?>
                        <div style="height: 300px;">
                            <canvas id="receitasChart"></canvas>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há dados de receitas para exibir no período selecionado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($tipo != 'receita'): ?>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <i class="bi bi-pie-chart"></i> Despesas por Categoria
                </div>
                <div class="card-body">
                    <?php if (count($categorias_despesas) > 0): ?>
                        <div style="height: 300px;">
                            <canvas id="despesasChart"></canvas>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há dados de despesas para exibir no período selecionado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Tabela de Dados -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table"></i> Detalhamento por Categoria
        </div>
        <div class="card-body">
            <?php if ($result_categorias && $result_categorias->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Categoria</th>
                                <th>Tipo</th>
                                <th class="text-end">Quantidade</th>
                                <th class="text-end">Valor Total</th>
                                <th class="text-end">Média por Lançamento</th>
                                <th class="text-end">% do Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $result_categorias->data_seek(0);
                            while ($row = $result_categorias->fetch_assoc()):
                                $percentual = 0;
                                if ($row['tipo'] == 'receita' && $total_receitas > 0) {
                                    $percentual = ($row['total'] / $total_receitas) * 100;
                                } elseif ($row['tipo'] == 'despesa' && $total_despesas > 0) {
                                    $percentual = ($row['total'] / $total_despesas) * 100;
                                }
                                
                                $media = $row['quantidade'] > 0 ? $row['total'] / $row['quantidade'] : 0;
                            ?>
                                <tr>
                                    <td>
                                        <span class="badge" style="background-color: <?= $row['cor'] ?>; width: 20px; height: 20px; display: inline-block; margin-right: 5px;"></span>
                                        <?= htmlspecialchars($row['nome']) ?>
                                    </td>
                                    <td>
                                        <span class="badge <?= $row['tipo'] == 'receita' ? 'bg-success' : 'bg-danger' ?>">
                                            <?= ucfirst($row['tipo']) ?>
                                        </span>
                                    </td>
                                    <td class="text-end"><?= $row['quantidade'] ?></td>
                                    <td class="text-end <?= $row['tipo'] == 'receita' ? 'text-success' : 'text-danger' ?>">
                                        R$ <?= number_format($row['total'], 2, ',', '.') ?>
                                    </td>
                                    <td class="text-end">
                                        R$ <?= number_format($media, 2, ',', '.') ?>
                                    </td>
                                    <td class="text-end">
                                        <?= number_format($percentual, 2, ',', '.') ?>%
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-dark">
                                <th colspan="2">Total</th>
                                <th class="text-end"><?= $qtd_receitas + $qtd_despesas ?></th>
                                <th class="text-end">R$ <?= number_format($total_receitas + $total_despesas, 2, ',', '.') ?></th>
                                <th class="text-end">-</th>
                                <th class="text-end">100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    Não há dados para exibir no período selecionado.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Scripts para os gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de Receitas por Categoria
    const ctxReceitas = document.getElementById('receitasChart');
    if (ctxReceitas && <?= count($categorias_receitas) > 0 ? 'true' : 'false' ?>) {
        new Chart(ctxReceitas, {
            type: 'doughnut',
            data: {
                labels: <?= $categorias_receitas_json ?>,
                datasets: [{
                    data: <?= $valores_receitas_json ?>,
                    backgroundColor: <?= $cores_receitas_json ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Gráfico de Despesas por Categoria
    const ctxDespesas = document.getElementById('despesasChart');
    if (ctxDespesas && <?= count($categorias_despesas) > 0 ? 'true' : 'false' ?>) {
        new Chart(ctxDespesas, {
            type: 'doughnut',
            data: {
                labels: <?= $categorias_despesas_json ?>,
                datasets: [{
                    data: <?= $valores_despesas_json ?>,
                    backgroundColor: <?= $cores_despesas_json ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>