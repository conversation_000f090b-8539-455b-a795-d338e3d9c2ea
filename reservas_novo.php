<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
	$user_id = $_SESSION['user_id'];
	$pousada_id = $_SESSION['user_pousada_id'];
	$is_admin = $_SESSION['user_is_admin'];
} else {
	echo "<script>window.location.href = 'index.html';</script>";
	return;
}

include_once("config.php");

echo '<center>';
echo '<h1>Pousada Bom Viver</h1>';
echo '<br>';
echo '<button onclick="location.href=\'index.php\';" class="btn btn-success">Home</button>';
echo '</center>';

$hospede_id = $_GET['hospede_id'] ?? null;
$hospede_nome = $_GET['hospede_nome'] ?? null;
$data = $_GET['data'] ?? null;
$uh = $_GET['uh'] ?? null;

// Processar datas se fornecidas
if ($data) {
    $dataInicial = DateTime::createFromFormat('d/m/Y', $data);
    if ($dataInicial !== false) {
        $dataFinal = clone $dataInicial;
        $dataFinal->modify('+1 day');
        $dataInicialFormatada = $dataInicial->format('Y-m-d');
        $dataFinalFormatada = $dataFinal->format('Y-m-d');
    } else {
        $dataInicial = new DateTime();
        $dataFinal = clone $dataInicial;
        $dataFinal->modify('+1 day');
        $dataInicialFormatada = $dataInicial->format('Y-m-d');
        $dataFinalFormatada = $dataFinal->format('Y-m-d');
        error_log("Formato de data inválido: $data. Usando data atual como fallback.");
    }
} else {
    $dataInicialFormatada = '';    
    $dataFinalFormatada = '';
}

// Verificar se é um cadastro rápido
$cadastro_rapido = isset($_GET['cadastro_rapido']) && $_GET['cadastro_rapido'] == 1;

// Se for cadastro rápido, exibir mensagem
if ($cadastro_rapido) {
    echo '<div class="alert alert-info alert-dismissible fade show" role="alert">
        <strong>Atenção!</strong> O hóspede foi cadastrado com dados mínimos. 
        Lembre-se de complementar o cadastro após finalizar a reserva.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
    </div>';
}

// CONFIGURAÇÃO PARA FORMULÁRIO REUTILIZÁVEL
// Contexto: Via index.php (não AJAX)
$form_action = '?page=reservas_salvar';
$hidden_fields = '<input type="hidden" name="acao" value="cadastrar">
                  <input type="hidden" name="hospede_id" value="' . htmlspecialchars($hospede_id) . '">
                  <input type="hidden" name="hospede_nome" value="' . htmlspecialchars($hospede_nome) . '">';
$form_id = 'reservas';

// Valores pré-preenchidos
$dataentrada = $dataInicialFormatada;
$datasaida = $dataFinalFormatada;
$titulo_reserva = 'Nova Reserva para:'; // Adicionar esta linha

// Incluir o formulário reutilizável
include 'formulario_reserva.php';
?>

<?php 
//Precisa ficar fora do form, se não gera uma inserção vazia no banco
echo '<center><button onclick="location.href=\'index.php\'" class="btn btn-cancelar">Cancelar</button></center>'; 
?>

<script src="custom/js/func.js"></script>
<script>
	document.addEventListener('DOMContentLoaded', function() {
		const uhField = document.querySelector('input[name="uh"]');
		if (uhField) {
			uhField.addEventListener('blur', function() {
				this.value = this.value.padStart(3, '0');
			});
		}
	});

	document.addEventListener('DOMContentLoaded', function() {
		const dataEntradaInput = document.querySelector('input[name="dataentrada"]');
		const dataSaidaInput = document.querySelector('input[name="datasaida"]');
		const horaEntradaInput = document.querySelector('input[name="horaentrada"]');
		const horaSaidaInput = document.querySelector('input[name="horasaida"]');
		const uhInput = document.querySelector('input[name="uh"]');
		
		// Criar elemento para o aviso de data retroativa
		const avisoRetroativo = document.createElement('div');
		avisoRetroativo.className = 'alert alert-warning alert-dismissible fade show';
		avisoRetroativo.style.position = 'absolute';
		avisoRetroativo.style.zIndex = '100';
		avisoRetroativo.style.marginTop = '5px';
		avisoRetroativo.style.width = '250px';
		avisoRetroativo.style.fontSize = '12px';
		avisoRetroativo.style.padding = '5px';
		avisoRetroativo.style.display = 'none';
		// Adicionar tabindex=-1 para que o alerta não entre na ordem de tabulação
		avisoRetroativo.setAttribute('tabindex', '-1');
		avisoRetroativo.innerHTML = `
			<strong>Atenção!</strong> Data retroativa.
			<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar" style="padding: 2px; font-size: 10px;" tabindex="-1"></button>
		`;
		
		// Inserir o aviso após o campo de data de entrada
		dataEntradaInput.parentNode.appendChild(avisoRetroativo);
		
		// Criar elemento para o aviso de data de saída inválida
		const avisoDataSaida = document.createElement('div');
		avisoDataSaida.className = 'alert alert-danger alert-dismissible fade show';
		avisoDataSaida.style.position = 'absolute';
		avisoDataSaida.style.zIndex = '100';
		avisoDataSaida.style.marginTop = '5px';
		avisoDataSaida.style.width = '250px';
		avisoDataSaida.style.fontSize = '12px';
		avisoDataSaida.style.padding = '5px';
		avisoDataSaida.style.display = 'none';
		// Adicionar tabindex=-1 para que o alerta não entre na ordem de tabulação
		avisoDataSaida.setAttribute('tabindex', '-1');
		avisoDataSaida.innerHTML = `
			<strong>Erro!</strong> Data de saída deve ser posterior à data de entrada.
			<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar" style="padding: 2px; font-size: 10px;" tabindex="-1"></button>
		`;
		
		// Inserir o aviso após o campo de data de saída
		dataSaidaInput.parentNode.appendChild(avisoDataSaida);
		
		// Função para verificar se a data é retroativa
		function verificarDataRetroativa() {
			if (!dataEntradaInput.value) return;
			
			// Obter a data de entrada como string no formato YYYY-MM-DD
			const dataEntradaStr = dataEntradaInput.value;
			
			// Obter a data atual como string no formato YYYY-MM-DD
			const hoje = new Date();
			const hojeStr = hoje.getFullYear() + '-' + 
						String(hoje.getMonth() + 1).padStart(2, '0') + '-' + 
						String(hoje.getDate()).padStart(2, '0');
			
			// Comparar as strings de data diretamente
			if (dataEntradaStr < hojeStr) {
				// Mostrar aviso para data retroativa
				avisoRetroativo.style.display = 'block';
				
				// Adicionar classe para destacar o campo
				dataEntradaInput.classList.add('border-warning');
				dataEntradaInput.style.backgroundColor = 'rgba(255, 193, 7, 0.1)';
			} else {
				// Esconder aviso e remover destaque
				avisoRetroativo.style.display = 'none';
				dataEntradaInput.classList.remove('border-warning');
				dataEntradaInput.style.backgroundColor = '';
			}
			
			// Verificar a data de saída também, caso já tenha sido preenchida
			if (dataSaidaInput.value) {
				verificarDataSaida();
			}
		}
		
		// Função para verificar se a data de saída é válida
		function verificarDataSaida() {
			if (!dataEntradaInput.value || !dataSaidaInput.value) return;
			
			// Obter as datas como strings no formato YYYY-MM-DD
			const dataEntradaStr = dataEntradaInput.value;
			const dataSaidaStr = dataSaidaInput.value;
			
			// Comparar as strings de data diretamente
			if (dataSaidaStr < dataEntradaStr) {
				// Mostrar aviso para data de saída inválida
				avisoDataSaida.style.display = 'block';
				
				// Adicionar classe para destacar o campo
				dataSaidaInput.classList.add('border-danger');
				dataSaidaInput.style.backgroundColor = 'rgba(220, 53, 69, 0.1)';
			} else {
				// Esconder aviso e remover destaque
				avisoDataSaida.style.display = 'none';
				dataSaidaInput.classList.remove('border-danger');
				dataSaidaInput.style.backgroundColor = '';
				
				// Se as datas forem iguais, ajustar a hora de saída
				if (dataEntradaStr === dataSaidaStr) {
					ajustarHoraSaida();
				}
			}
		}
		
		// Função para ajustar a hora de saída quando as datas são iguais
		function ajustarHoraSaida() {
			if (dataEntradaInput.value === dataSaidaInput.value) {
				if (horaEntradaInput.value) {
					// Pegar a hora de entrada
					const [horasEntrada, minutosEntrada] = horaEntradaInput.value.split(':').map(Number);
					
					// Adicionar 2 horas
					let novasHoras = horasEntrada + 2;
					let novosMinutos = minutosEntrada;
					
					// Se passar de 23h, ajustar para 23:59
					if (novasHoras > 23) {
						novasHoras = 23;
						novosMinutos = 59;
					}
					
					// Formatar a nova hora
					const novaHoraSaida = `${String(novasHoras).padStart(2, '0')}:${String(novosMinutos).padStart(2, '0')}`;
					
					// Definir a nova hora de saída
					horaSaidaInput.value = novaHoraSaida;
				}
			}
		}
		
		// Verificar ao carregar a página (caso já venha com datas preenchidas)
		if (dataEntradaInput.value) {
			verificarDataRetroativa();
		}
		if (dataSaidaInput.value && dataEntradaInput.value) {
			verificarDataSaida();
		}
		
		// Verificar quando o usuário selecionar uma data de entrada
		dataEntradaInput.addEventListener('change', function() {
			verificarDataRetroativa();
			
			// Se a data de entrada for alterada e for posterior à data de saída,
			// atualizar a data de saída para o mesmo dia
			if (dataSaidaInput.value && dataSaidaInput.value < dataEntradaInput.value) {
				dataSaidaInput.value = dataEntradaInput.value;
				// Remover destaque de erro
				dataSaidaInput.classList.remove('border-danger');
				dataSaidaInput.style.backgroundColor = '';
				avisoDataSaida.style.display = 'none';
				
				// Ajustar a hora de saída já que as datas são iguais
				ajustarHoraSaida();
			} else if (dataSaidaInput.value && dataSaidaInput.value === dataEntradaInput.value) {
				// Se as datas já eram iguais, ajustar a hora de saída
				ajustarHoraSaida();
			}
		});
		
		// Verificar quando o usuário selecionar uma data de saída
		dataSaidaInput.addEventListener('change', verificarDataSaida);
		
		// Quando o usuário sair do campo de data de saída
		dataSaidaInput.addEventListener('blur', function() {
			// Esconder o aviso
			avisoDataSaida.style.display = 'none';
			
			// Se a data de saída for menor que a data de entrada, corrigir automaticamente
			if (dataEntradaInput.value && dataSaidaInput.value) {
				if (dataSaidaInput.value < dataEntradaInput.value) {
					dataSaidaInput.value = dataEntradaInput.value;
					dataSaidaInput.classList.remove('border-danger');
					dataSaidaInput.style.backgroundColor = '';
					
					// Ajustar a hora de saída já que as datas são iguais
					ajustarHoraSaida();
				}
			}
		});
		
		// Quando o usuário alterar a hora de entrada
		horaEntradaInput.addEventListener('change', function() {
			// Se as datas forem iguais, ajustar a hora de saída
			if (dataEntradaInput.value && dataSaidaInput.value && 
				dataEntradaInput.value === dataSaidaInput.value) {
				ajustarHoraSaida();
			}
		});
		
		// Esconder o aviso de data retroativa quando o usuário sair do campo
		dataEntradaInput.addEventListener('blur', function() {
			avisoRetroativo.style.display = 'none';
		});
		
		// Garantir que o tab funcione corretamente após o campo de data de entrada
		dataEntradaInput.addEventListener('keydown', function(event) {
			if (event.key === 'Tab' && !event.shiftKey) {
				// Se o usuário pressionar Tab, garantir que o foco vá para o campo de hora de entrada
				if (avisoRetroativo.style.display === 'block') {
					event.preventDefault();
					horaEntradaInput.focus();
				}
			}
		});
		
		// Garantir que o tab funcione corretamente após o campo de data de saída
		dataSaidaInput.addEventListener('keydown', function(event) {
			if (event.key === 'Tab' && !event.shiftKey) {
				// Se o usuário pressionar Tab, garantir que o foco vá para o campo de hora de saída
				if (avisoDataSaida.style.display === 'block') {
					event.preventDefault();
					horaSaidaInput.focus();
				}
			}
		});
		
		// Mostrar novamente os avisos se os campos tiverem foco e houver problemas
		dataEntradaInput.addEventListener('focus', function() {
			if (dataEntradaInput.classList.contains('border-warning')) {
				avisoRetroativo.style.display = 'block';
			}
		});
		
		dataSaidaInput.addEventListener('focus', function() {
			if (dataSaidaInput.classList.contains('border-danger')) {
				avisoDataSaida.style.display = 'block';
			}
		});
		
		// Verificar também quando o usuário clicar no botão de enviar
		// ADICIONAR: Função para verificar conflitos de reserva
		function verificarConflitosNovaReserva() {
			if (!uhInput.value || !dataEntradaInput.value) return;
			
			// Remover avisos anteriores
			removerAvisoConflito(uhInput);
			removerAvisoConflito(dataEntradaInput);
			
			const formData = new FormData();
			formData.append('uh', uhInput.value);
			formData.append('dataEntrada', dataEntradaInput.value);
			formData.append('horaEntrada', horaEntradaInput.value || '13:00');
			
			fetch('verificar_disponibilidade.php', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				if (!data.disponivel) {
					mostrarAvisoConflito(dataEntradaInput, data.mensagem);
				}
			})
			.catch(error => {
				console.error('5_Erro ao verificar disponibilidade:', error);
			});
		}
		
		// ADICIONAR: Event listeners para verificação de conflitos
		if (dataEntradaInput) {
			dataEntradaInput.addEventListener('change', function() {
				verificarDataRetroativa();
				verificarConflitosNovaReserva();
				
				// Se a data de entrada for alterada e for posterior à data de saída,
				// atualizar a data de saída para o mesmo dia
				if (dataSaidaInput.value && dataSaidaInput.value < dataEntradaInput.value) {
					dataSaidaInput.value = dataEntradaInput.value;
					// Remover destaque de erro
					dataSaidaInput.classList.remove('border-danger');
					dataSaidaInput.style.backgroundColor = '';
					avisoDataSaida.style.display = 'none';
					
					// Ajustar a hora de saída já que as datas são iguais
					ajustarHoraSaida();
				} else if (dataSaidaInput.value && dataSaidaInput.value === dataEntradaInput.value) {
					// Se as datas já eram iguais, ajustar a hora de saída
					ajustarHoraSaida();
				}
			});
		}
		
		if (uhInput) {
			uhInput.addEventListener('change', verificarConflitosNovaReserva);
			uhInput.addEventListener('blur', verificarConflitosNovaReserva);
		}
		
		if (horaEntradaInput) {
			horaEntradaInput.addEventListener('change', verificarConflitosNovaReserva);
		}
		
		// MODIFICAR: Verificação no envio do formulário
		document.querySelector('form#reservas').addEventListener('submit', function(event) {
			let temErro = false;
			
			// Verificar se há avisos de conflito visíveis
			const avisosConflito = document.querySelectorAll('.aviso-conflito');
			if (avisosConflito.length > 0) {
				alert('Existe um conflito de reserva. Por favor, escolha outra data ou UH antes de continuar.');
				event.preventDefault();
				dataEntradaInput.focus();
				return false;
			}
			
			// Verificar data retroativa
			if (dataEntradaInput.value) {
				const dataEntradaStr = dataEntradaInput.value;
				const hoje = new Date();
				const hojeStr = hoje.getFullYear() + '-' + 
							String(hoje.getMonth() + 1).padStart(2, '0') + '-' + 
							String(hoje.getDate()).padStart(2, '0');
				
				if (dataEntradaStr < hojeStr) {
					if (!confirm('Você está criando uma reserva com data retroativa. Deseja continuar?')) {
						event.preventDefault();
						temErro = true;
					}
				}
			}
			
			// Verificar data de saída
			if (dataEntradaInput.value && dataSaidaInput.value) {
				if (dataSaidaInput.value < dataEntradaInput.value) {
					alert('A data de saída não pode ser anterior à data de entrada. Por favor, corrija antes de continuar.');
					event.preventDefault();
					temErro = true;
					dataSaidaInput.focus();
				}
			}
		});
	});



</script>
