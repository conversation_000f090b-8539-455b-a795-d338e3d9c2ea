// Adicionar esta linha no início do arquivo para transformá-lo em um módulo
export { };

// Importar DateTimeValidator do módulo
import { DateTimeValidator } from './valida_data_anterior.js';
// Importar função de verificação de disponibilidade
import { verificarDisponibilidadeUHMelhorada } from './verificacao-disponibilidade-local.js';
// Importar gerenciador de tooltips
import { tooltipManager } from './tooltip-manager.js';

// Declaração de módulo para estender interfaces globais
declare global {
    interface Window {
        ModalReserva: typeof ModalReserva;
    }
}

// Interfaces para as respostas da API
interface ResponseData {
    success: boolean;
    html?: string;
    message?: string;
}

interface DisponibilidadeResponse {
    disponivel: boolean;
    mensagem?: string;
}

// Declaração das funções externas do func.js
declare function verificarDisponibilidadeUH(
    uh: string,
    dataEntrada: string,
    horaEntrada: string,
    callback: (data: DisponibilidadeResponse | null, error: string | null) => void
): void;

// Declaração para o Bootstrap (apenas uma vez)
declare namespace bootstrap {
    class Modal {
        constructor(element: HTMLElement | null, options?: any);
        static getInstance(element: HTMLElement | null): Modal | null;
        show(): void;
        hide(): void;
    }
}

class ModalReserva {
    private modalReserva: bootstrap.Modal | null;
    private container: HTMLElement | null;
    private dateValidator: DateTimeValidator;

    constructor() {
        this.modalReserva = null;
        this.container = null;
        this.dateValidator = new DateTimeValidator();
    }

    carregarFormularioReserva(uh: string, data: string, hospedeId: string | { id: string, nome: string }, hospedeNome?: string): void {
        this.modalReserva = new bootstrap.Modal(document.getElementById('novaReservaModal'));
        this.container = document.getElementById('formularioReservaContainer');

        this.updateModalHeader(uh, data, typeof hospedeId === 'object' ? hospedeId.nome : hospedeNome);
        this.modalReserva.show();

        this.loadFormulario(uh, data, hospedeId, hospedeNome);
    }

    private updateModalHeader(uh: string, data: string, hospedeNome?: string): void {
        const modalUhElement = document.getElementById('modalUhReserva');
        const modalDataElement = document.getElementById('modalDataReserva');
        const modalHospedeElement = document.getElementById('modalHospedeReserva');

        if (modalUhElement) modalUhElement.textContent = uh;
        if (modalDataElement) modalDataElement.textContent = data;
        if (modalHospedeElement) modalHospedeElement.textContent = hospedeNome || 'Reserva Direta';
    }

    private loadFormulario(uh: string, data: string, hospedeId: string | { id: string, nome: string }, hospedeNome?: string): void {
        // Verificar se hospedeId é um objeto (novo formato) ou um ID simples (formato antigo)
        let realHospedeId: string;
        let realHospedeNome: string | undefined;

        if (typeof hospedeId === 'object' && hospedeId !== null) {
            // Novo formato: hospede é um objeto { id, nome }
            realHospedeId = hospedeId.id;
            realHospedeNome = hospedeId.nome;
        } else {
            // Formato antigo: hospedeId e hospedeNome são valores separados
            realHospedeId = hospedeId;
            realHospedeNome = hospedeNome;
        }

        // Criar FormData para enviar o nome do hóspede
        const formData = new FormData();
        if (realHospedeNome) {
            formData.append('hospede_nome', realHospedeNome);
        }

        fetch('carregar_formulario_reserva.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then((responseData: ResponseData) => {
                if (responseData.success && this.container && responseData.html) {
                    this.container.innerHTML = responseData.html;
                    setTimeout(() => {
                        this.setupForm(uh, data, hospedeId, hospedeNome);
                    }, 100);
                } else if (this.container) {
                    this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                if (this.container) {
                    this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
                }
            });
    }

    private setupForm(uh: string, data: string, hospedeId: string | { id: string, nome: string }, hospedeNome?: string): void {
        this.preencherCampos(uh, data, hospedeId, hospedeNome);
        this.setupFormValidation();
        this.setupDisponibilidadeCheck();
    }

    private preencherCampos(uh: string, data: string, hospedeId: string | { id: string, nome: string }, hospedeNome?: string): void {
        // Verificar se hospedeId é um objeto (novo formato) ou um ID simples (formato antigo)
        let realHospedeId: string;
        let realHospedeNome: string | undefined;

        if (typeof hospedeId === 'object' && hospedeId !== null) {
            // Novo formato: hospede é um objeto { id, nome }
            realHospedeId = hospedeId.id;
            realHospedeNome = hospedeId.nome;
        } else {
            // Formato antigo: hospedeId e hospedeNome são valores separados
            realHospedeId = hospedeId;
            realHospedeNome = hospedeNome;
        }

        // Preencher campos hidden
        const hospedeIdField = document.getElementById('hospede_id_hidden') as HTMLInputElement | null;
        const hospedeNomeField = document.getElementById('hospede_nome_hidden') as HTMLInputElement | null;
        const uhField = document.getElementById('uh_selecionada_hidden') as HTMLInputElement | null;
        const dataField = document.getElementById('data_selecionada_hidden') as HTMLInputElement | null;

        if (hospedeIdField) hospedeIdField.value = realHospedeId || '';
        if (hospedeNomeField) hospedeNomeField.value = realHospedeNome || '';
        if (uhField) uhField.value = uh;
        if (dataField) dataField.value = data;

        // Preencher campo UH no formulário
        const uhFormField = document.querySelector('input[name="uh"]') as HTMLInputElement | null;
        if (uhFormField) {
            uhFormField.value = uh;

            // Adicionar formatação com zeros à esquerda
            uhFormField.addEventListener('blur', function () {
                this.value = this.value.padStart(3, '0');
            });
        }

        // Processar data para formato YYYY-MM-DD se necessário
        const dataFormatada = this.converterDataParaISO(data);
        const dataEntradaField = document.querySelector('input[name="dataentrada"]') as HTMLInputElement | null;
        if (dataEntradaField && dataFormatada) {
            dataEntradaField.value = dataFormatada;

            // Calcular data de saída (próximo dia)
            const dataSaida = new Date(dataFormatada);
            dataSaida.setDate(dataSaida.getDate() + 1);
            const dataSaidaFormatada = dataSaida.toISOString().split('T')[0];

            const dataSaidaField = document.querySelector('input[name="datasaida"]') as HTMLInputElement | null;
            if (dataSaidaField) dataSaidaField.value = dataSaidaFormatada;
        }

        // Carregar script func.js para funcionalidades do formulário
        if (!document.querySelector('script[src="custom/js/func.js"]')) {
            const script = document.createElement('script');
            script.src = 'custom/js/func.js';
            document.head.appendChild(script);
        }
    }

    private setupFormValidation(): void {
        const form = document.getElementById('formReservaCompleto') as HTMLFormElement | null;
        if (form && this.container) {
            form.addEventListener('submit', (e) => {
                // Verificar se há conflitos antes de enviar
                const avisosConflito = this.container?.querySelectorAll('.aviso-conflito') ?? [];
                if (avisosConflito.length > 0) {
                    e.preventDefault();
                    alert('Não é possível salvar a reserva devido a conflitos de disponibilidade.');
                    return false;
                }

                e.preventDefault();
                this.salvarReservaCompleta(form);
            });
        }
    }

    private setupDisponibilidadeCheck(): void {
        // Configurar verificação de disponibilidade nos campos
        const uhFormField = document.querySelector('input[name="uh"]') as HTMLInputElement | null;
        const dataEntradaField = document.querySelector('input[name="dataentrada"]') as HTMLInputElement | null;
        const horaEntradaField = document.querySelector('input[name="horaentrada"]') as HTMLInputElement | null;
        const dataSaidaField = document.querySelector('input[name="datasaida"]') as HTMLInputElement | null;
        const horaSaidaField = document.querySelector('input[name="horasaida"]') as HTMLInputElement | null;

        if (uhFormField && dataEntradaField) {
            // Adicionar verificação quando os campos mudarem
            [uhFormField, dataEntradaField, horaEntradaField, dataSaidaField, horaSaidaField].forEach(field => {
                if (field) {
                    field.addEventListener('change', () => {
                        this.verificarDisponibilidadeModal();
                    });
                    field.addEventListener('blur', () => {
                        this.verificarDisponibilidadeModal();
                    });
                }
            });

            // Verificar disponibilidade imediatamente após carregar
            setTimeout(() => {
                this.verificarDisponibilidadeModal();
            }, 200);
        }

        // Inicializar validações de data no quadro único
        setTimeout(() => {
            this.inicializarValidacoesDatas();
        }, 300);
    }

    verificarDisponibilidadeModal(): void {
        // Função removida - não há mais validações visuais de conflito
        this.ocultarAvisoUnico();
    }

    private processarResultadoVerificacao(data: any, error: string | null, dataField: HTMLInputElement): void {
        if (error) {
            console.error('Erro na verificação:', error);
            return;
        }

        if (data && !data.disponivel && this.container) {
            // Mostrar aviso no quadro único
            this.mostrarAvisoUnico(data.mensagem || 'UH não disponível no período.');
        } else {
            // Ocultar aviso se não há conflito
            this.ocultarAvisoUnico();
        }
    }

    private mostrarAvisoUnico(mensagem: string): void {
        const avisoElement = document.getElementById('avisoValidacao');
        const mensagemElement = document.getElementById('mensagemAviso');

        if (avisoElement && mensagemElement) {
            mensagemElement.textContent = mensagem;
            avisoElement.style.display = 'block';
        }
    }

    private ocultarAvisoUnico(): void {
        const avisoElement = document.getElementById('avisoValidacao');
        if (avisoElement) {
            avisoElement.style.display = 'none';
        }
    }

    private inicializarValidacoesDatas(): void {
        const dataEntradaField = document.querySelector('input[name="dataentrada"]') as HTMLInputElement;
        const dataSaidaField = document.querySelector('input[name="datasaida"]') as HTMLInputElement;

        if (dataEntradaField) {
            dataEntradaField.addEventListener('blur', () => this.validarDatas());
        }

        if (dataSaidaField) {
            dataSaidaField.addEventListener('blur', () => this.validarDatas());
        }
    }

    private validarDatas(): void {
        const dataEntradaField = document.querySelector('input[name="dataentrada"]') as HTMLInputElement;
        const dataSaidaField = document.querySelector('input[name="datasaida"]') as HTMLInputElement;

        if (!dataEntradaField || !dataSaidaField) return;

        const dataEntrada = dataEntradaField.value;
        const dataSaida = dataSaidaField.value;
        const mensagens: string[] = [];

        // Verificar se data entrada é anterior a hoje
        if (dataEntrada) {
            const hoje = new Date();
            const hojeStr = hoje.getFullYear() + '-' +
                String(hoje.getMonth() + 1).padStart(2, '0') + '-' +
                String(hoje.getDate()).padStart(2, '0');

            if (dataEntrada < hojeStr) {
                mensagens.push('Data de entrada é anterior à data atual (reserva retroativa)');
            }
        }

        // Verificar se data saída é anterior à data entrada
        if (dataEntrada && dataSaida && dataSaida < dataEntrada) {
            mensagens.push('Data de saída não pode ser anterior à data de entrada');
        }

        // Mostrar mensagens cumulativas ou ocultar se não há erros
        if (mensagens.length > 0) {
            this.mostrarAvisoUnico(mensagens.join(' • '));
        } else {
            this.ocultarAvisoUnico();
        }
    }

    public salvarReservaCompleta(form: HTMLFormElement): void {
        const formData = new FormData(form);

        fetch('reservas_salvar.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.text())
            .then(text => {
                // Verificar se a resposta está vazia
                if (!text.trim()) {
                    throw new Error('Resposta vazia do servidor');
                }

                // Tentar fazer parse do JSON
                let data: ResponseData;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    // Se não for JSON, considerar como sucesso (compatibilidade)
                    alert('Reserva criada com sucesso!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('novaReservaModal'));
                    if (modal) modal.hide();
                    // Recarregar página para atualizar o mapa
                    setTimeout(() => location.reload(), 500);
                    return;
                }

                if (data.success) {
                    alert('Reserva criada com sucesso!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('novaReservaModal'));
                    if (modal) modal.hide();
                    // Recarregar página para atualizar o mapa
                    setTimeout(() => location.reload(), 500);
                } else {
                    alert('Erro ao salvar reserva: ' + (data.message || 'Erro desconhecido'));
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao processar a requisição: ' + error.message);
            });
    }

    // Função para remover avisos de conflito anteriores
    private removerAvisosConflito(): void {
        // Remover tooltips ativos
        if (this.container) {
            const campos = this.container.querySelectorAll('input[data-bs-toggle="tooltip"]');
            campos.forEach(campo => {
                tooltipManager.hideTooltip(campo as HTMLElement);
            });
        }

        // Remover avisos antigos (compatibilidade)
        const avisosAnteriores = document.querySelectorAll('.aviso-conflito');
        avisosAnteriores.forEach(aviso => aviso.remove());
    }

    // Função auxiliar para converter data DD/MM/YYYY para YYYY-MM-DD
    converterDataParaISO(dataBrasileira: string): string | null {
        if (!dataBrasileira) return null;
        const partes = dataBrasileira.split('/');
        if (partes.length === 3) {
            return `${partes[2]}-${partes[1].padStart(2, '0')}-${partes[0].padStart(2, '0')}`;
        }
        return null;
    }
}

// Exportar para uso global
// Remover esta linha:
// window.ModalReserva = ModalReserva;
export { ModalReserva };