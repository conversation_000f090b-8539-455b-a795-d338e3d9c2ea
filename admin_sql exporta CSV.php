<?php
// Disable error display for AJAX requests to prevent JSON parsing issues
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
// Incluir conexão com o banco de dados
include_once("config.php");

// Verificar se o usuário é super administrador PRIMEIRO
if (!isset($_SESSION['user_pousada_id']) || $_SESSION['user_pousada_id'] != 0) {
    // Para requisições AJAX, retornar JSON de erro
    if (isset($_POST['action'])) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Acesso negado. Apenas super administradores podem acessar esta funcionalidade.']);
        exit;
    }
    // Para carregamento da página, mostrar mensagem HTML
    echo "<div class='alert alert-danger'>Acesso negado. Apenas super administradores podem acessar esta página.</div>";
    exit;
}

// Processar solicitação para limpar histórico
if (isset($_POST['action'])) {
    
    if ($_POST['action'] === 'clear_history') {  
      $delete_stmt = $conn->prepare("DELETE FROM sql_query_history WHERE pousada_id = 0");  
    }

    // Processar solicitação para excluir consultas
    if (isset($_POST['action'])) {
              
        if ($_POST['action'] === 'delete_query' && isset($_POST['query_id'])) {  
            $query_id = $_POST['query_id']; 
            $delete_stmt = $conn->prepare("DELETE FROM sql_query_history WHERE id = ? AND pousada_id = 0");
            $delete_stmt->bind_param("i", $query_id);        
        }   
     
        if (isset($delete_stmt) && $delete_stmt->execute()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Excluído com sucesso']);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Erro ao excluir']);
        }
        exit;
    }
    exit;
}


// Verificar se o usuário é super administrador (para o resto da página)
if (!isset($_SESSION['user_pousada_id']) || $_SESSION['user_pousada_id'] != 0) {
    echo "<div class='alert alert-danger'>Acesso negado. Apenas super administradores podem acessar esta página.</div>";
    exit;
}

// Inicializar variáveis
$query = '';
$result = null;
$error = '';
$affected_rows = 0;
$execution_time = 0;
$tables = [];

// Carregar histórico de consultas do banco de dados para usuários com pousada_id=0
function loadQueryHistory($conn) {
    $history_stmt = $conn->prepare("
        SELECT id, query_text, executed_at, execution_time, affected_rows 
        FROM sql_query_history 
        WHERE pousada_id = 0 
        ORDER BY executed_at DESC 
        LIMIT 20
    ");
    $history_stmt->execute();
    $result = $history_stmt->get_result();
    
    $query_history = [];
    while ($row = $result->fetch_assoc()) {
        $query_history[] = $row;
    }
    
    return $query_history;
}

// Salvar consulta no histórico do banco de dados
function saveQueryToHistory($conn, $user_id, $query, $execution_time, $affected_rows) {
    $save_stmt = $conn->prepare("
        INSERT INTO sql_query_history (user_id, pousada_id, query_text, execution_time, affected_rows) 
        VALUES (?, 0, ?, ?, ?)
    ");
    $save_stmt->bind_param("isdi", $user_id, $query, $execution_time, $affected_rows);
    $save_stmt->execute();
    
    // Manter apenas os últimos 100 registros por usuário
    $cleanup_stmt = $conn->prepare("
        DELETE FROM sql_query_history 
        WHERE pousada_id = 0 AND user_id = ? 
        AND id NOT IN (
            SELECT id FROM (
                SELECT id FROM sql_query_history 
                WHERE pousada_id = 0 AND user_id = ? 
                ORDER BY executed_at DESC 
                LIMIT 250
            ) AS recent
        )
    ");
    $cleanup_stmt->bind_param("ii", $user_id, $user_id);
    $cleanup_stmt->execute();
}

$query_history = loadQueryHistory($conn);

// Obter lista de tabelas para o autocomplete
$tables_query = $conn->query("SHOW TABLES");
while ($table = $tables_query->fetch_array()) {
    $tables[] = $table[0];
}

// Processar consulta SQL
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['query'])) {
    $query = trim($_POST['query']);
    
    if (!empty($query)) {
        // Medir tempo de execução
        $start_time = microtime(true);
        
        try {
            // Executar a consulta
            $result = $conn->query($query);
            
            if ($result === false) {
                $error = "Erro na consulta: " . $conn->error;
            } else {
                // Verificar se é uma consulta SELECT ou uma operação de modificação
                if ($result instanceof mysqli_result) {
                    // É um SELECT, obter os resultados
                    $affected_rows = $result->num_rows;
                } else {
                    // É uma operação de modificação (INSERT, UPDATE, DELETE)
                    $affected_rows = $conn->affected_rows;
                }
            }
        } catch (Exception $e) {
            $error = "Exceção: " . $e->getMessage();
        }
        
        $execution_time = microtime(true) - $start_time;
        
        // Salvar no histórico apenas se não houve erro e o checkbox estiver marcado
        if (empty($error) && isset($_POST['save_query'])) {
            saveQueryToHistory($conn, $_SESSION['user_id'] ?? 0, $query, $execution_time, $affected_rows);
            // Recarregar histórico
            $query_history = loadQueryHistory($conn);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor SQL - Super Administrador</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/externo.css">
    <link rel="stylesheet" href="custom/css/admin-financeiro.css">
    <!-- Adicionar Font Awesome para os ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>

<div class="container-fluid mt-4">
    <h2>Editor SQL - Super Administrador</h2>
    
    <form method="post" action="" class="mb-3">
        <div class="row">
            <div class="col-12">
                <label for="sqlQuery" class="form-label">Consulta SQL:</label>
                <textarea class="form-control" id="sqlQuery" name="query" rows="3" ><?php echo htmlspecialchars($query); ?></textarea>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-12">
                <button type="submit" class="btn btn-primary">Executar</button>
                <button type="button" class="btn btn-secondary" id="clearBtn">Limpar</button>
                <div class="form-check form-check-inline ms-2">
                    <input class="form-check-input" type="checkbox" id="saveQueryCheckbox" name="save_query" checked>
                    <label class="form-check-label" for="saveQueryCheckbox">Salvar SQL</label>
                </div>
            </div>
        </div>
    </form>
    
    <!-- Histórico de Consultas, Tabelas e Campos da tabela - Alinhados horizontalmente -->
    <div class="row mb-3">
        <!-- Tabelas - Largura reduzida -->
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">Tabelas</h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" style="height: 200px; overflow-y: auto;">
                        <?php foreach ($tables as $table): ?>
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-link p-0 m-0 text-start text-decoration-none table-btn" data-table="<?php echo $table; ?>" style="flex-grow: 1; overflow: hidden; text-overflow: ellipsis;">
                                    <?php echo $table; ?>
                                </button>
                                <button class="btn btn-sm btn-outline-primary py-0 px-1 ms-2" onclick="insertAtCursor(document.getElementById('sqlQuery'), '<?php echo $table; ?>')" title="Inserir nome da tabela">+</button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Campos da Tabela - Largura reduzida -->
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">Campos da Tabela</h6>
                </div>
                <div class="card-body p-0">
                    <div id="tableFields" style="height: 200px; overflow-y: auto;">
                        <!-- Define um espaço para os campos aparecerem -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Instruções SQL - Substituindo o Histórico de Consultas -->
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">Instruções SQL</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2" style="height: 200px; overflow-y: auto;">
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="SELECT">SELECT</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="*">*</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="FROM">FROM</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="WHERE">WHERE</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="INSERT INTO">INSERT INTO</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="UPDATE">UPDATE</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="DELETE FROM">DELETE FROM</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="JOIN">JOIN</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="LEFT JOIN">LEFT JOIN</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="RIGHT JOIN">RIGHT JOIN</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="INNER JOIN">INNER JOIN</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="GROUP BY">GROUP BY</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="ORDER BY">ORDER BY</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="HAVING">HAVING</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="LIMIT">LIMIT</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="OFFSET">OFFSET</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="AS">AS</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="AND">AND</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="OR">OR</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="IN">IN</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="NOT IN">NOT IN</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="LIKE">LIKE</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="IS NULL">IS NULL</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="IS NOT NULL">IS NOT NULL</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="COUNT(*)">COUNT(*)</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="SUM()">SUM()</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="AVG()">AVG()</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="MAX()">MAX()</button>
                        <button type="button" class="btn btn-sm btn-outline-primary sql-command-btn" data-command="MIN()">MIN()</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Resultados - Abaixo dos três quadros, com largura total -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Resultados</h6>
                    <button type="button" class="btn btn-sm btn-success" id="exportCsvBtn" style="display: none;">
                        <i class="fas fa-download"></i> Exportar CSV
                    </button>
                </div>
                <div class="card-body">
                    <div id="queryResults" style="max-height: 400px; overflow-y: scroll; -webkit-overflow-scrolling: touch;">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($result && empty($error)): ?> 
                            <?php if ($result instanceof mysqli_result && $result->num_rows > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <?php 
                                                $fields = $result->fetch_fields();
                                                foreach ($fields as $field): 
                                                ?>
                                                    <th style="font-size: 0.8rem;"><?php echo htmlspecialchars($field->name); ?></th>
                                                <?php endforeach; ?>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            // Reiniciar o ponteiro de resultados
                                            $result->data_seek(0);
                                            
                                            // Limitar a 1000 registros para evitar sobrecarga
                                            $max_rows = 1000;
                                            $row_count = 0;
                                            
                                            while ($row = $result->fetch_assoc()): 
                                                if ($row_count >= $max_rows) break;
                                                $row_count++;
                                            ?>
                                                <tr>
                                                    <?php foreach ($row as $value): ?>
                                                        <td style="font-size: 0.8rem;"><?php echo htmlspecialchars($value ?? 'NULL'); ?></td>
                                                    <?php endforeach; ?>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                    
                                    <?php if ($affected_rows > $max_rows): ?>
                                        <div class="alert alert-info">
                                            Exibindo apenas os primeiros <?php echo $max_rows; ?> registros de um total de <?php echo $affected_rows; ?>.
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Histórico de Consultas - Movido para baixo do quadro Resultados -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Histórico de Consultas</h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" style="height: 200px; overflow-y: auto;">
                        <?php if (empty($query_history)): ?>
                            <div class="list-group-item text-muted">Nenhuma consulta executada ainda</div>
                        <?php else: ?>
                            <?php foreach ($query_history as $hist_item): ?>
                                <div class="list-group-item list-group-item-action history-query-container" data-query-id="<?php echo $hist_item['id']; ?>">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1 history-query" data-query="<?php echo htmlspecialchars($hist_item['query_text']); ?>" style="cursor: pointer;">
                                            <div class="fw-bold text-truncate">
                                                <?php 
                                                $display_query = strlen($hist_item['query_text']) > 80 ? substr($hist_item['query_text'], 0, 77) . '...' : $hist_item['query_text'];
                                                echo htmlspecialchars($display_query); 
                                                ?>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center gap-2">
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-query-btn" 
                                                    data-query-id="<?php echo $hist_item['id']; ?>" 
                                                    title="Excluir esta consulta">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Função para inserir texto na posição do cursor no textarea
function insertAtCursor(textarea, text) {
    const startPos = textarea.selectionStart;
    const endPos = textarea.selectionEnd;
    const scrollTop = textarea.scrollTop;
    
    textarea.value = textarea.value.substring(0, startPos) + text + textarea.value.substring(endPos, textarea.value.length);
    textarea.focus();
    textarea.selectionStart = startPos + text.length;
    textarea.selectionEnd = startPos + text.length;
    textarea.scrollTop = scrollTop;
}

// Função para carregar campos da tabela
function loadTableFields(tableName) {
    const fieldsContainer = document.getElementById('tableFields');
   
    // Mostrar loading
    fieldsContainer.innerHTML = '<div class="p-3 text-center"><div class="spinner-border spinner-border-sm" role="status"></div> Carregando...</div>';
    
    // Fazer requisição AJAX
    fetch('get_table_fields.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'table=' + encodeURIComponent(tableName)                                                                                                                                                                                                                         
    })
    .then(response => response.json())
    .then(fields => {
        let html = '<div class="list-group list-group-flush">';
        fields.forEach(field => {
            const typeInfo = field.Type + (field.Null === 'NO' ? ' NOT NULL' : '') + (field.Key === 'PRI' ? ' PRIMARY' : '');
            html += '<div class="list-group-item field-item" data-field="' + field.Field + '">';
            html += '<div class="d-flex justify-content-between align-items-start">';
            html += '<div title="' + typeInfo + '">';
            html += '<strong>' + field.Field + '</strong>';
            html += '</div>';
            html += '<button class="btn btn-sm btn-outline-primary py-0 px-1" onclick="insertAtCursor(document.getElementById(\'sqlQuery\'), \'' + field.Field + '\')">+</button>';
            html += '</div>';
            html += '</div>';
        });
        html += '</div>';
        
        fieldsContainer.innerHTML = html;
    })
    .catch(error => {
        console.error('Erro ao carregar campos:', error);
        fieldsContainer.innerHTML = '<div class="p-3 text-danger">Erro ao carregar campos da tabela</div>';
    });
}

// Função para adicionar redimensionamento de colunas
function makeColumnsResizable() {
    const table = document.querySelector('#queryResults table');
    if (!table) return;
    
    const headers = table.querySelectorAll('th');
    
    headers.forEach((header, index) => {
        // Criar handle de redimensionamento
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'resize-handle';
        header.appendChild(resizeHandle);
        
        let isResizing = false;
        let startX = 0;
        let startWidth = 0;
        
        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            startX = e.clientX;
            startWidth = header.offsetWidth;
            
            document.body.classList.add('resizing');
            
            // Criar linha de redimensionamento visual
            const resizeLine = document.createElement('div');
            resizeLine.className = 'resize-line';
            resizeLine.style.left = e.clientX + 'px';
            resizeLine.style.height = table.offsetHeight + 'px';
            document.body.appendChild(resizeLine);
            
            const onMouseMove = function(e) {
                if (!isResizing) return;
                
                const diff = e.clientX - startX;
                const newWidth = Math.max(80, startWidth + diff); // Mínimo 80px
                
                resizeLine.style.left = e.clientX + 'px';
            };
            
            const onMouseUp = function(e) {
                if (!isResizing) return;
                
                isResizing = false;
                document.body.classList.remove('resizing');
                
                const diff = e.clientX - startX;
                const newWidth = Math.max(80, startWidth + diff);
                
                // Aplicar nova largura
                header.style.width = newWidth + 'px';
                
                // Aplicar a mesma largura para as células da coluna
                const cells = table.querySelectorAll(`td:nth-child(${index + 1})`);
                cells.forEach(cell => {
                    cell.style.width = newWidth + 'px';
                });
                
                // Remover linha de redimensionamento
                document.body.removeChild(resizeLine);
                
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            };
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            
            e.preventDefault();
        });
    });
}

// Função para executar SELECT * na tabela (modificar para incluir redimensionamento)
function executeSelectTable(tableName) {
    const resultsContainer = document.getElementById('queryResults');
    const sqlQuery = document.getElementById('sqlQuery');
    
    // Atualizar o textarea com a query
    sqlQuery.value = 'SELECT * FROM ' + tableName + ' LIMIT 100;';
    
    // Mostrar loading nos resultados
    resultsContainer.innerHTML = '<div class="p-3 text-center"><div class="spinner-border" role="status"></div> Executando consulta...</div>';
    
    // Fazer requisição AJAX para o arquivo separado
    fetch('ajax_select_table.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'table_name=' + encodeURIComponent(tableName)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            resultsContainer.innerHTML = '<div class="alert alert-danger">' + data.error + '</div>';
        } else {
            resultsContainer.innerHTML = data.html;
            // Adicionar redimensionamento após carregar a tabela
            setTimeout(makeColumnsResizable, 100);
        }
    })
    .catch(error => {
        console.error('Erro ao executar consulta:', error);
        resultsContainer.innerHTML = '<div class="alert alert-danger">Erro ao executar consulta</div>';
    });
}

// Adicionar redimensionamento quando uma consulta SQL for executada via formulário
document.addEventListener('DOMContentLoaded', function() {
    const saveQueryCheckbox = document.getElementById('saveQueryCheckbox');
    
    // Carregar estado salvo do checkbox (padrão: marcado)
    if (localStorage.getItem('saveQueryCheckbox') === 'false') {
        saveQueryCheckbox.checked = false;
    }
    
    // Salvar estado do checkbox quando alterado
    saveQueryCheckbox.addEventListener('change', function() {
        localStorage.setItem('saveQueryCheckbox', this.checked);
    });
});
document.addEventListener('DOMContentLoaded', function() {
    const sqlQuery = document.getElementById('sqlQuery');
    const clearBtn = document.getElementById('clearBtn');
    
    // Botão limpar
    clearBtn.addEventListener('click', function() {
        sqlQuery.value = '';
        sqlQuery.focus();
    });
    
    // Botões de tabela - ao clicar executa SELECT *
    document.querySelectorAll('.table-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const tableName = this.getAttribute('data-table');
            
            // Carregar campos da tabela
            loadTableFields(tableName);
            
            // Destacar tabela selecionada
            document.querySelectorAll('.list-group-item').forEach(item => {
                if (item.querySelector('.table-btn')) {
                    item.classList.remove('active');
                }
            });
            this.closest('.list-group-item').classList.add('active');
        });
    });
    
    // Consultas do histórico - clique para carregar
    document.addEventListener('click', function(e) {
        if (e.target.closest('.history-query')) {
            const historyQuery = e.target.closest('.history-query');
            const query = historyQuery.getAttribute('data-query');
            document.getElementById('sqlQuery').value = query;
        }
    });
    
    // Botões de exclusão individual - também usar admin_sql.php
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-query-btn')) {
            e.preventDefault();
            e.stopPropagation();
            
            const deleteBtn = e.target.closest('.delete-query-btn');
            const queryId = deleteBtn.getAttribute('data-query-id');
            
            if (confirm('Tem certeza que deseja excluir esta consulta do histórico?')) {
                // Usar o mesmo arquivo admin_sql.php
                fetch('admin_sql.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'action=delete_query&query_id=' + encodeURIComponent(queryId)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Remover o item da interface
                        const container = deleteBtn.closest('.history-query-container');
                        container.remove();
                        
                        // Se não há mais itens, mostrar mensagem
                        const historyContainer = document.querySelector('.list-group-flush');
                        if (historyContainer.children.length === 0) {
                            historyContainer.innerHTML = '<div class="list-group-item text-muted">Nenhuma consulta executada ainda</div>';
                        }
                    } else {
                        alert('Erro ao excluir consulta: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Erro ao excluir consulta:', error);
                    alert('Erro ao excluir consulta. Verifique o console para mais detalhes.');
                });
            }
        }
    });
    
    // Botão para limpar histórico
    // Botão para limpar histórico
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');
    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', function(event) {
            // Prevenir qualquer comportamento padrão
            event.preventDefault();
            event.stopPropagation();
            
            if (confirm('Tem certeza que deseja limpar o histórico de consultas?')) {
                // Usar o mesmo arquivo que os botões individuais
                fetch('admin_sql.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: 'action=clear_history'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Limpar a interface do histórico
                        const historyContainer = document.querySelector('.list-group-flush');
                        historyContainer.innerHTML = '<div class="list-group-item text-muted">Nenhuma consulta executada ainda</div>';
                        
                        // ADICIONAR ESTA LINHA: Limpar também o campo de texto da consulta
                        document.getElementById('sqlQuery').value = '';
                    } else {
                        alert('Erro ao limpar histórico: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Erro ao limpar histórico:', error);
                    alert('Erro ao limpar histórico. Verifique o console para mais detalhes.');
                });
            }
        });
    }
});
// Botões de instruções SQL
document.addEventListener('click', function(e) {
    if (e.target.closest('.sql-command-btn')) {
        const commandBtn = e.target.closest('.sql-command-btn');
        const command = commandBtn.getAttribute('data-command');
        const sqlQuery = document.getElementById('sqlQuery');
        
        // Inserir o comando na posição do cursor
        insertAtCursor(sqlQuery, command + ' ');
    }
});
// Função para exportar tabela para CSV
function exportTableToCSV() {
    const table = document.querySelector('#queryResults table');
    if (!table) {
        alert('Nenhuma tabela encontrada para exportar.');
        return;
    }
    
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            let cellText = cols[j].innerText.replace(/"/g, '""');
            row.push('"' + cellText + '"');
        }
        
        csv.push(row.join(','));
    }
    
    // Criar e baixar arquivo CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'consulta_sql_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Event listener para o botão de exportação
document.addEventListener('DOMContentLoaded', function() {
    const exportBtn = document.getElementById('exportCsvBtn');
    
    if (exportBtn) {
        exportBtn.addEventListener('click', exportTableToCSV);
    }
    
    // Mostrar/ocultar botão de exportação baseado na presença de resultados
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                const table = document.querySelector('#queryResults table');
                const exportBtn = document.getElementById('exportCsvBtn');
                
                if (table && exportBtn) {
                    exportBtn.style.display = 'block';
                } else if (exportBtn) {
                    exportBtn.style.display = 'none';
                }
            }
        });
    });
    
    // Observar mudanças no container de resultados
    const queryResults = document.getElementById('queryResults');
    if (queryResults) {
        observer.observe(queryResults, { childList: true, subtree: true });
        
        // Verificar estado inicial
        const table = document.querySelector('#queryResults table');
        const exportBtn = document.getElementById('exportCsvBtn');
        if (table && exportBtn) {
            exportBtn.style.display = 'block';
        }
    }
});
</script>
