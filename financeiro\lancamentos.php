<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Configuração de filtros
$filtro_tipo = $_GET['tipo'] ?? 'todos';
$filtro_status = $_GET['status'] ?? 'todos';
$filtro_data_inicio = $_GET['data_inicio'] ?? date('Y-m-01'); // Primeiro dia do mês atual
$filtro_data_fim = $_GET['data_fim'] ?? date('Y-m-t'); // Último dia do mês atual
$filtro_categoria = $_GET['categoria'] ?? 'todos';
$filtro_busca = $_GET['busca'] ?? '';

// Construir a consulta SQL com base nos filtros
$sql_base = "SELECT l.*, c.nome as categoria_nome, c.cor as categoria_cor, r.id as reserva_codigo 
             FROM lancamentos_financeiros l 
             LEFT JOIN categorias_financeiras c ON l.categoria_id = c.id 
             LEFT JOIN reservas r ON l.reserva_id = r.id 
             WHERE l.pousada_id = ?";

$params = [$pousada_id];
$types = "i";

// Aplicar filtro de tipo (receita/despesa)
if ($filtro_tipo != 'todos') {
    $sql_base .= " AND l.tipo = ?";
    $params[] = $filtro_tipo;
    $types .= "s";
}

// Aplicar filtro de status
if ($filtro_status != 'todos') {
    $sql_base .= " AND l.status = ?";
    $params[] = $filtro_status;
    $types .= "s";
}

// Aplicar filtro de data
$sql_base .= " AND l.data_vencimento BETWEEN ? AND ?";
$params[] = $filtro_data_inicio;
$params[] = $filtro_data_fim;
$types .= "ss";

// Aplicar filtro de categoria
if ($filtro_categoria != 'todos') {
    $sql_base .= " AND l.categoria_id = ?";
    $params[] = $filtro_categoria;
    $types .= "i";
}

// Aplicar filtro de busca
if (!empty($filtro_busca)) {
    $sql_base .= " AND (l.descricao LIKE ? OR l.observacao LIKE ?)";
    $busca_param = "%{$filtro_busca}%";
    $params[] = $busca_param;
    $params[] = $busca_param;
    $types .= "ss";
}

// Ordenar por data de vencimento
$sql_base .= " ORDER BY l.data_vencimento ASC";

// Preparar e executar a consulta
$stmt = $conn->prepare($sql_base);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$lancamentos = $stmt->get_result();

// Buscar categorias para o filtro
$sql_categorias = "SELECT id, nome, tipo FROM categorias_financeiras WHERE pousada_id = ? ORDER BY nome";
$stmt_categorias = $conn->prepare($sql_categorias);
$stmt_categorias->bind_param("i", $pousada_id);
$stmt_categorias->execute();
$categorias = $stmt_categorias->get_result();

// Buscar formas de pagamento para o modal de novo lançamento
$sql_formas = "SELECT id, nome FROM formas_pagamento WHERE pousada_id = ? AND is_active = 1 ORDER BY nome";
$stmt_formas = $conn->prepare($sql_formas);
$stmt_formas->bind_param("i", $pousada_id);
$stmt_formas->execute();
$formas_pagamento = $stmt_formas->get_result();

// Verificar se existe mensagem de feedback
$mensagem = "";
if (isset($_SESSION['mensagem'])) {
    $mensagem = $_SESSION['mensagem'];
    unset($_SESSION['mensagem']); // Limpa a mensagem após exibir
}

// Calcular totais
$total_receitas = 0;
$total_despesas = 0;
$total_pendente = 0;
$total_pago = 0;

// Reiniciar o resultado para calcular os totais
$stmt->execute();
$totais = $stmt->get_result();

while ($lancamento = $totais->fetch_assoc()) {
    if ($lancamento['tipo'] == 'receita') {
        $total_receitas += $lancamento['valor'];
    } else {
        $total_despesas += $lancamento['valor'];
    }
    
    if ($lancamento['status'] == 'pendente') {
        $total_pendente += $lancamento['valor'];
    } elseif ($lancamento['status'] == 'pago') {
        $total_pago += $lancamento['valor'];
    }
}

// Calcular saldo
$saldo = $total_receitas - $total_despesas;
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Lançamentos Financeiros</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../custom/css/form_fnrh.css">
    <link rel="stylesheet" href="../custom/css/admin-financeiro.css">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center">Lançamentos Financeiros</h1>
        
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $mensagem; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            </div>
        <?php endif; ?>

        <!-- Resumo Financeiro -->
        <div class="row resumo-container">
            <div class="col-md-4">
                <div class="resumo-item resumo-receitas">
                    <h5>Total de Receitas</h5>
                    <span class="valor-receita">R$ <?php echo number_format($total_receitas, 2, ',', '.'); ?></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="resumo-item resumo-despesas">
                    <h5>Total de Despesas</h5>
                    <span class="valor-despesa">R$ <?php echo number_format($total_despesas, 2, ',', '.'); ?></span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="resumo-item resumo-saldo">
                    <h5>Saldo</h5>
                    <span class="<?php echo $saldo >= 0 ? 'valor-saldo-positivo' : 'valor-saldo-negativo'; ?>">
                        R$ <?php echo number_format($saldo, 2, ',', '.'); ?>
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="resumo-item resumo-pendente">
                    <h5>Total Pendente</h5>
                    <span>R$ <?php echo number_format($total_pendente, 2, ',', '.'); ?></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="resumo-item resumo-pago">
                    <h5>Total Pago</h5>
                    <span>R$ <?php echo number_format($total_pago, 2, ',', '.'); ?></span>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filtros-container">
            <form method="GET" action="index.php">
                <input type="hidden" name="page" value="lancamentos">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="tipo" class="form-label">Tipo</label>
                            <select class="form-select" id="tipo" name="tipo">
                                <option value="todos" <?php echo $filtro_tipo == 'todos' ? 'selected' : ''; ?>>Todos</option>
                                <option value="receita" <?php echo $filtro_tipo == 'receita' ? 'selected' : ''; ?>>Receitas</option>
                                <option value="despesa" <?php echo $filtro_tipo == 'despesa' ? 'selected' : ''; ?>>Despesas</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="todos" <?php echo $filtro_status == 'todos' ? 'selected' : ''; ?>>Todos</option>
                                <option value="pendente" <?php echo $filtro_status == 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                                <option value="pago" <?php echo $filtro_status == 'pago' ? 'selected' : ''; ?>>Pago</option>
                                <option value="cancelado" <?php echo $filtro_status == 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="data_inicio" class="form-label">Data Início</label>
                            <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?php echo $filtro_data_inicio; ?>">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="data_fim" class="form-label">Data Fim</label>
                            <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?php echo $filtro_data_fim; ?>">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="categoria" class="form-label">Categoria</label>
                            <select class="form-select" id="categoria" name="categoria">
                                <option value="todos">Todas as categorias</option>
                                <optgroup label="Receitas">
                                    <?php 
                                    $categorias->data_seek(0);
                                    while ($categoria = $categorias->fetch_assoc()): 
                                        if ($categoria['tipo'] == 'receita'):
                                    ?>
                                        <option value="<?php echo $categoria['id']; ?>" <?php echo $filtro_categoria == $categoria['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($categoria['nome']); ?>
                                        </option>
                                    <?php 
                                        endif;
                                    endwhile;
                                    ?>
                                </optgroup>
                                <optgroup label="Despesas">
                                    <?php 
                                    $categorias->data_seek(0);
                                    while ($categoria = $categorias->fetch_assoc()): 
                                        if ($categoria['tipo'] == 'despesa'):
                                    ?>
                                        <option value="<?php echo $categoria['id']; ?>" <?php echo $filtro_categoria == $categoria['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($categoria['nome']); ?>
                                        </option>
                                    <?php 
                                        endif;
                                    endwhile;
                                    ?>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="busca" class="form-label">Busca</label>
                            <input type="text" class="form-control" id="busca" name="busca" placeholder="Buscar por descrição ou observação" value="<?php echo htmlspecialchars($filtro_busca); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary btn-filtrar">Filtrar</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Botões de Ação -->
        <div class="row mb-4">
            <div class="col-md-12">
                <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#modalNovoLancamento" data-tipo="receita">
                    <i class="bi bi-plus-circle"></i> Nova Receita
                </button>
                <button type="button" class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#modalNovoLancamento" data-tipo="despesa">
                    <i class="bi bi-plus-circle"></i> Nova Despesa
                </button>
            </div>
        </div>

        <!-- Lista de Lançamentos -->
        <div class="row">
            <?php if ($lancamentos->num_rows > 0): ?>
                <?php while ($lancamento = $lancamentos->fetch_assoc()): ?>
                    <div class="col-md-6">
                        <div class="lancamento-card lancamento-<?php echo $lancamento['tipo']; ?> lancamento-<?php echo $lancamento['status']; ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5><?php echo htmlspecialchars($lancamento['descricao']); ?></h5>
                                <div>
                                    <?php if ($lancamento['status'] == 'pendente'): ?>
                                        <button type="button" class="btn btn-sm btn-success btn-pagar" 
                                                data-id="<?php echo $lancamento['id']; ?>"
                                                data-bs-toggle="modal" data-bs-target="#modalPagarLancamento">
                                            Pagar
                                        </button>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-sm btn-outline-primary btn-editar" 
                                            data-id="<?php echo $lancamento['id']; ?>"
                                            data-bs-toggle="modal" data-bs-target="#modalEditarLancamento">
                                        Editar
                                    </button>
                                    <?php if ($lancamento['status'] != 'cancelado'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger btn-cancelar"
                                                data-id="<?php echo $lancamento['id']; ?>"
                                                data-descricao="<?php echo htmlspecialchars($lancamento['descricao']); ?>"
                                                data-bs-toggle="modal" data-bs-target="#modalCancelarLancamento">
                                            Cancelar
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="mt-2">
                                <span class="categoria-badge" style="background-color: <?php echo $lancamento['categoria_cor']; ?>">
                                    <?php echo htmlspecialchars($lancamento['categoria_nome']); ?>
                                </span>
                                <span class="badge bg-secondary">
                                    <?php echo ucfirst($lancamento['status']); ?>
                                </span>
                                <?php if ($lancamento['reserva_id']): ?>
                                    <span class="badge bg-info">
                                        Reserva #<?php echo $lancamento['reserva_codigo']; ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Valor:</strong> 
                                        <span class="<?php echo $lancamento['tipo'] == 'receita' ? 'valor-receita' : 'valor-despesa'; ?>">
                                            R$ <?php echo number_format($lancamento['valor'], 2, ',', '.'); ?>
                                        </span>
                                    </p>
                                    <p class="mb-1"><strong>Vencimento:</strong> <?php echo date('d/m/Y', strtotime($lancamento['data_vencimento'])); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <?php if ($lancamento['data_pagamento']): ?>
                                        <p class="mb-1"><strong>Pagamento:</strong> <?php echo date('d/m/Y', strtotime($lancamento['data_pagamento'])); ?></p>
                                    <?php endif; ?>
                                    <?php if ($lancamento['forma_pagamento']): ?>
                                        <p class="mb-1"><strong>Forma:</strong> <?php echo htmlspecialchars($lancamento['forma_pagamento']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if (!empty($lancamento['observacao'])): ?>
                                <div class="mt-2">
                                    <p class="mb-0"><strong>Observação:</strong> <?php echo htmlspecialchars($lancamento['observacao']); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        Nenhum lançamento encontrado com os filtros selecionados.
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal Novo Lançamento -->
    <div class="modal fade" id="modalNovoLancamento" tabindex="-1" aria-labelledby="modalNovoLancamentoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalNovoLancamentoLabel">Novo Lançamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="?page=lancamentos_salvar" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="acao" value="cadastrar">
                        <input type="hidden" name="tipo" id="tipo_lancamento" value="receita">
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="descricao" class="form-label">Descrição*</label>
                                    <input type="text" class="form-control" id="descricao" name="descricao" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="valor" class="form-label">Valor*</label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="number" class="form-control" id="valor" name="valor" step="0.01" min="0.01" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="categoria_id" class="form-label">Categoria*</label>
                                    <select class="form-select" id="categoria_id" name="categoria_id" required>
                                        <option value="">Selecione uma categoria</option>
                                        <div id="categorias_receita">
                                            <?php 
                                            $categorias->data_seek(0);
                                            while ($categoria = $categorias->fetch_assoc()): 
                                                if ($categoria['tipo'] == 'receita'):
                                            ?>
                                                <option value="<?php echo $categoria['id']; ?>" data-tipo="receita">
                                                    <?php echo htmlspecialchars($categoria['nome']); ?>
                                                </option>
                                            <?php 
                                                endif;
                                            endwhile;
                                            ?>
                                        </div>
                                        <div id="categorias_despesa">
                                            <?php 
                                            $categorias->data_seek(0);
                                            while ($categoria = $categorias->fetch_assoc()): 
                                                if ($categoria['tipo'] == 'despesa'):
                                            ?>
                                                <option value="<?php echo $categoria['id']; ?>" data-tipo="despesa">
                                                    <?php echo htmlspecialchars($categoria['nome']); ?>
                                                </option>
                                            <?php 
                                                endif;
                                            endwhile;
                                            ?>
                                        </div>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_vencimento" class="form-label">Data de Vencimento*</label>
                                    <input type="date" class="form-control" id="data_vencimento" name="data_vencimento" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status*</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pendente">Pendente</option>
                                        <option value="pago">Pago</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 div-data-pagamento" style="display: none;">
                                <div class="mb-3">
                                    <label for="data_pagamento" class="form-label">Data de Pagamento*</label>
                                    <input type="date" class="form-control" id="data_pagamento" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row div-forma-pagamento" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="forma_pagamento" class="form-label">Forma de Pagamento*</label>
                                    <select class="form-select" id="forma_pagamento" name="forma_pagamento">
                                        <option value="">Selecione</option>
                                        <?php 
                                        while ($forma = $formas_pagamento->fetch_assoc()): 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                                                <?php echo htmlspecialchars($forma['nome']); ?>
                                            </option>
                                        <?php 
                                        endwhile;
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="observacao" class="form-label">Observação</label>
                            <textarea class="form-control" id="observacao" name="observacao" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">Salvar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Editar Lançamento -->
    <div class="modal fade" id="modalEditarLancamento" tabindex="-1" aria-labelledby="modalEditarLancamentoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalEditarLancamentoLabel">Editar Lançamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="?page=lancamentos_salvar" method="POST" id="formEditarLancamento">
                    <div class="modal-body">
                        <!-- Conteúdo será carregado via AJAX -->
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Carregando...</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"></button>
                        <button type="submit" class="btn btn-primary">Salvar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Pagar Lançamento -->
    <div class="modal fade" id="modalPagarLancamento" tabindex="-1" aria-labelledby="modalPagarLancamentoLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalPagarLancamentoLabel">Registrar Pagamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <form action="?page=lancamentos_salvar" method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="acao" value="pagar">
                        <input type="hidden" name="id" id="pagar_id">
                        
                        <div class="mb-3">
                            <label for="pagar_data_pagamento" class="form-label">Data de Pagamento*</label>
                            <input type="date" class="form-control" id="pagar_data_pagamento" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="pagar_forma_pagamento" class="form-label">Forma de Pagamento*</label>
                            <select class="form-select" id="pagar_forma_pagamento" name="forma_pagamento" required>
                                <option value="">Selecione</option>
                                <?php 
                                $formas_pagamento->data_seek(0);
                                while ($forma = $formas_pagamento->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                                        <?php echo htmlspecialchars($forma['nome']); ?>
                                    </option>
                                <?php 
                                endwhile;
                                ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="pagar_observacao" class="form-label">Observação</label>
                            <textarea class="form-control" id="pagar_observacao" name="observacao" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">Confirmar Pagamento</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Cancelar Lançamento -->
    <div class="modal fade" id="modalCancelarLancamento" tabindex="-1" aria-labelledby="modalCancelarLancamentoLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCancelarLancamentoLabel">Confirmar Cancelamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja cancelar o lançamento <strong id="cancelar_descricao"></strong>?</p>
                    <p class="text-danger">Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Voltar</button>
                    <form action="?page=lancamentos_salvar" method="POST">
                        <input type="hidden" name="acao" value="cancelar">
                        <input type="hidden" name="id" id="cancelar_id">
                        <button type="submit" class="btn btn-danger">Confirmar Cancelamento</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar modal de novo lançamento
            const modalNovoLancamento = document.getElementById('modalNovoLancamento');
            modalNovoLancamento.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const tipo = button.getAttribute('data-tipo');
                const modal = this;
                
                // Atualizar título do modal
                const modalTitle = modal.querySelector('.modal-title');
                modalTitle.textContent = tipo === 'receita' ? 'Nova Receita' : 'Nova Despesa';
                
                // Definir o tipo de lançamento
                document.getElementById('tipo_lancamento').value = tipo;
                
                // Filtrar categorias com base no tipo
                const categoriasSelect = document.getElementById('categoria_id');
                const options = categoriasSelect.querySelectorAll('option');
                
                options.forEach(option => {
                    if (option.value === '') return; // Manter a opção "Selecione uma categoria"
                    
                    const optionTipo = option.getAttribute('data-tipo');
                    if (optionTipo === tipo) {
                        option.style.display = '';
                    } else {
                        option.style.display = 'none';
                    }
                });
                
                // Limpar seleção anterior
                categoriasSelect.value = '';
            });
            
            // Mostrar/ocultar campos de pagamento com base no status
            const statusSelect = document.getElementById('status');
            statusSelect.addEventListener('change', function() {
                const dataPagamentoDiv = document.querySelector('.div-data-pagamento');
                const formaPagamentoDiv = document.querySelector('.div-forma-pagamento');
                
                if (this.value === 'pago') {
                    dataPagamentoDiv.style.display = 'block';
                    formaPagamentoDiv.style.display = 'block';
                    document.getElementById('data_pagamento').required = true;
                    document.getElementById('forma_pagamento').required = true;
                } else {
                    dataPagamentoDiv.style.display = 'none';
                    formaPagamentoDiv.style.display = 'none';
                    document.getElementById('data_pagamento').required = false;
                    document.getElementById('forma_pagamento').required = false;
                }
            });
            
            // Configurar modal de edição
            const modalEditarLancamento = document.getElementById('modalEditarLancamento');
            modalEditarLancamento.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const modalBody = this.querySelector('.modal-body');
                
                // Carregar dados do lançamento via AJAX
                fetch(`index.php?page=lancamentos_detalhes&id=${id}`)
                    .then(response => response.text())
                    .then(html => {
                        modalBody.innerHTML = html;
                        
                        // Configurar eventos após carregar o conteúdo
                        const editStatusSelect = document.getElementById('edit_status');
                        if (editStatusSelect) {
                            editStatusSelect.addEventListener('change', function() {
                                const dataPagamentoDiv = document.querySelector('.div-edit-data-pagamento');
                                const formaPagamentoDiv = document.querySelector('.div-edit-forma-pagamento');
                                
                                if (this.value === 'pago') {
                                    dataPagamentoDiv.style.display = 'block';
                                    formaPagamentoDiv.style.display = 'block';
                                    document.getElementById('edit_data_pagamento').required = true;
                                    document.getElementById('edit_forma_pagamento').required = true;
                                } else {
                                    dataPagamentoDiv.style.display = 'none';
                                    formaPagamentoDiv.style.display = 'none';
                                    document.getElementById('edit_data_pagamento').required = false;
                                    document.getElementById('edit_forma_pagamento').required = false;
                                }
                            });
                            
                            // Disparar o evento change para configurar a visibilidade inicial
                            editStatusSelect.dispatchEvent(new Event('change'));
                        }
                    })
                    .catch(error => {
                        console.error('Erro ao carregar detalhes do lançamento:', error);
                        modalBody.innerHTML = '<div class="alert alert-danger">Erro ao carregar dados do lançamento.</div>';
                    });
            });
            
            // Configurar modal de pagamento
            const modalPagarLancamento = document.getElementById('modalPagarLancamento');
            modalPagarLancamento.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                document.getElementById('pagar_id').value = id;
            });
            
            // Configurar modal de cancelamento
            const modalCancelarLancamento = document.getElementById('modalCancelarLancamento');
            modalCancelarLancamento.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const descricao = button.getAttribute('data-descricao');
                
                document.getElementById('cancelar_id').value = id;
                document.getElementById('cancelar_descricao').textContent = descricao;
            });
        });
    </script>
</body>
</html>