<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se há uma mensagem na sessão
if (isset($_SESSION['mensagem'])) {
    $mensagem = $_SESSION['mensagem'];
    unset($_SESSION['mensagem']); // Limpar a mensagem após exibi-la
}

// Obter categorias financeiras
$categorias = $conn->query("SELECT id, nome, tipo, cor FROM categorias_financeiras WHERE pousada_id = $pousada_id ORDER BY tipo, nome");

// Obter formas de pagamento
$formas_pagamento = $conn->query("SELECT id, nome FROM formas_pagamento WHERE pousada_id = $pousada_id AND is_active = 1 ORDER BY nome");

// Definir filtros padrão
$filtro_tipo = $_GET['tipo'] ?? 'todos';
$filtro_status = $_GET['status'] ?? 'todos';
$filtro_categoria = $_GET['categoria'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? date('Y-m-01'); // Primeiro dia do mês atual
$filtro_data_fim = $_GET['data_fim'] ?? date('Y-m-t'); // Último dia do mês atual
$filtro_busca = $_GET['busca'] ?? '';

// Construir a consulta SQL com base nos filtros
$sql = "SELECT lf.*, cf.nome as categoria_nome, cf.cor as categoria_cor,
               CONCAT(h.nome, ' - UH ', r.uh) as reserva_info
        FROM lancamentos_financeiros lf
        LEFT JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
        LEFT JOIN reservas r ON lf.reserva_id = r.id
        LEFT JOIN hospedes h ON r.hospede_id = h.id
        WHERE lf.pousada_id = $pousada_id";

// Aplicar filtros
if ($filtro_tipo != 'todos') {
    $sql .= " AND lf.tipo = '$filtro_tipo'";
}

if ($filtro_status != 'todos') {
    $sql .= " AND lf.status = '$filtro_status'";
}

if (!empty($filtro_categoria)) {
    $sql .= " AND lf.categoria_id = " . intval($filtro_categoria);
}

// Filtro de data - por padrão, filtra pela data de vencimento
$campo_data = $_GET['campo_data'] ?? 'vencimento';
if ($campo_data == 'vencimento') {
    $sql .= " AND lf.data_vencimento BETWEEN '$filtro_data_inicio' AND '$filtro_data_fim'";
} elseif ($campo_data == 'lancamento') {
    $sql .= " AND lf.data_lancamento BETWEEN '$filtro_data_inicio' AND '$filtro_data_fim'";
} elseif ($campo_data == 'pagamento') {
    $sql .= " AND lf.data_pagamento BETWEEN '$filtro_data_inicio' AND '$filtro_data_fim'";
}

// Filtro de busca por texto
if (!empty($filtro_busca)) {
    $sql .= " AND (lf.descricao LIKE '%$filtro_busca%' OR cf.nome LIKE '%$filtro_busca%')";
}

// Ordenação
$sql .= " ORDER BY lf.data_vencimento ASC, lf.id DESC";

// Executar a consulta
$lancamentos = $conn->query($sql);

// Calcular totais
$total_receitas = 0;
$total_despesas = 0;
$total_pendente_receitas = 0;
$total_pendente_despesas = 0;

// Consulta para obter totais
$sql_totais = "SELECT 
                SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) as total_receitas,
                SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as total_despesas,
                SUM(CASE WHEN tipo = 'receita' AND status = 'pendente' THEN valor ELSE 0 END) as total_pendente_receitas,
                SUM(CASE WHEN tipo = 'despesa' AND status = 'pendente' THEN valor ELSE 0 END) as total_pendente_despesas
               FROM lancamentos_financeiros 
               WHERE pousada_id = $pousada_id";

// Aplicar os mesmos filtros da consulta principal
if ($filtro_tipo != 'todos') {
    $sql_totais .= " AND tipo = '$filtro_tipo'";
}

if ($filtro_status != 'todos') {
    $sql_totais .= " AND status = '$filtro_status'";
}

if (!empty($filtro_categoria)) {
    $sql_totais .= " AND categoria_id = " . intval($filtro_categoria);
}

if ($campo_data == 'vencimento') {
    $sql_totais .= " AND data_vencimento BETWEEN '$filtro_data_inicio' AND '$filtro_data_fim'";
} elseif ($campo_data == 'lancamento') {
    $sql_totais .= " AND data_lancamento BETWEEN '$filtro_data_inicio' AND '$filtro_data_fim'";
} elseif ($campo_data == 'pagamento') {
    $sql_totais .= " AND data_pagamento BETWEEN '$filtro_data_inicio' AND '$filtro_data_fim'";
}

if (!empty($filtro_busca)) {
    $sql_totais .= " AND descricao LIKE '%$filtro_busca%'";
}

$result_totais = $conn->query($sql_totais);
if ($row_totais = $result_totais->fetch_assoc()) {
    $total_receitas = $row_totais['total_receitas'] ?: 0;
    $total_despesas = $row_totais['total_despesas'] ?: 0;
    $total_pendente_receitas = $row_totais['total_pendente_receitas'] ?: 0;
    $total_pendente_despesas = $row_totais['total_pendente_despesas'] ?: 0;
}

// Calcular saldo
$saldo = $total_receitas - $total_despesas;
$saldo_pendente = $total_pendente_receitas - $total_pendente_despesas;

// Verificar se existe um caixa aberto
$caixa_aberto = false;
$sql_caixa = "SELECT id FROM caixa_diario WHERE pousada_id = $pousada_id AND status = 'aberto'";
$result_caixa = $conn->query($sql_caixa);
if ($result_caixa && $result_caixa->num_rows > 0) {
    $caixa_aberto = true;
}

// Função auxiliar para validar data
function validarData($data) {
    if (empty($data)) return false;
    
    $d = DateTime::createFromFormat('Y-m-d', $data);
    return $d && $d->format('Y-m-d') === $data;
}
?>

<div class="container-fluid px-4 py-4">
    <?php if (isset($mensagem)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?php echo $mensagem; ?>
            <small class="d-block mt-1">
                <em>Nota: Apenas pagamentos em dinheiro afetam o saldo do caixa físico.</em>
            </small>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-md-6">
            <h2 class="mb-3">Controle Financeiro</h2>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-lightbulb-fill me-2" style="font-size: 1.2em;"></i>
                <div>
                    <strong>Aqui você:</strong>Administra contas a pagar e receber, com registro de lançamentos, categorização e acompanhamento de status de pagamentos.
                </div>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#modalNovaReceita">
                <i class="bi bi-plus-circle"></i> Nova Receita
            </button>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#modalNovaDespesa">
                <i class="bi bi-plus-circle"></i> Nova Despesa
            </button>
        </div>
    </div>
    
    <!-- Resumo Financeiro -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Receitas</h5>
                    <h3 class="card-text">R$ <?php echo number_format($total_receitas, 2, ',', '.'); ?></h3>
                    <small>Pendente: R$ <?php echo number_format($total_pendente_receitas, 2, ',', '.'); ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">Despesas</h5>
                    <h3 class="card-text">R$ <?php echo number_format($total_despesas, 2, ',', '.'); ?></h3>
                    <small>Pendente: R$ <?php echo number_format($total_pendente_despesas, 2, ',', '.'); ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card <?php echo $saldo >= 0 ? 'bg-primary' : 'bg-warning text-dark'; ?> text-white">
                <div class="card-body">
                    <h5 class="card-title">Saldo</h5>
                    <h3 class="card-text">R$ <?php echo number_format($saldo, 2, ',', '.'); ?></h3>
                    <small>Pendente: R$ <?php echo number_format($saldo_pendente, 2, ',', '.'); ?></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Período</h5>
                    <p class="card-text"><?php echo date('d/m/Y', strtotime($filtro_data_inicio)); ?> a <?php echo date('d/m/Y', strtotime($filtro_data_fim)); ?></p>
                    <small>Campo: <?php echo ucfirst($campo_data); ?></small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filtros -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-funnel"></i> Filtros
        </div>
        <div class="card-body">
            <form method="GET" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="contas">
                
                <div class="col-md-2">
                    <label for="tipo" class="form-label">Tipo</label>
                    <select class="form-select" id="tipo" name="tipo">
                        <option value="todos" <?php echo $filtro_tipo == 'todos' ? 'selected' : ''; ?>>Todos</option>
                        <option value="receita" <?php echo $filtro_tipo == 'receita' ? 'selected' : ''; ?>>Receitas</option>
                        <option value="despesa" <?php echo $filtro_tipo == 'despesa' ? 'selected' : ''; ?>>Despesas</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="todos" <?php echo $filtro_status == 'todos' ? 'selected' : ''; ?>>Todos</option>
                        <option value="pendente" <?php echo $filtro_status == 'pendente' ? 'selected' : ''; ?>>Pendentes</option>
                        <option value="pago" <?php echo $filtro_status == 'pago' ? 'selected' : ''; ?>>Pagos</option>
                        <option value="cancelado" <?php echo $filtro_status == 'cancelado' ? 'selected' : ''; ?>>Cancelados</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="categoria" class="form-label">Categoria</label>
                    <select class="form-select" id="categoria" name="categoria">
                        <option value="">Todas</option>
                        <optgroup label="Receitas">
                            <?php 
                            $categorias->data_seek(0);
                            while ($categoria = $categorias->fetch_assoc()): 
                                if ($categoria['tipo'] == 'receita'):
                            ?>
                                <option value="<?php echo $categoria['id']; ?>" <?php echo $filtro_categoria == $categoria['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($categoria['nome']); ?>
                                </option>
                            <?php 
                                endif;
                            endwhile; 
                            ?>
                        </optgroup>
                        <optgroup label="Despesas">
                            <?php 
                            $categorias->data_seek(0);
                            while ($categoria = $categorias->fetch_assoc()): 
                                if ($categoria['tipo'] == 'despesa'):
                            ?>
                                <option value="<?php echo $categoria['id']; ?>" <?php echo $filtro_categoria == $categoria['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($categoria['nome']); ?>
                                </option>
                            <?php 
                                endif;
                            endwhile; 
                            ?>
                        </optgroup>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="campo_data" class="form-label">Campo de Data</label>
                    <select class="form-select" id="campo_data" name="campo_data">
                        <option value="vencimento" <?php echo $campo_data == 'vencimento' ? 'selected' : ''; ?>>Vencimento</option>
                        <option value="lancamento" <?php echo $campo_data == 'lancamento' ? 'selected' : ''; ?>>Lançamento</option>
                        <option value="pagamento" <?php echo $campo_data == 'pagamento' ? 'selected' : ''; ?>>Pagamento</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="data_inicio" class="form-label">Período</label>
                    <div class="input-group">
                        <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?php echo $filtro_data_inicio; ?>">
                        <span class="input-group-text">até</span>
                        <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?php echo $filtro_data_fim; ?>">
                    </div>
                </div>
                
                <div class="col-md-8">
                    <label for="busca" class="form-label">Busca</label>
                    <input type="text" class="form-control" id="busca" name="busca" placeholder="Buscar por descrição ou categoria" value="<?php echo htmlspecialchars($filtro_busca); ?>">
                </div>
                
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> Filtrar
                    </button>
                    <a href="index.php?page=contas" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> Limpar Filtros
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Listagem de Lançamentos -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-list-ul"></i> Lançamentos Financeiros
        </div>
        <div class="card-body">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Data Venc.</th>
                        <th>Descrição</th>
                        <th>Categoria</th>
                        <th>Valor</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($lancamentos && $lancamentos->num_rows > 0): ?>
                        <?php while ($lancamento = $lancamentos->fetch_assoc()): ?>
                            <tr class="<?php echo $lancamento['status'] == 'cancelado' ? 'text-muted' : ''; ?>">
                                <td><?php echo date('d/m/Y', strtotime($lancamento['data_vencimento'])); ?></td>
                                <td>
                                    <?php echo htmlspecialchars($lancamento['descricao']); ?>
                                    <?php if ($lancamento['reserva_id']): ?>
                                        <br><small class="text-muted">Reserva: <?php echo htmlspecialchars($lancamento['reserva_info']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge" style="background-color: <?php echo $lancamento['categoria_cor']; ?>">
                                        <?php echo htmlspecialchars($lancamento['categoria_nome']); ?>
                                    </span>
                                </td>
                                <td class="<?php echo $lancamento['tipo'] == 'receita' ? 'text-success' : 'text-danger'; ?> fw-bold">
                                    R$ <?php echo number_format($lancamento['valor'], 2, ',', '.'); ?>
                                </td>
                                <td>
                                    <?php if ($lancamento['status'] == 'pendente'): ?>
                                        <span class="badge bg-warning text-dark">Pendente</span>
                                    <?php elseif ($lancamento['status'] == 'pago'): ?>
                                        <span class="badge bg-success">
                                            Pago em <?php echo date('d/m/Y', strtotime($lancamento['data_pagamento'])); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Cancelado</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-info btn-acao" 
                                                title="Visualizar Detalhes"
                                                data-bs-toggle="modal" 
                                                data-bs-target="#modalDetalhes" 
                                                data-descricao="<?php echo htmlspecialchars($lancamento['descricao']); ?>"
                                                data-categoria="<?php echo htmlspecialchars($lancamento['categoria_nome']); ?>"
                                                data-valor="R$ <?php echo number_format($lancamento['valor'], 2, ',', '.'); ?>"
                                                data-data-lancamento="<?php echo date('d/m/Y', strtotime($lancamento['data_lancamento'])); ?>"
                                                data-data-vencimento="<?php echo date('d/m/Y', strtotime($lancamento['data_vencimento'])); ?>"
                                                data-status="<?php echo $lancamento['status']; ?>"
                                                data-data-pagamento="<?php echo $lancamento['data_pagamento'] ? date('d/m/Y', strtotime($lancamento['data_pagamento'])) : ''; ?>"
                                                data-forma-pagamento="<?php echo htmlspecialchars($lancamento['forma_pagamento'] ?? ''); ?>"
                                                data-observacao="<?php echo htmlspecialchars($lancamento['observacao'] ?? ''); ?>"
                                                data-reserva-info="<?php echo htmlspecialchars($lancamento['reserva_info'] ?? ''); ?>"
                                                data-tipo="<?php echo $lancamento['tipo']; ?>">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        
                                        <?php if ($lancamento['status'] == 'pendente'): ?>
                                            <button type="button" class="btn btn-sm btn-primary btn-acao" 
                                                    title="Editar Lançamento"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#modalEditar" 
                                                    data-id="<?php echo $lancamento['id']; ?>"
                                                    data-tipo="<?php echo $lancamento['tipo']; ?>"
                                                    data-categoria-id="<?php echo $lancamento['categoria_id']; ?>"
                                                    data-descricao="<?php echo htmlspecialchars($lancamento['descricao']); ?>"
                                                    data-valor="<?php echo $lancamento['valor']; ?>"
                                                    data-data-lancamento="<?php echo $lancamento['data_lancamento']; ?>"
                                                    data-data-vencimento="<?php echo $lancamento['data_vencimento']; ?>"
                                                    data-reserva-id="<?php echo $lancamento['reserva_id'] ?? ''; ?>"
                                                    data-observacao="<?php echo htmlspecialchars($lancamento['observacao'] ?? ''); ?>">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            
                                            <button type="button" class="btn btn-sm btn-success btn-acao" 
                                                    title="<?php echo $lancamento['tipo'] == 'receita' ? 'Registrar Recebimento' : 'Registrar Pagamento'; ?>"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#modalPagar" 
                                                    data-id="<?php echo $lancamento['id']; ?>"
                                                    data-descricao="<?php echo htmlspecialchars($lancamento['descricao']); ?>"
                                                    data-valor="<?php echo number_format($lancamento['valor'], 2, ',', '.'); ?>"
                                                    data-tipo="<?php echo $lancamento['tipo']; ?>">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($lancamento['status'] != 'cancelado'): ?>
                                            <button type="button" class="btn btn-sm btn-danger btn-acao" 
                                                    title="Cancelar Lançamento"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#modalCancelar" 
                                                    data-id="<?php echo $lancamento['id']; ?>"
                                                    data-descricao="<?php echo htmlspecialchars($lancamento['descricao']); ?>">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center">Nenhum lançamento encontrado.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Modal Nova Receita -->
    <div class="modal fade" id="modalNovaReceita" tabindex="-1" aria-labelledby="modalNovaReceitaLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="modalNovaReceitaLabel">Nova Receita</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=contas_salvar" method="POST" id="formNovaReceita">
                        <input type="hidden" name="acao" value="cadastrar">
                        <input type="hidden" name="tipo" value="receita">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="categoria_id_receita" class="form-label">Categoria</label>
                                <select class="form-select" id="categoria_id_receita" name="categoria_id" required>
                                    <option value="">Selecione uma categoria</option>
                                    <?php 
                                    $categorias->data_seek(0);
                                    while ($categoria = $categorias->fetch_assoc()): 
                                        if ($categoria['tipo'] == 'receita'):
                                    ?>
                                        <option value="<?php echo $categoria['id']; ?>">
                                            <?php echo htmlspecialchars($categoria['nome']); ?>
                                        </option>
                                    <?php 
                                        endif;
                                    endwhile; 
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="valor_receita" class="form-label">Valor (R$)</label>
                                <input type="text" class="form-control" id="valor_receita" name="valor" placeholder="0,00" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="descricao_receita" class="form-label">Descrição</label>
                            <input type="text" class="form-control" id="descricao_receita" name="descricao" required>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="data_lancamento_receita" class="form-label">Data do Lançamento</label>
                                <input type="date" class="form-control" id="data_lancamento_receita" name="data_lancamento" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="data_vencimento_receita" class="form-label">Data de Vencimento</label>
                                <input type="date" class="form-control" id="data_vencimento_receita" name="data_vencimento" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reserva_id_receita" class="form-label">Reserva (opcional)</label>
                            <select class="form-select" id="reserva_id_receita" name="reserva_id">
                                <option value="">Selecione uma reserva</option>
                                <?php 
                                $reservas = $conn->query("SELECT r.id, r.uh, h.nome 
                                                         FROM reservas r 
                                                         JOIN hospedes h ON r.hospede_id = h.id 
                                                         WHERE r.pousada_id = $pousada_id 
                                                         ORDER BY r.dataentrada DESC 
                                                         LIMIT 50");
                                while ($reserva = $reservas->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo $reserva['id']; ?>">
                                        <?php echo htmlspecialchars($reserva['nome'] . ' - UH ' . $reserva['uh']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="observacao_receita" class="form-label">Observação</label>
                            <textarea class="form-control" id="observacao_receita" name="observacao" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="ja_pago_receita" name="ja_pago" onchange="togglePagamentoFields('receita')">
                            <label class="form-check-label" for="ja_pago_receita">Já foi recebido</label>
                        </div>
                        
                        <div id="pagamento_fields_receita" style="display: none;">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="data_pagamento_receita" class="form-label">Data de Recebimento</label>
                                    <input type="date" class="form-control" id="data_pagamento_receita" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="forma_pagamento_receita" class="form-label">Forma de Pagamento</label>
                                    <select class="form-select" id="forma_pagamento_receita" name="forma_pagamento">
                                        <option value="">Selecione</option>
                                        <?php 
                                        $formas_pagamento->data_seek(0);
                                        while ($forma = $formas_pagamento->fetch_assoc()): 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                                                <?php echo htmlspecialchars($forma['nome']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formNovaReceita" class="btn btn-success">Salvar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Nova Despesa -->
    <div class="modal fade" id="modalNovaDespesa" tabindex="-1" aria-labelledby="modalNovaDespesaLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalNovaDespesaLabel">Nova Despesa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=contas_salvar" method="POST" id="formNovaDespesa">
                        <input type="hidden" name="acao" value="cadastrar">
                        <input type="hidden" name="tipo" value="despesa">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="categoria_id_despesa" class="form-label">Categoria</label>
                                <select class="form-select" id="categoria_id_despesa" name="categoria_id" required>
                                    <option value="">Selecione uma categoria</option>
                                    <?php 
                                    $categorias->data_seek(0);
                                    while ($categoria = $categorias->fetch_assoc()): 
                                        if ($categoria['tipo'] == 'despesa'):
                                    ?>
                                        <option value="<?php echo $categoria['id']; ?>">
                                            <?php echo htmlspecialchars($categoria['nome']); ?>
                                        </option>
                                    <?php 
                                        endif;
                                    endwhile; 
                                    ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="valor_despesa" class="form-label">Valor (R$)</label>
                                <input type="text" class="form-control" id="valor_despesa" name="valor" placeholder="0,00" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="descricao_despesa" class="form-label">Descrição</label>
                            <input type="text" class="form-control" id="descricao_despesa" name="descricao" required>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="data_lancamento_despesa" class="form-label">Data do Lançamento</label>
                                <input type="date" class="form-control" id="data_lancamento_despesa" name="data_lancamento" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="data_vencimento_despesa" class="form-label">Data de Vencimento</label>
                                <input type="date" class="form-control" id="data_vencimento_despesa" name="data_vencimento" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="observacao_despesa" class="form-label">Observação</label>
                            <textarea class="form-control" id="observacao_despesa" name="observacao" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="ja_pago_despesa" name="ja_pago" onchange="togglePagamentoFields('despesa')">
                            <label class="form-check-label" for="ja_pago_despesa">Já foi pago</label>
                        </div>
                        
                        <div id="pagamento_fields_despesa" style="display: none;">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="data_pagamento_despesa" class="form-label">Data de Pagamento</label>
                                    <input type="date" class="form-control" id="data_pagamento_despesa" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="forma_pagamento_despesa" class="form-label">Forma de Pagamento</label>
                                    <select class="form-select" id="forma_pagamento_despesa" name="forma_pagamento">
                                        <option value="">Selecione</option>
                                        <?php 
                                        $formas_pagamento->data_seek(0);
                                        while ($forma = $formas_pagamento->fetch_assoc()): 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                                                <?php echo htmlspecialchars($forma['nome']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formNovaDespesa" class="btn btn-success">Salvar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Editar Lançamento -->
    <div class="modal fade" id="modalEditar" tabindex="-1" aria-labelledby="modalEditarLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalEditarLabel">Editar Lançamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=contas_salvar" method="POST" id="formEditar">
                        <input type="hidden" name="acao" value="editar">
                        <input type="hidden" name="id" id="editar_id">
                        <input type="hidden" name="tipo" id="editar_tipo">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editar_categoria_id" class="form-label">Categoria</label>
                                <select class="form-select" id="editar_categoria_id" name="categoria_id" required>
                                    <option value="">Selecione uma categoria</option>
                                    <optgroup label="Receitas" id="categorias_receita">
                                        <?php 
                                        $categorias->data_seek(0);
                                        while ($categoria = $categorias->fetch_assoc()): 
                                            if ($categoria['tipo'] == 'receita'):
                                        ?>
                                            <option value="<?php echo $categoria['id']; ?>">
                                                <?php echo htmlspecialchars($categoria['nome']); ?>
                                            </option>
                                        <?php 
                                            endif;
                                        endwhile; 
                                        ?>
                                    </optgroup>
                                    <optgroup label="Despesas" id="categorias_despesa">
                                        <?php 
                                        $categorias->data_seek(0);
                                        while ($categoria = $categorias->fetch_assoc()): 
                                            if ($categoria['tipo'] == 'despesa'):
                                        ?>
                                            <option value="<?php echo $categoria['id']; ?>">
                                                <?php echo htmlspecialchars($categoria['nome']); ?>
                                            </option>
                                        <?php 
                                            endif;
                                        endwhile; 
                                        ?>
                                    </optgroup>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="editar_valor" class="form-label">Valor (R$)</label>
                                <input type="text" class="form-control" id="editar_valor" name="valor" placeholder="0,00" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editar_descricao" class="form-label">Descrição</label>
                            <input type="text" class="form-control" id="editar_descricao" name="descricao" required>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editar_data_lancamento" class="form-label">Data do Lançamento</label>
                                <input type="date" class="form-control" id="editar_data_lancamento" name="data_lancamento" required>
                            </div>
                            <div class="col-md-6">
                                <label for="editar_data_vencimento" class="form-label">Data de Vencimento</label>
                                <input type="date" class="form-control" id="editar_data_vencimento" name="data_vencimento" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editar_reserva_id" class="form-label">Reserva (opcional)</label>
                            <select class="form-select" id="editar_reserva_id" name="reserva_id">
                                <option value="">Selecione uma reserva</option>
                                <?php 
                                $reservas->data_seek(0);
                                while ($reserva = $reservas->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo $reserva['id']; ?>">
                                        <?php echo htmlspecialchars($reserva['nome'] . ' - UH ' . $reserva['uh']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editar_observacao" class="form-label">Observação</label>
                            <textarea class="form-control" id="editar_observacao" name="observacao" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="marcar_como_pago" name="marcar_como_pago" onchange="togglePagamentoFieldsEditar()">
                            <label class="form-check-label" for="marcar_como_pago">Marcar como pago</label>
                        </div>
                        
                        <div id="pagamento_fields_editar" style="display: none;">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="editar_data_pagamento" class="form-label">Data de Pagamento</label>
                                    <input type="date" class="form-control" id="editar_data_pagamento" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <div class="col-md-6">
                                    <label for="editar_forma_pagamento" class="form-label">Forma de Pagamento</label>
                                    <select class="form-select" id="editar_forma_pagamento" name="forma_pagamento">
                                        <option value="">Selecione</option>
                                        <?php 
                                        $formas_pagamento->data_seek(0);
                                        while ($forma = $formas_pagamento->fetch_assoc()): 
                                        ?>
                                            <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                                                <?php echo htmlspecialchars($forma['nome']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formEditar" class="btn btn-primary">Salvar Alterações</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Registrar Pagamento -->
    <div class="modal fade" id="modalPagar" tabindex="-1" aria-labelledby="modalPagarLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="modalPagarLabel">Registrar Pagamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=contas_salvar" method="POST" id="formPagar">
                        <input type="hidden" name="acao" value="registrar_pagamento">
                        <input type="hidden" name="id" id="pagar_id">
                        
                        <div class="mb-3">
                            <label class="form-label">Descrição</label>
                            <p id="pagar_descricao" class="form-control-plaintext"></p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Valor</label>
                            <p id="pagar_valor" class="form-control-plaintext fw-bold"></p>
                        </div>
                        
                        <div class="mb-3">
                            <label for="data_pagamento" class="form-label">Data de Pagamento</label>
                            <input type="date" class="form-control" id="data_pagamento" name="data_pagamento" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="forma_pagamento" class="form-label">Forma de Pagamento</label>
                            <select class="form-select" id="forma_pagamento" name="forma_pagamento" required>
                                <option value="">Selecione</option>
                                <?php 
                                $formas_pagamento->data_seek(0);
                                while ($forma = $formas_pagamento->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo htmlspecialchars($forma['nome']); ?>">
                                        <?php echo htmlspecialchars($forma['nome']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="observacao_pagamento" class="form-label">Observação (opcional)</label>
                            <textarea class="form-control" id="observacao_pagamento" name="observacao_pagamento" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formPagar" class="btn btn-success">Confirmar Pagamento</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Cancelar Lançamento -->
    <div class="modal fade" id="modalCancelar" tabindex="-1" aria-labelledby="modalCancelarLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalCancelarLabel">Cancelar Lançamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=contas_salvar" method="POST" id="formCancelar">
                        <input type="hidden" name="acao" value="cancelar">
                        <input type="hidden" name="id" id="cancelar_id">
                        
                        <div class="mb-3">
                            <p>Tem certeza que deseja cancelar o lançamento <strong id="cancelar_descricao"></strong>?</p>
                            <p class="text-danger">Esta ação não poderá ser desfeita.</p>
                        </div>
                        
                        <div class="mb-3">
                            <label for="motivo_cancelamento" class="form-label">Motivo do Cancelamento</label>
                            <textarea class="form-control" id="motivo_cancelamento" name="motivo_cancelamento" rows="3" required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Não, Voltar</button>
                    <button type="submit" form="formCancelar" class="btn btn-danger">Sim, Cancelar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Detalhes do Lançamento -->
    <div class="modal fade" id="modalDetalhes" tabindex="-1" aria-labelledby="modalDetalhesLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDetalhesLabel">Detalhes do Lançamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="fw-bold">Descrição:</label>
                        <p id="detalhes_descricao"></p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="fw-bold">Categoria:</label>
                            <p id="detalhes_categoria"></p>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-bold">Valor:</label>
                            <p id="detalhes_valor" class="fw-bold"></p>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="fw-bold">Data do Lançamento:</label>
                            <p id="detalhes_data_lancamento"></p>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-bold">Data de Vencimento:</label>
                            <p id="detalhes_data_vencimento"></p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="fw-bold">Status:</label>
                        <p id="detalhes_status_container">
                            <span id="detalhes_status" class="badge"></span>
                        </p>
                    </div>
                    
                    <div id="detalhes_pagamento_container" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="fw-bold">Data de Pagamento:</label>
                                <p id="detalhes_data_pagamento"></p>
                            </div>
                            <div class="col-md-6">
                                <label class="fw-bold">Forma de Pagamento:</label>
                                <p id="detalhes_forma_pagamento"></p>
                            </div>
                        </div>
                    </div>
                    
                    <div id="detalhes_reserva_container" style="display: none;">
                        <div class="mb-3">
                            <label class="fw-bold">Reserva:</label>
                            <p id="detalhes_reserva_info"></p>
                        </div>
                    </div>
                    
                    <div id="detalhes_observacao_container" style="display: none;">
                        <div class="mb-3">
                            <label class="fw-bold">Observação:</label>
                            <p id="detalhes_observacao"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Função para formatar valores monetários
    function formatarMoeda(valor) {
        return parseFloat(valor).toLocaleString('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        });
    }
    
    // Função para mostrar/esconder campos de pagamento
    function togglePagamentoFields(tipo) {
        const checkboxPago = document.getElementById('ja_pago_' + tipo);
        const pagamentoFields = document.getElementById('pagamento_fields_' + tipo);
        
        if (checkboxPago.checked) {
            pagamentoFields.style.display = 'block';
        } else {
            pagamentoFields.style.display = 'none';
        }
    }
    
    // Função para mostrar/esconder campos de pagamento no formulário de edição
    function togglePagamentoFieldsEditar() {
        const checkboxPago = document.getElementById('marcar_como_pago');
        const pagamentoFields = document.getElementById('pagamento_fields_editar');
        
        if (checkboxPago.checked) {
            pagamentoFields.style.display = 'block';
        } else {
            pagamentoFields.style.display = 'none';
        }
    }
    
    // Configurar máscaras e eventos quando o DOM estiver pronto
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar máscaras para campos de valor
        const camposValor = document.querySelectorAll('input[name="valor"]');
        camposValor.forEach(function(campo) {
            campo.addEventListener('input', function(e) {
                let valor = e.target.value.replace(/\D/g, '');
                if (valor === '') {
                    e.target.value = '';
                    return;
                }
                valor = (parseFloat(valor) / 100).toFixed(2).replace('.', ',');
                e.target.value = valor;
            });
        });
        
        // Configurar o modal de edição
        const modalEditar = document.getElementById('modalEditar');
        if (modalEditar) {
            modalEditar.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const tipo = button.getAttribute('data-tipo');
                const categoriaId = button.getAttribute('data-categoria-id');
                const descricao = button.getAttribute('data-descricao');
                const valor = button.getAttribute('data-valor');
                const dataLancamento = button.getAttribute('data-data-lancamento');
                const dataVencimento = button.getAttribute('data-data-vencimento');
                const reservaId = button.getAttribute('data-reserva-id');
                const observacao = button.getAttribute('data-observacao');
                
                document.getElementById('editar_id').value = id;
                document.getElementById('editar_tipo').value = tipo;
                document.getElementById('editar_categoria_id').value = categoriaId;
                document.getElementById('editar_descricao').value = descricao;
                document.getElementById('editar_valor').value = valor.replace('.', ',');
                document.getElementById('editar_data_lancamento').value = dataLancamento;
                document.getElementById('editar_data_vencimento').value = dataVencimento;
                document.getElementById('editar_reserva_id').value = reservaId || '';
                document.getElementById('editar_observacao').value = observacao || '';
                
                // Mostrar apenas as categorias do tipo correto
                const categoriasReceita = document.getElementById('categorias_receita');
                const categoriasDespesa = document.getElementById('categorias_despesa');
                
                if (tipo === 'receita') {
                    categoriasReceita.style.display = '';
                    categoriasDespesa.style.display = 'none';
                } else {
                    categoriasReceita.style.display = 'none';
                    categoriasDespesa.style.display = '';
                }
            });
        }
        
        // Configurar o modal de pagamento
        const modalPagar = document.getElementById('modalPagar');
        if (modalPagar) {
            modalPagar.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const descricao = button.getAttribute('data-descricao');
                const valor = button.getAttribute('data-valor');
                const tipo = button.getAttribute('data-tipo');
                
                document.getElementById('pagar_id').value = id;
                document.getElementById('pagar_descricao').textContent = descricao;
                document.getElementById('pagar_valor').textContent = `R$ ${valor}`;
                
                // Ajustar título do modal conforme o tipo
                const titulo = tipo === 'receita' ? 'Registrar Recebimento' : 'Registrar Pagamento';
                document.getElementById('modalPagarLabel').textContent = titulo;
                
                // Ajustar texto do botão conforme o tipo
                const botao = tipo === 'receita' ? 'Confirmar Recebimento' : 'Confirmar Pagamento';
                const submitButton = document.querySelector('#formPagar button[type="submit"]');
                if (submitButton) {
                    submitButton.textContent = botao;
                } else {
                    console.warn('Botão de submit não encontrado no formulário #formPagar');
                }
            });
        }
        
        
        // Configurar o modal de cancelamento
        const modalCancelar = document.getElementById('modalCancelar');
        if (modalCancelar) {
            modalCancelar.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const id = button.getAttribute('data-id');
                const descricao = button.getAttribute('data-descricao');
                
                document.getElementById('cancelar_id').value = id;
                document.getElementById('cancelar_descricao').textContent = descricao;
            });
        }
        
        // Configurar o modal de detalhes
        const modalDetalhes = document.getElementById('modalDetalhes');
        if (modalDetalhes) {
            modalDetalhes.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const descricao = button.getAttribute('data-descricao');
                const categoria = button.getAttribute('data-categoria');
                const valor = button.getAttribute('data-valor');
                const dataLancamento = button.getAttribute('data-data-lancamento');
                const dataVencimento = button.getAttribute('data-data-vencimento');
                const status = button.getAttribute('data-status');
                const dataPagamento = button.getAttribute('data-data-pagamento');
                const formaPagamento = button.getAttribute('data-forma-pagamento');
                const observacao = button.getAttribute('data-observacao');
                const reservaInfo = button.getAttribute('data-reserva-info');
                const tipo = button.getAttribute('data-tipo');
                
                document.getElementById('detalhes_descricao').textContent = descricao;
                document.getElementById('detalhes_categoria').textContent = categoria;
                
                const valorElement = document.getElementById('detalhes_valor');
                valorElement.textContent = valor;
                valorElement.className = tipo === 'receita' ? 'fw-bold text-success' : 'fw-bold text-danger';
                
                document.getElementById('detalhes_data_lancamento').textContent = dataLancamento;
                document.getElementById('detalhes_data_vencimento').textContent = dataVencimento;
                
                const statusElement = document.getElementById('detalhes_status');
                statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                
                if (status === 'pendente') {
                    statusElement.className = 'badge bg-warning text-dark';
                } else if (status === 'pago') {
                    statusElement.className = 'badge bg-success';
                } else {
                    statusElement.className = 'badge bg-danger';
                }
                
                // Mostrar ou esconder informações de pagamento
                const pagamentoContainer = document.getElementById('detalhes_pagamento_container');
                if (status === 'pago' && dataPagamento) {
                    pagamentoContainer.style.display = 'block';
                    document.getElementById('detalhes_data_pagamento').textContent = dataPagamento;
                    document.getElementById('detalhes_forma_pagamento').textContent = formaPagamento;
                } else {
                    pagamentoContainer.style.display = 'none';
                }
                
                // Mostrar ou esconder informações da reserva
                const reservaContainer = document.getElementById('detalhes_reserva_container');
                if (reservaInfo) {
                    reservaContainer.style.display = 'block';
                    document.getElementById('detalhes_reserva_info').textContent = reservaInfo;
                } else {
                    reservaContainer.style.display = 'none';
                }
                
                // Mostrar ou esconder observações
                const observacaoContainer = document.getElementById('detalhes_observacao_container');
                if (observacao) {
                    observacaoContainer.style.display = 'block';
                    document.getElementById('detalhes_observacao').textContent = observacao;
                } else {
                    observacaoContainer.style.display = 'none';
                }
            });
        }
        
        // Inicializar máscaras para campos de valor existentes
        document.querySelectorAll('input[name="valor"]').forEach(function(input) {
            input.addEventListener('blur', function() {
                if (this.value !== '') {
                    // Garantir que o valor tenha duas casas decimais
                    let valor = this.value.replace(',', '.');
                    valor = parseFloat(valor).toFixed(2);
                    this.value = valor.replace('.', ',');
                }
            });
        });
        
        // Verificar alertas de vencimento
        function verificarAlertasVencimento() {
            const hoje = new Date();
            const lancamentos = document.querySelectorAll('tr[data-vencimento]');
            
            lancamentos.forEach(function(lancamento) {
                const dataVencimento = new Date(lancamento.getAttribute('data-vencimento'));
                const status = lancamento.getAttribute('data-status');
                
                // Verificar apenas lançamentos pendentes
                if (status === 'pendente') {
                    const diasRestantes = Math.floor((dataVencimento - hoje) / (1000 * 60 * 60 * 24));
                    
                    if (diasRestantes < 0) {
                        // Vencido
                        lancamento.classList.add('table-danger');
                    } else if (diasRestantes <= 3) {
                        // Próximo do vencimento
                        lancamento.classList.add('table-warning');
                    }
                }
            });
        }
        
        // Executar verificação de alertas ao carregar a página
        verificarAlertasVencimento();
        
        // Função para validar formulários antes do envio
        function validarFormulario(formId) {
            const form = document.getElementById(formId);
            if (!form) return true;
            
            let valido = true;
            
            // Validar campos obrigatórios
            form.querySelectorAll('[required]').forEach(function(campo) {
                if (!campo.value.trim()) {
                    campo.classList.add('is-invalid');
                    valido = false;
                } else {
                    campo.classList.remove('is-invalid');
                }
            });
            
            // Validar valor
            const campoValor = form.querySelector('input[name="valor"]');
            if (campoValor) {
                const valor = parseFloat(campoValor.value.replace(',', '.'));
                if (isNaN(valor) || valor <= 0) {
                    campoValor.classList.add('is-invalid');
                    valido = false;
                }
            }
            
            // Se for um pagamento, validar campos específicos
            const checkboxPago = form.querySelector('input[type="checkbox"][name="ja_pago"], input[type="checkbox"][name="marcar_como_pago"]');
            if (checkboxPago && checkboxPago.checked) {
                const dataPagamento = form.querySelector('input[name="data_pagamento"]');
                const formaPagamento = form.querySelector('select[name="forma_pagamento"]');
                
                if (dataPagamento && !dataPagamento.value) {
                    dataPagamento.classList.add('is-invalid');
                    valido = false;
                }
                
                if (formaPagamento && !formaPagamento.value) {
                    formaPagamento.classList.add('is-invalid');
                    valido = false;
                }
            }
            
            return valido;
        }
        
        // Adicionar validação aos formulários antes do envio
        ['formNovaReceita', 'formNovaDespesa', 'formEditar', 'formPagar'].forEach(function(formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validarFormulario(formId)) {
                        e.preventDefault();
                        alert('Por favor, preencha todos os campos obrigatórios corretamente.');
                    }
                });
            }
        });
    });

</script>
        
