<?php
session_start();

// Definir header para JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Verificar se usuário está logado
if (!isset($_SESSION['user_id']) || !isset($_SESSION['pousada_cores'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Usuário não logado ou cores não disponíveis'
    ]);
    exit();
}

// Retornar cores da sessão
$cores = $_SESSION['pousada_cores'];

echo json_encode([
    'success' => true,
    'primaria' => $cores['primaria'],
    'clara' => $cores['clara'],
    'escura' => $cores['escura'],
    'rgb' => $cores['rgb']
]);
?>
