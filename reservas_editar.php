<?php
include_once("config.php");

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
	$user_id = $_SESSION['user_id'];
} else {
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

$reserva_id = $_GET["reserva_id"];
$hospede_nome = $_GET["hospede_nome"];

$sql = "SELECT * FROM reservas WHERE id='$reserva_id'";
$res = $conn->query($sql);
$row = $res->fetch_object();
?>

<center>
	<div class="form-group no-print">
		<button onclick="location.href='index.php';" class="btn btn-success">Home</button>
		<button type="button" class="btn btn-secondary" id="print-button-top">Imprimir</button>
	</div>
</center>

<div class="text-center mb-3 print-only">
	<img src="<?php echo $_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png'; ?>" alt="Logo" style="max-width: 200px; max-height: 100px;">
</div>

<?php
// CONFIGURAÇÃO PARA FORMULÁRIO REUTILIZÁVEL
// Contexto: Via index.php (não AJAX)
$form_action = '?page=reservas_salvar';
$hidden_fields = '<input type="hidden" name="acao" value="editar">
                  <input type="hidden" name="reserva_id" value="' . htmlspecialchars($row->id) . '">
                  <input type="hidden" name="hospede_id" value="' . htmlspecialchars($row->hospede_id) . '">
                  <input type="hidden" name="hospede_nome" value="' . htmlspecialchars($hospede_nome) . '">';
$form_id = 'reservas';

// Preencher valores dos campos com dados do banco
$uh = $row->uh;
$numacomp = $row->numacomp;
$valor = $row->valor;
$dataentrada = $row->dataentrada;
$horaentrada = $row->horaentrada;
$datasaida = $row->datasaida;
$horasaida = $row->horasaida;
$acompanhantes = $row->acompanhantes;
$vemde = $row->vemde;
$vaipara = $row->vaipara;
$motivo = $row->motivo;
$transporte = $row->transporte;
$observacao = $row->observacao;

// Configurações específicas para edição
$titulo_reserva = 'Editar Reserva de:'; // Adicionar esta linha

// Substituir o texto do botão para "Alterar"
$button_text = 'Alterar';

// Incluir o formulário reutilizável
include 'formulario_reserva.php';
?>

<center>
<br>
<div id="qrcode"></div>
<br>
<div class="form-group no-print">
	<button type="button" class="btn btn-secondary" id="print-button-bottom">Imprimir</button>
</div>
</center>

<?php
print "<center>";
print "<button onclick=\"location.href='index.php';\" class='btn btn-success'>Home</button>";
print "</center>";
?>

<script type="text/javascript" src="instascan/qrcode.js"></script>
<script src="custom/js/func.js"></script>

<script>
	// Adiciona eventos de clique aos botões de impressão
	document.getElementById("print-button-top").addEventListener("click", imprimir);
	document.getElementById("print-button-bottom").addEventListener("click", imprimir);

	function createQrCode(id) {
		var qrcode = new QRCode("qrcode", {
			text: id,
			width: 80,
			height: 80,
			colorDark: "black",
			colorLight: "white",
			correctLevel: QRCode.CorrectLevel.H
		});
	}

	// Chama a função para criar o QR Code passando o ID diretamente
	createQrCode("<?php echo $row->id; ?>");

	function imprimir() {
		window.print();
	}

	document.addEventListener('DOMContentLoaded', function() {
		const reservaModal = document.getElementById('reservaModal');
		if (reservaModal) {  // Verificar se o elemento existe
			reservaModal.addEventListener('show.bs.modal', function (event) {
				const cell = event.relatedTarget;
				const uh = cell.getAttribute('data-uh');
				const data = cell.getAttribute('data-data');
				const reservaId = cell.getAttribute('data-reserva-id');
				const hospedeNome = cell.getAttribute('data-hospede-nome') || 'Não há reserva';
				const entrada = cell.getAttribute('data-entrada') || 'N/A';
				const saida = cell.getAttribute('data-saida') || 'N/A';

				document.getElementById('modalUh').textContent = uh;
				document.getElementById('modalData').textContent = data;
				document.getElementById('modalHospede').textContent = hospedeNome;
				document.getElementById('modalEntrada').textContent = entrada;
				document.getElementById('modalSaida').textContent = saida;

				const editarLink = document.getElementById('editarReservaLink');
				if (reservaId) {
					// Use index.php routing instead of direct link
					editarLink.href = `index.php?page=reservas_editar&id=${reservaId}`;
					editarLink.style.display = 'inline-block';
				} else {
					editarLink.style.display = 'none';
				}
			});
		}

		// Adicionar verificação de conflitos para edição
		const dataEntradaInput = document.querySelector('input[name="dataentrada"]');
		const dataSaidaInput = document.querySelector('input[name="datasaida"]');
		const horaEntradaInput = document.querySelector('input[name="horaentrada"]');
		const horaSaidaInput = document.querySelector('input[name="horasaida"]');
		const uhInput = document.querySelector('input[name="uh"]');
		const reservaIdInput = document.querySelector('input[name="reserva_id"]');
		
		// Criar elemento para o aviso de data de saída inválida
		const avisoDataSaida = document.createElement('div');
		avisoDataSaida.className = 'alert alert-danger alert-dismissible fade show';
		avisoDataSaida.style.position = 'absolute';
		avisoDataSaida.style.zIndex = '100';
		avisoDataSaida.style.marginTop = '5px';
		avisoDataSaida.style.width = '250px';
		avisoDataSaida.style.fontSize = '12px';
		avisoDataSaida.style.padding = '5px';
		avisoDataSaida.style.display = 'none';
		// Adicionar tabindex=-1 para que o alerta não entre na ordem de tabulação
		avisoDataSaida.setAttribute('tabindex', '-1');
		avisoDataSaida.innerHTML = `
			<strong>Erro!</strong> Data de saída deve ser posterior à data de entrada.
			<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar" style="padding: 2px; font-size: 10px;" tabindex="-1"></button>
		`;
		
		// Inserir o aviso após o campo de data de saída
		if (dataSaidaInput) {
			dataSaidaInput.parentNode.appendChild(avisoDataSaida);
		}
		
		// Função para verificar se a data de saída é válida
		function verificarDataSaida() {
			if (!dataEntradaInput.value || !dataSaidaInput.value) return;
			
			// Obter as datas como strings no formato YYYY-MM-DD
			const dataEntradaStr = dataEntradaInput.value;
			const dataSaidaStr = dataSaidaInput.value;
			
			// Comparar as strings de data diretamente
			if (dataSaidaStr < dataEntradaStr) {
				// Mostrar aviso para data de saída inválida
				avisoDataSaida.style.display = 'block';
				
				// Adicionar classe para destacar o campo
				dataSaidaInput.classList.add('border-danger');
				dataSaidaInput.style.backgroundColor = 'rgba(220, 53, 69, 0.1)';
			} else {
				// Esconder aviso e remover destaque
				avisoDataSaida.style.display = 'none';
				dataSaidaInput.classList.remove('border-danger');
				dataSaidaInput.style.backgroundColor = '';
				
				// Se as datas forem iguais, ajustar a hora de saída
				if (dataEntradaStr === dataSaidaStr) {
					ajustarHoraSaida();
				}
			}
		}
		
		// Função para ajustar a hora de saída quando as datas são iguais
		function ajustarHoraSaida() {
			if (dataEntradaInput.value === dataSaidaInput.value) {
				if (horaEntradaInput.value) {
					// Pegar a hora de entrada
					const [horasEntrada, minutosEntrada] = horaEntradaInput.value.split(':').map(Number);
					
					// Adicionar 2 horas
					let novasHoras = horasEntrada + 2;
					let novosMinutos = minutosEntrada;
					
					// Se passar de 23h, ajustar para 23:59
					if (novasHoras > 23) {
						novasHoras = 23;
						novosMinutos = 59;
					}
					
					// Formatar a nova hora
					const novaHoraSaida = `${String(novasHoras).padStart(2, '0')}:${String(novosMinutos).padStart(2, '0')}`;
					
					// Definir a nova hora de saída
					horaSaidaInput.value = novaHoraSaida;
				}
			}
		}
		
		// Verificar quando o usuário selecionar uma data de entrada
		if (dataEntradaInput) {
			dataEntradaInput.addEventListener('change', function() {
				verificarConflitosEdicao();
				
				// Se a data de entrada for alterada e for posterior à data de saída,
				// atualizar a data de saída para o mesmo dia
				if (dataSaidaInput.value && dataSaidaInput.value < dataEntradaInput.value) {
					dataSaidaInput.value = dataEntradaInput.value;
					// Remover destaque de erro
					dataSaidaInput.classList.remove('border-danger');
					dataSaidaInput.style.backgroundColor = '';
					avisoDataSaida.style.display = 'none';
					
					// Ajustar a hora de saída já que as datas são iguais
					ajustarHoraSaida();
				} else if (dataSaidaInput.value && dataSaidaInput.value === dataEntradaInput.value) {
					// Se as datas já eram iguais, ajustar a hora de saída
					ajustarHoraSaida();
				}
			});
		}
		
		// Verificar quando o usuário selecionar uma data de saída
		if (dataSaidaInput) {
			dataSaidaInput.addEventListener('change', verificarDataSaida);
			
			// Quando o usuário sair do campo de data de saída
			dataSaidaInput.addEventListener('blur', function() {
				// Esconder o aviso
				avisoDataSaida.style.display = 'none';
				
				// Se a data de saída for menor que a data de entrada, corrigir automaticamente
				if (dataEntradaInput.value && dataSaidaInput.value) {
					if (dataSaidaInput.value < dataEntradaInput.value) {
						dataSaidaInput.value = dataEntradaInput.value;
						dataSaidaInput.classList.remove('border-danger');
						dataSaidaInput.style.backgroundColor = '';
						
						// Ajustar a hora de saída já que as datas são iguais
						ajustarHoraSaida();
					}
				}
			});
			
			// Garantir que o tab funcione corretamente após o campo de data de saída
			dataSaidaInput.addEventListener('keydown', function(event) {
				if (event.key === 'Tab' && !event.shiftKey) {
					// Se o usuário pressionar Tab, garantir que o foco vá para o campo de hora de saída
					if (avisoDataSaida.style.display === 'block') {
						event.preventDefault();
						horaSaidaInput.focus();
					}
				}
			});
			
			// Mostrar novamente o aviso se o campo tiver foco e houver problemas
			dataSaidaInput.addEventListener('focus', function() {
				if (dataSaidaInput.classList.contains('border-danger')) {
					avisoDataSaida.style.display = 'block';
				}
			});
		}
		
		// Quando o usuário alterar a hora de entrada
		if (horaEntradaInput) {
			horaEntradaInput.addEventListener('change', function() {
				verificarConflitosEdicao();
				// Se as datas forem iguais, ajustar a hora de saída
				if (dataEntradaInput.value && dataSaidaInput.value && 
					dataEntradaInput.value === dataSaidaInput.value) {
					ajustarHoraSaida();
				}
			});
		}
		
		// Verificar ao carregar a página (caso já venha com datas preenchidas)
		if (dataEntradaInput && dataEntradaInput.value && dataSaidaInput && dataSaidaInput.value) {
			verificarDataSaida();
		}
	});
	
	// FALTA: Validação no envio do formulário
	document.querySelector('form').addEventListener('submit', function(event) {
		let temErro = false;
		
		// Verificar se há avisos de conflito visíveis
		const avisosConflito = document.querySelectorAll('.aviso-conflito');
		if (avisosConflito.length > 0) {
			alert('Existe um conflito de reserva. Por favor, escolha outra data ou UH antes de continuar.');
			event.preventDefault();
			dataEntradaInput.focus();
			return false;
		}
		
		// Verificar data de saída
		const dataEntradaInput = document.querySelector('input[name="dataentrada"]');
		const dataSaidaInput = document.querySelector('input[name="datasaida"]');
		
		if (dataEntradaInput.value && dataSaidaInput.value) {
			if (dataSaidaInput.value < dataEntradaInput.value) {
				alert('A data de saída não pode ser anterior à data de entrada. Por favor, corrija antes de continuar.');
				event.preventDefault();
				temErro = true;
				dataSaidaInput.focus();
			}
		}
	});
	
	function verificarConflitosEdicao() {
		const dataEntradaInput = document.querySelector('input[name="dataentrada"]');
		const uhInput = document.querySelector('input[name="uh"]');
		const horaEntradaInput = document.querySelector('input[name="horaentrada"]');
		const reservaIdInput = document.querySelector('input[name="reserva_id"]');
		
		if (!uhInput.value || !dataEntradaInput.value) return;
		
		// Remover avisos anteriores
		removerAvisoConflito(uhInput);
		removerAvisoConflito(dataEntradaInput);
		
		// Para edição, precisamos verificar conflitos excluindo a reserva atual
		const formData = new FormData();
		formData.append('uh', uhInput.value);
		formData.append('dataEntrada', dataEntradaInput.value);
		formData.append('horaEntrada', horaEntradaInput.value || '13:00');
		formData.append('reserva_id_excluir', reservaIdInput.value); // Excluir a reserva atual da verificação
		
		fetch('verificar_disponibilidade.php', {
			method: 'POST',
			body: formData
		})
		.then(response => response.json())
		.then(data => {
			if (!data.disponivel) {
				mostrarAvisoConflito(dataEntradaInput, data.mensagem);
			}
		})
		.catch(error => {
			console.error('4_Erro ao verificar disponibilidade:', error);
		});
	}
	
	// Formatação automática do campo UH com zeros à esquerda
	const uhInput = document.querySelector('input[name="uh"]');
	if (uhInput) {
		uhInput.addEventListener('blur', function() {
			let valor = this.value.trim();
			if (valor && !isNaN(valor)) {
				// Converte para número e formata com 3 dígitos
				this.value = String(parseInt(valor)).padStart(3, '0');
			}
		});
	}
</script>