<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";        
    return;
}

include_once("config.php");

// Verificar se o método de requisição é POST
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    $_SESSION['mensagem'] = "Método de requisição inválido.";
    echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
    return;
}

// Obter a ação a ser executada
$acao = $_POST['acao'] ?? '';

// Processar de acordo com a ação
switch ($acao) {
    case 'cadastrar':
        cadastrarLancamento($conn, $user_id, $pousada_id);
        break;
    case 'editar':
        editarLancamento($conn, $user_id, $pousada_id);
        break;
    case 'registrar_pagamento':
        registrarPagamento($conn, $user_id, $pousada_id);
        break;
    case 'cancelar':
        cancelarLancamento($conn, $user_id, $pousada_id);
        break;
    default:
        $_SESSION['mensagem'] = "Ação inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
}

// Função para validar formato de data
function validarData($data) {
    if (empty($data)) return false;
    
    $formatoData = '/^\d{4}-\d{2}-\d{2}$/'; // Formato YYYY-MM-DD
    if (!preg_match($formatoData, $data)) return false;
    
    $partes = explode('-', $data);
    return checkdate($partes[1], $partes[2], $partes[0]);
}

// Função para cadastrar um novo lançamento
function cadastrarLancamento($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $tipo = $_POST['tipo'] ?? '';
    $categoria_id = isset($_POST['categoria_id']) ? intval($_POST['categoria_id']) : 0;
    $descricao = $_POST['descricao'] ?? '';
    $valor = isset($_POST['valor']) ? str_replace(',', '.', $_POST['valor']) : 0;
    $data_lancamento = $_POST['data_lancamento'] ?? '';
    if (empty($data_lancamento)) {
        $data_lancamento = date('Y-m-d'); // Define a data atual se estiver vazia
    }
    $data_vencimento = $_POST['data_vencimento'] ?? '';
    $reserva_id = isset($_POST['reserva_id']) && !empty($_POST['reserva_id']) ? intval($_POST['reserva_id']) : null;
    $observacao = $_POST['observacao'] ?? '';
    $ja_pago = isset($_POST['ja_pago']) ? 1 : 0;
    
    // Validar campos obrigatórios
    if (empty($tipo) || empty($categoria_id) || empty($descricao) || empty($valor) || empty($data_lancamento) || empty($data_vencimento)) {
        $_SESSION['mensagem'] = "Todos os campos obrigatórios devem ser preenchidos.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar tipo
    if ($tipo != 'receita' && $tipo != 'despesa') {
        $_SESSION['mensagem'] = "Tipo de lançamento inválido.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar valor
    if (!is_numeric($valor) || $valor <= 0) {
        $_SESSION['mensagem'] = "O valor deve ser um número positivo.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar datas
    if (!validarData($data_lancamento) || !validarData($data_vencimento)) {
        $_SESSION['mensagem'] = "Data inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se a categoria pertence à pousada
    $stmt = $conn->prepare("SELECT id, tipo FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $categoria_id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Categoria inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    $categoria = $result->fetch_assoc();
    
    // Verificar se o tipo da categoria corresponde ao tipo do lançamento
    if ($categoria['tipo'] != $tipo) {
        $_SESSION['mensagem'] = "O tipo da categoria não corresponde ao tipo do lançamento.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se a reserva pertence à pousada, se fornecida
    if ($reserva_id !== null) {
        $stmt = $conn->prepare("SELECT id FROM reservas WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $reserva_id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Reserva inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
        }
    }
    
    // Definir status e dados de pagamento
    $status = 'pendente';
    $data_pagamento = null;
    $forma_pagamento = null;
    
    if ($ja_pago) {
        $status = 'pago';
        $data_pagamento = $_POST['data_pagamento'] ?? date('Y-m-d');
        $forma_pagamento = $_POST['forma_pagamento'] ?? '';
        
        // Validar data de pagamento
        if (!validarData($data_pagamento)) {
            $_SESSION['mensagem'] = "Data de pagamento inválida.";
            echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
            return;
        }
        
        // Validar forma de pagamento
        if (empty($forma_pagamento)) {
            $_SESSION['mensagem'] = "Forma de pagamento é obrigatória para lançamentos pagos.";
            echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
            return;
        }
    }
    
    // Inserir o lançamento no banco de dados
    $stmt = $conn->prepare("INSERT INTO lancamentos_financeiros 
                           (pousada_id, reserva_id, tipo, categoria_id, descricao, valor, 
                            data_lancamento, data_vencimento, data_pagamento, status, 
                            forma_pagamento, observacao, usuario_id) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param("iisissssssssi", 
                     $pousada_id, $reserva_id, $tipo, $categoria_id, $descricao, $valor, 
                     $data_lancamento, $data_vencimento, $data_pagamento, $status, 
                     $forma_pagamento, $observacao, $user_id);
    
    if ($stmt->execute()) {
        $lancamento_id = $conn->insert_id;
        
        // Se o lançamento foi pago, registrar no caixa se houver um caixa aberto
        if ($status == 'pago') {
            registrarMovimentacaoCaixa($conn, $lancamento_id, $tipo, $valor, $descricao, $user_id, $pousada_id, false, $forma_pagamento);
        }
        
        $_SESSION['mensagem'] = "Lançamento cadastrado com sucesso.";
    } else {
        $_SESSION['mensagem'] = "Erro ao cadastrar lançamento: " . $conn->error;
    }
    
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
}

// Função para editar um lançamento existente
function editarLancamento($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $tipo = $_POST['tipo'] ?? '';
    $categoria_id = isset($_POST['categoria_id']) ? intval($_POST['categoria_id']) : 0;
    $descricao = $_POST['descricao'] ?? '';
    $valor = isset($_POST['valor']) ? str_replace(',', '.', $_POST['valor']) : 0;
    $data_lancamento = $_POST['data_lancamento'] ?? '';
    $data_vencimento = $_POST['data_vencimento'] ?? '';
    $reserva_id = isset($_POST['reserva_id']) && !empty($_POST['reserva_id']) ? intval($_POST['reserva_id']) : null;
    $observacao = $_POST['observacao'] ?? '';
    $marcar_como_pago = isset($_POST['marcar_como_pago']) ? 1 : 0;
    
    // Validar ID
    if ($id <= 0) {
        $_SESSION['mensagem'] = "ID do lançamento inválido.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se o lançamento existe e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM lancamentos_financeiros WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Lançamento não encontrado.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    $lancamento_atual = $result->fetch_assoc();
    
    // Verificar se o lançamento já está cancelado
    if ($lancamento_atual['status'] == 'cancelado') {
        $_SESSION['mensagem'] = "Não é possível editar um lançamento cancelado.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar campos obrigatórios
    if (empty($tipo) || empty($categoria_id) || empty($descricao) || empty($valor) || empty($data_lancamento) || empty($data_vencimento)) {
        $_SESSION['mensagem'] = "Todos os campos obrigatórios devem ser preenchidos.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar tipo
    if ($tipo != 'receita' && $tipo != 'despesa') {
        $_SESSION['mensagem'] = "Tipo de lançamento inválido.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Se o lançamento já estiver pago, não permitir alterar o valor ou o tipo
    if ($lancamento_atual['status'] == 'pago') {
        $valor = $lancamento_atual['valor'];
        $tipo = $lancamento_atual['tipo'];
    } else {
        // Validar valor
        if (!is_numeric($valor) || $valor <= 0) {
            $_SESSION['mensagem'] = "O valor deve ser um número positivo.";
            echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
            return;
        }
    }
    
    // Validar datas
    if (!validarData($data_lancamento) || !validarData($data_vencimento)) {
        $_SESSION['mensagem'] = "Data inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se a categoria pertence à pousada
    $stmt = $conn->prepare("SELECT id, tipo FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $categoria_id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Categoria inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    $categoria = $result->fetch_assoc();
    
    // Verificar se o tipo da categoria corresponde ao tipo do lançamento
    if ($categoria['tipo'] != $tipo) {
        $_SESSION['mensagem'] = "O tipo da categoria não corresponde ao tipo do lançamento.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se a reserva pertence à pousada, se fornecida
    if ($reserva_id !== null) {
        $stmt = $conn->prepare("SELECT id FROM reservas WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $reserva_id, $pousada_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows == 0) {
            $_SESSION['mensagem'] = "Reserva inválida.";
            echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
            return;
        }
    }
    
    // Definir status e dados de pagamento
    $status = $lancamento_atual['status'];
    $data_pagamento = $lancamento_atual['data_pagamento'];
    $forma_pagamento = $lancamento_atual['forma_pagamento'];
    
    if ($marcar_como_pago && $status == 'pendente') {
        $status = 'pago';
        $data_pagamento = $_POST['data_pagamento'] ?? date('Y-m-d');
        $forma_pagamento = $_POST['forma_pagamento'] ?? '';
        
        // Validar data de pagamento
        if (!validarData($data_pagamento)) {
            $_SESSION['mensagem'] = "Data de pagamento inválida.";
            echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
            return;
        }
        
        // Validar forma de pagamento
        if (empty($forma_pagamento)) {
            $_SESSION['mensagem'] = "Forma de pagamento é obrigatória para lançamentos pagos.";
            echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
            return;
        }
    }
    
    // Atualizar o lançamento no banco de dados
    $stmt = $conn->prepare("UPDATE lancamentos_financeiros 
                           SET tipo = ?, categoria_id = ?, descricao = ?, valor = ?, 
                               data_lancamento = ?, data_vencimento = ?, data_pagamento = ?, 
                               status = ?, forma_pagamento = ?, observacao = ?, reserva_id = ? 
                           WHERE id = ? AND pousada_id = ?");
    
    $stmt->bind_param("sissssssssiis", 
                     $tipo, $categoria_id, $descricao, $valor, 
                     $data_lancamento, $data_vencimento, $data_pagamento, 
                     $status, $forma_pagamento, $observacao, $reserva_id,
                     $id, $pousada_id);
    
    if ($stmt->execute()) {
        // Se o lançamento mudou para pago, registrar no caixa
        if ($marcar_como_pago && $lancamento_atual['status'] == 'pendente') {
            registrarMovimentacaoCaixa($conn, $id, $tipo, $valor, $descricao, $user_id, $pousada_id, false, $forma_pagamento);
        }
        
        $_SESSION['mensagem'] = "Lançamento atualizado com sucesso.";
    } else {
        $_SESSION['mensagem'] = "Erro ao atualizar lançamento: " . $conn->error;
    }
    
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
}

// Função para registrar o pagamento de um lançamento
function registrarPagamento($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    error_log("Iniciando registro de pagamento: ID=" . $id . ", Data=" . $_POST['data_pagamento']);
    
    // Validar ID com mensagem mais clara
    if ($id <= 0) {
        error_log("ERRO: ID do lançamento inválido ou não fornecido: " . print_r($_POST, true));
        $_SESSION['mensagem'] = "Erro: ID do lançamento não foi fornecido corretamente. Por favor, tente novamente.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    $data_pagamento = $_POST['data_pagamento'] ?? '';
    $forma_pagamento = $_POST['forma_pagamento'] ?? '';
    $observacao_pagamento = $_POST['observacao_pagamento'] ?? '';
    
    // Validar data de pagamento
    if (!validarData($data_pagamento)) {
        $_SESSION['mensagem'] = "Data de pagamento inválida.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar forma de pagamento
    if (empty($forma_pagamento)) {
        $_SESSION['mensagem'] = "Forma de pagamento é obrigatória.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se o lançamento existe, pertence à pousada e está pendente
    $stmt = $conn->prepare("SELECT * FROM lancamentos_financeiros WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Lançamento não encontrado.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    $lancamento = $result->fetch_assoc();
    
    if ($lancamento['status'] != 'pendente') {
        $_SESSION['mensagem'] = "Este lançamento não está pendente.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Adicionar a observação de pagamento à observação existente, se fornecida
    $observacao = $lancamento['observacao'];
    if (!empty($observacao_pagamento)) {
        $observacao = ($observacao ? $observacao . "\n\n" : '') . "Observação do pagamento em " . date('d/m/Y', strtotime($data_pagamento)) . ":\n" . $observacao_pagamento;
    }
    
    // Atualizar o lançamento para pago
    $status = 'pago';
    $stmt = $conn->prepare("UPDATE lancamentos_financeiros 
                           SET status = ?, data_pagamento = ?, forma_pagamento = ?, observacao = ? 
                           WHERE id = ? AND pousada_id = ?");
    
    $stmt->bind_param("ssssis", $status, $data_pagamento, $forma_pagamento, $observacao, $id, $pousada_id);
    
    if ($stmt->execute()) {
        error_log("Pagamento registrado com sucesso para ID: " . $id);
        // Registrar no caixa apenas se for dinheiro
        registrarMovimentacaoCaixa($conn, $id, $lancamento['tipo'], $lancamento['valor'], $lancamento['descricao'], $user_id, $pousada_id, false, $forma_pagamento);
        
        $_SESSION['mensagem'] = "Pagamento registrado com sucesso.";
    } else {
        error_log("Erro ao registrar pagamento: " . $conn->error . " para ID: " . $id);
        $_SESSION['mensagem'] = "Erro ao registrar pagamento: " . $conn->error;
    }
    
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
}

// Função para cancelar um lançamento
function cancelarLancamento($conn, $user_id, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $motivo_cancelamento = $_POST['motivo_cancelamento'] ?? '';
    
    // Validar ID
    if ($id <= 0) {
        $_SESSION['mensagem'] = "ID do lançamento inválido.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Validar motivo de cancelamento
    if (empty($motivo_cancelamento)) {
        $_SESSION['mensagem'] = "Motivo do cancelamento é obrigatório.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Verificar se o lançamento existe e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM lancamentos_financeiros WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Lançamento não encontrado.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    $lancamento = $result->fetch_assoc();
    
    // Verificar se o lançamento já está cancelado
    if ($lancamento['status'] == 'cancelado') {
        $_SESSION['mensagem'] = "Este lançamento já está cancelado.";
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
    }
    
    // Adicionar o motivo de cancelamento à observação existente
    $observacao = $lancamento['observacao'];
    $observacao = ($observacao ? $observacao . "\n\n" : '') . "CANCELADO em " . date('d/m/Y') . ":\n" . $motivo_cancelamento;
    
    // Atualizar o lançamento para cancelado
    $status = 'cancelado';
    $stmt = $conn->prepare("UPDATE lancamentos_financeiros 
                           SET status = ?, observacao = ? 
                           WHERE id = ? AND pousada_id = ?");
    
    $stmt->bind_param("ssii", $status, $observacao, $id, $pousada_id);
    
    if ($stmt->execute()) {
        // Se o lançamento estava pago, registrar o estorno no caixa
        if ($lancamento['status'] == 'pago') {
            // CORREÇÃO: Para estorno, usar o mesmo tipo mas marcar como estorno
            // Se era receita paga, o estorno deve SAIR do caixa
            // Se era despesa paga, o estorno deve ENTRAR no caixa
            $descricao_estorno = "Estorno - " . $lancamento['descricao'];
            
            // Registrar estorno no caixa (o terceiro parâmetro 'true' indica estorno)
            registrarMovimentacaoCaixa(
                $conn, 
                $id, 
                $lancamento['tipo'], // Manter o tipo original
                $lancamento['valor'], 
                $descricao_estorno . " (Motivo: " . $motivo_cancelamento . ")", 
                $user_id, 
                $pousada_id,
                true, // Indica que é um estorno
                $lancamento['forma_pagamento'] // Passar a forma de pagamento
            );
        }
        
        $_SESSION['mensagem'] = "Lançamento cancelado com sucesso.";
    } else {
        $_SESSION['mensagem'] = "Erro ao cancelar lançamento: " . $conn->error;
    }
    
        echo "<script>window.location.href = 'index.php?page=contas&refresh=" . time() . "';</script>";
        return;
}

// Função para registrar movimentação no caixa
function registrarMovimentacaoCaixa($conn, $lancamento_id, $tipo, $valor, $descricao, $user_id, $pousada_id, $estorno = false, $forma_pagamento = null) {
    // Verificar se a forma de pagamento afeta o caixa usando o novo campo
    if ($forma_pagamento) {
        $stmt_forma = $conn->prepare("SELECT afeta_caixa FROM formas_pagamento 
                                     WHERE pousada_id = ? AND nome = ? AND is_active = 1");
        $stmt_forma->bind_param("is", $pousada_id, $forma_pagamento);
        $stmt_forma->execute();
        $result_forma = $stmt_forma->get_result();
        
        if ($result_forma->num_rows == 0) {
            return false; // Forma de pagamento não encontrada ou inativa
        }
        
        $forma_data = $result_forma->fetch_assoc();
        if (!$forma_data['afeta_caixa']) {
            return false; // Esta forma de pagamento não afeta o caixa
        }
    } else {
        return false; // Sem forma de pagamento definida
    }
    
    // Verificar se há um caixa aberto
    $stmt = $conn->prepare("SELECT id FROM caixa_diario 
                           WHERE pousada_id = ? AND status = 'aberto' 
                           ORDER BY data_abertura DESC LIMIT 1");
    $stmt->bind_param("i", $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Se não houver caixa aberto, não registrar movimentação
    if ($result->num_rows == 0) {
        return false;
    }
    
    $caixa = $result->fetch_assoc();
    $caixa_id = $caixa['id'];
    
    // Determinar o tipo de movimentação (entrada/saída)
    if ($estorno) {
        // CORREÇÃO: Para estorno, inverter a lógica
        // Se era receita (entrada), o estorno deve ser saída
        // Se era despesa (saída), o estorno deve ser entrada
        $tipo_movimentacao = ($tipo == 'receita') ? 'saida' : 'entrada';
    } else {
        // Operação normal
        $tipo_movimentacao = ($tipo == 'receita') ? 'entrada' : 'saida';
    }
    
    // Inserir a movimentação no caixa
    $data_hora = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("INSERT INTO movimentacoes_caixa 
                           (caixa_id, lancamento_id, tipo, valor, descricao, data_hora, usuario_id) 
                           VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param("iisdssi", 
                     $caixa_id, $lancamento_id, $tipo_movimentacao, $valor, 
                     $descricao, $data_hora, $user_id);
    
    return $stmt->execute();
}