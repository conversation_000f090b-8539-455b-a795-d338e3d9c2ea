<?php
// DETECÇÃO AUTOMÁTICA DE CONTEXTO - Evita divergências!
$is_ajax_context = isset($ajax_context) && $ajax_context === true;

// Definir valores padrão baseados no contexto
if ($is_ajax_context) {
    $form_action = $form_action ?? 'reservas_salvar.php'; // Chamada direta
    $form_id = $form_id ?? 'formReservaCompleto';
} else {
    $form_action = $form_action ?? '?page=reservas_salvar'; // Via index.php
    $form_id = $form_id ?? 'formReserva';
}

$form_method = $form_method ?? 'POST';
$hidden_fields = $hidden_fields ?? '<input type="hidden" name="acao" value="cadastrar">';

// Valores dos campos (vazios por padrão, preenchidos quando editando)
$hospede_id = $hospede_id ?? '';
$hospede_nome = $hospede_nome ?? '';
$uh = $uh ?? '';
$numacomp = $numacomp ?? '';
$valor = $valor ?? '';
$dataentrada = $dataentrada ?? '';
$horaentrada = $horaentrada ?? '13:00';
$datasaida = $datasaida ?? '';
$horasaida = $horasaida ?? '12:00';
$acompanhantes = $acompanhantes ?? '';
$vemde = $vemde ?? '';
$vaipara = $vaipara ?? '';
$motivo = $motivo ?? '';
$transporte = $transporte ?? '';
$observacao = $observacao ?? '';

// Configurações de exibição
$readonly_uh = $readonly_uh ?? false;
$titulo_reserva = $titulo_reserva ?? 'Nova Reserva para:'; // Adicionar esta linha
?>

<div class="form-container">
	<center><br><h3>
    <b>
        <?php if ($hospede_nome): ?>
            <?= htmlspecialchars($hospede_nome) ?>
        <?php endif; ?>
    </b>
	</h3></center>

	<form id="<?= $form_id ?>" action="<?= $form_action ?>" method="<?= $form_method ?>">
		<?= $hidden_fields ?>
		
		<div class="form-group-row">
			<div class="form-group">
				<label for="uh">Un. Habit.</label>
				<input type="text" name="uh" maxlength="3" size="3" value="<?= htmlspecialchars($uh) ?>" <?= $readonly_uh ? 'readonly' : '' ?>>
			</div>
			<div class="form-group">
				<label for="numacomp">Nº Acomp.</label>
				<input type="number" name="numacomp" min="0" max="30" step="1" value="<?= htmlspecialchars($numacomp) ?>">
			</div>
			<div class="form-group">
				<label for="valor">Valor</label>
				<input type="text" name="valor" value="<?= htmlspecialchars($valor) ?>">
			</div>
		</div>
		
		<div class="form-group-row">
			<div class="form-group">
				<label for="dataentrada">Entrada: Data</label>
				<input type="date" name="dataentrada" value="<?= htmlspecialchars($dataentrada) ?>">
			</div>
			<div class="form-group">
				<label for="horaentrada">Hora</label>
				<input type="time" name="horaentrada" value="<?= htmlspecialchars($horaentrada) ?>">
			</div>
		</div>
		
		<div class="form-group-row">
			<div class="form-group">
				<label for="datasaida">Saída: Data</label>
				<input type="date" name="datasaida" value="<?= htmlspecialchars($datasaida) ?>">
			</div>
			<div class="form-group">
				<label for="horasaida">Hora</label>
				<input type="time" name="horasaida" value="<?= htmlspecialchars($horasaida) ?>">
			</div>
		</div>
		
		<div class="form-group">
			<label for="acompanhantes">Acompanhantes:</label>
			<textarea class="form-control" name="acompanhantes" rows="4"
				placeholder="Nome dos acompanhantes e parentesco..."><?= htmlspecialchars($acompanhantes) ?></textarea>
		</div>
		
		<div class="form-group">
			<label for="vemde">Último destino:</label>
			<input type="text" name="vemde" value="<?= htmlspecialchars($vemde) ?>">
		</div>
		
		<div class="form-group">
			<label for="vaipara">Próximo destino:</label>
			<input type="text" name="vaipara" value="<?= htmlspecialchars($vaipara) ?>">
		</div>
		
		<div class="form-group-row">
			<div class="form-group">
				<label for="motivo">Motivo da Viagem:</label>
				<select name="motivo">
					<option value="">Selecione</option>
					<option value="Ferias" <?= ($motivo == 'Ferias') ? 'selected' : '' ?>>Férias</option>
					<option value="Negocios" <?= ($motivo == 'Negocios') ? 'selected' : '' ?>>Negócios</option>
					<option value="Congresso" <?= ($motivo == 'Congresso') ? 'selected' : '' ?>>Congresso</option>
					<option value="Estudos" <?= ($motivo == 'Estudos') ? 'selected' : '' ?>>Estudos</option>
					<option value="Saude" <?= ($motivo == 'Saude') ? 'selected' : '' ?>>Saúde</option>
					<option value="Outro" <?= ($motivo == 'Outro') ? 'selected' : '' ?>>Outro</option>
				</select>
			</div>
			<div class="form-group">
				<label for="transporte">Meio de Transporte:</label>
				<select name="transporte">
					<option value="">Selecione</option>
					<option value="Automovel" <?= ($transporte == 'Automovel') ? 'selected' : '' ?>>Automóvel</option>
					<option value="Aviao" <?= ($transporte == 'Aviao') ? 'selected' : '' ?>>Avião</option>
					<option value="Navio" <?= ($transporte == 'Navio') ? 'selected' : '' ?>>Navio</option>
					<option value="Onibus" <?= ($transporte == 'Onibus') ? 'selected' : '' ?>>Ônibus</option>
					<option value="Trem" <?= ($transporte == 'Trem') ? 'selected' : '' ?>>Trem</option>
					<option value="Outro" <?= ($transporte == 'Outro') ? 'selected' : '' ?>>Outro</option>
				</select>
			</div>
		</div>

		<div class="form-group">
			<label for="observacao">Observações:</label>
			<textarea class="form-control" name="observacao" rows="4" placeholder="Insira aqui as observações"><?= htmlspecialchars($observacao) ?></textarea>
		</div>



		<div class="form-group">
			<center>
			<button type="submit" class="btn btn-primary"><?= isset($button_text) ? $button_text : 'Registrar' ?></button>
			</center>
		</div>
	</form>
</div>