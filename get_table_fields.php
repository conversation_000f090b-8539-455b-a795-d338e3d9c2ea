<?php
session_start();
require_once 'config.php';

// Verificar se o usuário é super administrador
if (!isset($_SESSION['user_pousada_id']) || $_SESSION['user_pousada_id'] != 0) {
    http_response_code(403);
    echo json_encode(['error' => 'Acesso negado']);
    exit;
}

// Verificar se foi enviado o nome da tabela
if (!isset($_POST['table']) || empty($_POST['table'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Nome da tabela não fornecido']);
    exit;
}

$table_name = $_POST['table'];

// Validar nome da tabela (apenas caracteres alfanuméricos e underscore)
if (!preg_match('/^[a-zA-Z0-9_]+$/', $table_name)) {
    http_response_code(400);
    echo json_encode(['error' => 'Nome de tabela inválido']);
    exit;
}

try {
    // Obter campos da tabela
    $fields_query = $conn->query("DESCRIBE `$table_name`");
    
    if (!$fields_query) {
        throw new Exception($conn->error);
    }
    
    $fields = [];
    while ($field = $fields_query->fetch_assoc()) {
        $fields[] = [
            'Field' => $field['Field'],
            'Type' => $field['Type'],
            'Null' => $field['Null'],
            'Key' => $field['Key'],
            'Default' => $field['Default'],
            'Extra' => $field['Extra']
        ];
    }
    
    // Retornar os campos em formato JSON
    header('Content-Type: application/json');
    echo json_encode($fields);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao obter campos da tabela: ' . $e->getMessage()]);
}
?>