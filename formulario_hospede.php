<?php
// Definir valores padrão se não estiverem setados
$form_action = $form_action ?? '?page=salvar';
$form_method = $form_method ?? 'POST';
$hidden_fields = $hidden_fields ?? '<input type="hidden" name="acao" value="cadastrar">';
$form_id = $form_id ?? 'registroHospedes';

// Valores dos campos (vazios por padrão, preenchidos quando editando ou com dados preservados)
$nome = $nome ?? ($form_data['nome'] ?? '');
$nasc = $nasc ?? ($form_data['nasc'] ?? '');
$idade = $idade ?? ($form_data['idade'] ?? '');
$profissao = $profissao ?? ($form_data['profissao'] ?? '');
$nacionalidade = $nacionalidade ?? ($form_data['nacionalidade'] ?? '');
$sexo = $sexo ?? ($form_data['sexo'] ?? '');
$cpf = $cpf ?? ($form_data['cpf'] ?? '');
$documento = $documento ?? ($form_data['documento'] ?? '');
$tipo = $tipo ?? ($form_data['tipo'] ?? '');
$expedidor = $expedidor ?? ($form_data['expedidor'] ?? '');
$endereco = $endereco ?? ($form_data['endereco'] ?? '');
$telefone = $telefone ?? ($form_data['telefone'] ?? '');
$cep = $cep ?? ($form_data['cep'] ?? '');
$cidade = $cidade ?? ($form_data['cidade'] ?? '');
$uf = $uf ?? ($form_data['uf'] ?? '');
$pais = $pais ?? ($form_data['pais'] ?? '');
$email = $email ?? ($form_data['email'] ?? '');

$readonly_idade = $readonly_idade ?? 'readonly';
$titulo_hospede = $titulo_hospede?? 'Editar Hóspede'; 

?>

<div class="form-container">
	<center><br><h3>
    <?php echo htmlspecialchars($titulo_hospede); ?>
    <br>
    <b>
        <?php if ($nome): ?>
            <?= htmlspecialchars($nome) ?>
        <?php endif; ?>
    </b>
	</h3></center>

	
	<form id="<?= $form_id ?>" action="<?= $form_action ?>" method="<?= $form_method ?>">
		<?= $hidden_fields ?>
	
		<div class="form-group">
			<label for="nome">Nome:</label>
			<input type="text" name="nome" value="<?= htmlspecialchars($nome) ?>" required>
		</div>
		
		<div class="form-group-row">
			<div class="form-group">
				<label for="nasc">Data de Nasc:</label>
				<input type="date" name="nasc" value="<?= htmlspecialchars($nasc) ?>">
			</div>    
			<div class="form-group">
				<label for="idade">Idade:</label>
				<input type="text" name="idade" value="<?= htmlspecialchars($idade) ?>" <?= $readonly_idade ?>>
			</div>
		</div>   
				
		<div class="form-group-row">
			<div class="form-group">
				<label for="profissao">Profissão:</label>
				<input type="text" name="profissao" value="<?= htmlspecialchars($profissao) ?>">
			</div>            
			<div class="form-group">
				<label for="nacionalidade">Nacionalidade:</label>
				<input type="text" name="nacionalidade" value="<?= htmlspecialchars($nacionalidade) ?>">
			</div>   
		</div> 	
		<div class="form-group-row">
			<div class="form-group">
				<label for="sexo">Sexo:</label>
				<input type="text" name="sexo" value="<?= htmlspecialchars($sexo) ?>">
			</div>                
			<div class="form-group">
				<label for="cpf">CPF:</label>
				<input type="text" name="cpf" value="<?= htmlspecialchars($cpf) ?>">
				<div id="cpf-feedback" class="invalid-feedback">
                    <b>CPF inválido</b>.
                </div>
			</div>    
		</div>
		<div class="form-group-row">
			<div class="form-group">
				<label for="documento">Documento:</label>
				<input type="text" name="documento" value="<?= htmlspecialchars($documento) ?>">
			</div>    
			<div class="form-group">
				<label for="tipo">Tipo:</label>
				<input type="text" name="tipo" value="<?= htmlspecialchars($tipo) ?>">
			</div>
			<div class="form-group">
				<label for="expedidor">Expedidor:</label>
				<input type="text" name="expedidor" value="<?= htmlspecialchars($expedidor) ?>">
			</div>
		</div>
		
		<div class="form-group">
			<label for="endereco">Endereço:</label>
			<input type="text" name="endereco" value="<?= htmlspecialchars($endereco) ?>">
		</div>
		<div class="form-group-row">
			<div class="form-group">
				<label for="telefone">Telefone:</label>
				<input type="tel" name="telefone" value="<?= htmlspecialchars($telefone) ?>">
			</div>    
			<div class="form-group">
				<label for="cep">CEP:</label>
				<input type="text" name="cep" value="<?= htmlspecialchars($cep) ?>">
			</div>
		</div>    
		<div class="form-group-row">
			<div class="form-group cidade">
				<label for="cidade">Cidade:</label>
				<input type="text" name="cidade" value="<?= htmlspecialchars($cidade) ?>">
			</div>
			<div class="form-group uf">
				<label for="uf">UF:</label>
				<input type="text" name="uf" value="<?= htmlspecialchars($uf) ?>">
			</div>
			<div class="form-group pais">
				<label for="pais">País:</label>
				<input type="text" name="pais" value="<?= htmlspecialchars($pais) ?>">
			</div>
		</div>
		<div class="form-group">
			<label for="email">Email:</label>
			<input type="text" name="email" value="<?= htmlspecialchars($email) ?>">
		</div>
		<div class="form-group">
			<center>
			<button type="submit" class="btn btn-primary">Registrar</button>
			</center>
		</div>
	</form>
</div>