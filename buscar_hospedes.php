<?php
// Reportar todos os erros do PHP
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

// Check if a session is not already active before starting
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Inclui os arquivos de configuração e funções
if (!function_exists('prepareAndExecute') && !isset($conn)) {
    include_once("config.php");
    include_once("func.php");
}

// Verify session
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Sessão inválida']);
    exit;
}

// Obtém o ID da pousada do usuário logado
$pousada_id = $_SESSION['user_pousada_id'];

// Verificar se o termo de busca foi fornecido
$termo = isset($_GET['termo']) ? trim($_GET['termo']) : '';

// Remover limitação de caracteres - permitir busca vazia (retorna todos)
// if (empty($termo) || strlen($termo) < 2) {
//     header('Content-Type: application/json');
//     echo json_encode([]);
//     exit;
// }

// Preparar a consulta SQL para buscar hóspedes (nome ou CPF)
if (empty($termo)) {
    // Se termo vazio, retornar todos os hóspedes
    $sql = "SELECT id, nome, cpf as documento, cidade, uf
            FROM hospedes
            WHERE pousada_id = ?
            ORDER BY nome";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $pousada_id);
} else {
    // Verificar se o termo parece ser um CPF (só números ou formatado)
    $cpf_limpo = preg_replace('/[^0-9]/', '', $termo);
    $eh_cpf = (strlen($cpf_limpo) >= 3 && ctype_digit($cpf_limpo));

    if ($eh_cpf) {
        // Buscar por CPF
        $sql = "SELECT id, nome, cpf as documento, cidade, uf
                FROM hospedes
                WHERE pousada_id = ? AND cpf LIKE ?
                ORDER BY nome";

        $stmt = $conn->prepare($sql);
        $cpf_busca = "%{$cpf_limpo}%";
        $stmt->bind_param('is', $pousada_id, $cpf_busca);
    } else {
        // Buscar por nome
        $sql = "SELECT id, nome, cpf as documento, cidade, uf
                FROM hospedes
                WHERE pousada_id = ? AND nome LIKE ?
                ORDER BY nome";

        $stmt = $conn->prepare($sql);
        $termo_busca = "%{$termo}%";
        $stmt->bind_param('is', $pousada_id, $termo_busca);
    }
}
$stmt->execute();
$result = $stmt->get_result();

$hospedes = [];
while ($row = $result->fetch_assoc()) {
    $hospedes[] = $row;
}

// Retornar os resultados em formato JSON
header('Content-Type: application/json');
echo json_encode($hospedes);
?>