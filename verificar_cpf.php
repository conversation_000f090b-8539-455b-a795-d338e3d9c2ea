<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autorizado']);
    exit;
}

$cpf = $_GET['cpf'] ?? '';
$hospede_id_excluir = $_GET['hospede_id'] ?? 0; // Para edição
$pousada_id = $_SESSION['user_pousada_id'];

if (empty($cpf)) {
    echo json_encode(['error' => 'CPF não informado']);
    exit;
}

// Remove formatação do CPF
$cpf_limpo = preg_replace('/[^0-9]/', '', $cpf);

// Verifica se CPF já existe na pousada
$sql = "SELECT id, nome FROM hospedes WHERE cpf = ? AND pousada_id = ?";

// Se estiver editando, excluir o próprio registro da verificação
if ($hospede_id_excluir > 0) {
    $sql .= " AND id != ?";
}

$stmt = $conn->prepare($sql);

if ($hospede_id_excluir > 0) {
    $stmt->bind_param("sii", $cpf_limpo, $pousada_id, $hospede_id_excluir);
} else {
    $stmt->bind_param("si", $cpf_limpo, $pousada_id);
}

$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $hospede = $result->fetch_assoc();
    echo json_encode([
        'existe' => true,
        'nome_hospede' => $hospede['nome'],
        'hospede_id' => $hospede['id']
    ]);
} else {
    echo json_encode(['existe' => false]);
}

$stmt->close();
$conn->close();
?>