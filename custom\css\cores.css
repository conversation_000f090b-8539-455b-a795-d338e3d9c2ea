/* Cores Personalizadas - Gerado automaticamente */
/* Cor primária: #28a745 */
/* Gerado em: 2025-01-15 12:00:00 */

/* Background do body */
body {
    background-color: rgba(40, 167, 69, 0.1) !important;
    font-family: Arial, sans-serif;
}

/* Navbar com gradiente */
.navbar.navbar-expand-lg.navbar-light {
    background: linear-gradient(135deg, #28a745 0%, #d4edda 100%) !important;
    border-bottom: none !important;
}

/* Links da navbar */
.navbar .nav-link {
    color: white !important;
}

.navbar .nav-link:hover {
    color: #155724 !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px;
}

/* Botões */
.form-group button,
.btn-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
}

.form-group button:hover,
.btn-success:hover {
    background-color: #155724 !important;
    border-color: #155724 !important;
}

/* Células livres */
.livre {
    background-color: #d4edda !important;
}

/* Alertas */
.alert-success {
    background-color: #d4edda !important;
    border-color: #28a745 !important;
    color: #155724 !important;
}