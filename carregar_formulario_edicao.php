
<?php
header('Content-Type: application/json');

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    echo json_encode(['success' => false, 'message' => 'Sessão inválida']);
    exit;
}

include_once("config.php");

$reserva_id = $_POST['reserva_id'] ?? '';
$hospede_nome = $_POST['hospede_nome'] ?? '';

if (empty($reserva_id)) {
    echo json_encode(['success' => false, 'message' => 'ID da reserva não fornecido']);
    exit;
}

// Buscar dados da reserva
$sql = "SELECT * FROM reservas WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $reserva_id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_object();

if (!$row) {
    echo json_encode(['success' => false, 'message' => 'Reserva não encontrada']);
    exit;
}

// Configurar variáveis para o formulário
$ajax_context = true;
$form_action = 'reservas_salvar.php';
$form_id = 'formEditarReserva';
$hidden_fields = '<input type="hidden" name="acao" value="editar">
                  <input type="hidden" name="reserva_id" value="' . htmlspecialchars($row->id) . '">
                  <input type="hidden" name="hospede_id" value="' . htmlspecialchars($row->hospede_id) . '">
                  <input type="hidden" name="hospede_nome" value="' . htmlspecialchars($hospede_nome) . '">';

// Preencher valores dos campos
$uh = $row->uh;
$numacomp = $row->numacomp;
$valor = $row->valor;
$dataentrada = $row->dataentrada;
$horaentrada = $row->horaentrada;
$datasaida = $row->datasaida;
$horasaida = $row->horasaida;
$acompanhantes = $row->acompanhantes;
$vemde = $row->vemde;
$vaipara = $row->vaipara;
$motivo = $row->motivo;
$transporte = $row->transporte;
$observacao = $row->observacao;
$titulo_reserva = 'Editar Reserva de:';
$button_text = 'Salvar Alterações';

// Capturar o HTML do formulário com funcionalidades de impressão e QR Code
ob_start();
?>
<!-- Botões de impressão no topo -->
<div class="form-group no-print mb-3">
    <button type="button" class="btn btn-secondary" id="print-button-top" onclick="imprimirModal()">Imprimir</button>
</div>

<!-- Logo para impressão -->
<div class="text-center mb-3 print-only">
    <img src="<?php echo $_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png'; ?>" alt="Logo" style="max-width: 200px; max-height: 100px;">
</div>

<?php
include 'formulario_reserva.php';
?>

<!-- QR Code e botão de impressão no final -->
<center>
<br>
<div id="qrcode"></div>
<br>
<div class="form-group no-print">
    <button type="button" class="btn btn-secondary" id="print-button-bottom" onclick="imprimirModal()">Imprimir</button>
</div>
</center>

<!-- Scripts necessários para QR Code e impressão -->
<script>
// Carregar biblioteca QRCode dinamicamente se não estiver carregada
if (typeof QRCode === 'undefined') {
    const script = document.createElement('script');
    script.src = 'instascan/qrcode.js';
    script.onload = function() {
        console.log('Biblioteca QRCode carregada com sucesso');
    };
    document.head.appendChild(script);
}
</script>

<script>
// Definir função global de impressão
window.imprimirModal = function() {
    console.log("Função imprimirModal chamada");
    try {
        window.print();
        console.log("window.print() executado com sucesso");
    } catch (error) {
        console.error("Erro ao executar window.print():", error);
    }
};

// Função para criar QR Code
function tryCreateQrCode() {
    if (typeof QRCode !== 'undefined') {
        createQrCode("<?php echo $row->id; ?>");
    } else {
        // Tentar novamente após 200ms se a biblioteca ainda não estiver carregada
        setTimeout(tryCreateQrCode, 200);
    }
}

// Executar criação do QR Code após um delay
setTimeout(tryCreateQrCode, 200);

function createQrCode(id) {
    const qrcodeElement = document.getElementById("qrcode");
    if (qrcodeElement && typeof QRCode !== 'undefined') {
        // Limpar conteúdo anterior se existir
        qrcodeElement.innerHTML = '';

        var qrcode = new QRCode("qrcode", {
            text: id,
            width: 80,
            height: 80,
            colorDark: "black",
            colorLight: "white",
            correctLevel: QRCode.CorrectLevel.H
        });
        console.log("QR Code criado com sucesso para ID:", id);
    } else {
        console.error('Elemento qrcode não encontrado ou biblioteca QRCode não carregada');
    }
}
</script>

<?php
$html = ob_get_clean();

echo json_encode([
    'success' => true,
    'html' => $html
]);
?>