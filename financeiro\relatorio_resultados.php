<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return; 
}

// Definir período padrão (mês atual)
$data_inicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : date('Y-m-01');
$data_fim = isset($_GET['data_fim']) ? $_GET['data_fim'] : date('Y-m-t');
$campo_data = isset($_GET['campo_data']) ? $_GET['campo_data'] : 'vencimento';
$agrupar_por = isset($_GET['agrupar_por']) ? $_GET['agrupar_por'] : 'mes';

// Definir o formato de agrupamento
$format_sql = '';
$format_label = '';

switch ($agrupar_por) {
    case 'dia':
        $format_sql = "DATE(data_$campo_data)";
        $format_label = 'd/m/Y';
        break;
    case 'semana':
        $format_sql = "YEARWEEK(data_$campo_data, 1)";
        $format_label = '\S\e\m\a\n\a W \d\e Y';
        break;
    case 'mes':
        $format_sql = "DATE_FORMAT(data_$campo_data, '%Y-%m-01')";
        $format_label = 'F/Y';
        break;
    case 'trimestre':
        $format_sql = "CONCAT(YEAR(data_$campo_data), '-', CEILING(MONTH(data_$campo_data)/3))";
        $format_label = 'Q\º Trimestre/Y';
        break;
    case 'ano':
        $format_sql = "YEAR(data_$campo_data)";
        $format_label = 'Y';
        break;
    default:
        $format_sql = "DATE_FORMAT(data_$campo_data, '%Y-%m-01')";
        $format_label = 'F/Y';
}

// Obter dados de resultados por período
$sql = "SELECT 
        $format_sql as periodo,
        SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) as receitas,
        SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as despesas,
        SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) - 
        SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as resultado
        FROM lancamentos_financeiros
        WHERE pousada_id = $pousada_id 
        AND data_$campo_data BETWEEN '$data_inicio' AND '$data_fim'
        GROUP BY periodo
        ORDER BY periodo ASC";

$result = $conn->query($sql);

// Calcular totais
$total_receitas = 0;
$total_despesas = 0;
$total_resultado = 0;

// Preparar dados para o gráfico
$periodos = [];
$receitas = [];
$despesas = [];
$resultados = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Formatar o período conforme o agrupamento
        if ($agrupar_por == 'semana') {
            // Para semanas, precisamos extrair o ano e a semana
            $ano = substr($row['periodo'], 0, 4);
            $semana = substr($row['periodo'], 4);
            $data_semana = new DateTime();
            $data_semana->setISODate($ano, $semana);
            $periodo_formatado = $data_semana->format($format_label);
        } elseif ($agrupar_por == 'mes') {
            $periodo_formatado = date($format_label, strtotime($row['periodo']));
            // Traduzir mês para português
            $meses_en = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            $meses_pt = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
            $periodo_formatado = str_replace($meses_en, $meses_pt, $periodo_formatado);
        } elseif ($agrupar_por == 'trimestre') {
            // Para trimestres, precisamos extrair o ano e o trimestre
            $partes = explode('-', $row['periodo']);
            $ano = $partes[0];
            $trimestre = $partes[1];
            $periodo_formatado = str_replace('Q', $trimestre, str_replace('Y', $ano, $format_label));
        } else {
            $periodo_formatado = date($format_label, strtotime($row['periodo']));
        }
        
        $periodos[] = $periodo_formatado;
        $receitas[] = floatval($row['receitas']);
        $despesas[] = floatval($row['despesas']);
        $resultados[] = floatval($row['resultado']);
        
        $total_receitas += floatval($row['receitas']);
        $total_despesas += floatval($row['despesas']);
        $total_resultado += floatval($row['resultado']);
    }
}

// Calcular margem de lucro
$margem_lucro = $total_receitas > 0 ? ($total_resultado / $total_receitas) * 100 : 0;

// Converter arrays para formato JSON para uso nos gráficos
$periodos_json = json_encode($periodos);
$receitas_json = json_encode($receitas);
$despesas_json = json_encode($despesas);
$resultados_json = json_encode($resultados);

// Obter dados por categoria para análise
$sql_categorias = "SELECT 
                  cf.nome, cf.tipo, cf.cor,
                  SUM(lf.valor) as total
                  FROM lancamentos_financeiros lf
                  JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
                  WHERE lf.pousada_id = $pousada_id 
                  AND lf.status = 'pago'
                  AND lf.data_$campo_data BETWEEN '$data_inicio' AND '$data_fim'
                  GROUP BY cf.id
                  ORDER BY cf.tipo, total DESC";
$result_categorias = $conn->query($sql_categorias);

// Preparar dados para o gráfico de categorias
$categorias_receitas = [];
$valores_receitas = [];
$cores_receitas = [];

$categorias_despesas = [];
$valores_despesas = [];
$cores_despesas = [];

if ($result_categorias && $result_categorias->num_rows > 0) {
    while ($row = $result_categorias->fetch_assoc()) {
        if ($row['tipo'] == 'receita') {
            $categorias_receitas[] = $row['nome'];
            $valores_receitas[] = floatval($row['total']);
            $cores_receitas[] = $row['cor'];
        } else {
            $categorias_despesas[] = $row['nome'];
            $valores_despesas[] = floatval($row['total']);
            $cores_despesas[] = $row['cor'];
        }
    }
}

// Converter arrays para formato JSON para uso nos gráficos
$categorias_receitas_json = json_encode($categorias_receitas);
$valores_receitas_json = json_encode($valores_receitas);
$cores_receitas_json = json_encode($cores_receitas);

$categorias_despesas_json = json_encode($categorias_despesas);
$valores_despesas_json = json_encode($valores_despesas);
$cores_despesas_json = json_encode($cores_despesas);
?>

<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2 class="mb-3">Demonstrativo de Resultados</h2>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-lightbulb-fill me-2" style="font-size: 1.2em;"></i>
                <div>
                    <strong>Aqui você:</strong>Analisa a lucratividade do negócio, com foco na margem de lucro e no resultado financeiro.
                </div>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-primary" onclick="window.print()">
                <i class="bi bi-printer"></i> Imprimir
            </button>
        </div>
    </div>
    
    <!-- Filtros -->
    <div class="card mb-4 no-print">
        <div class="card-header">
            <i class="bi bi-funnel"></i> Filtros
        </div>
        <div class="card-body">
            <form method="GET" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="relatorio_resultados">
                
                <div class="col-md-3">
                    <label for="data_inicio" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?= $data_inicio ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="data_fim" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?= $data_fim ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="agrupar_por" class="form-label">Agrupar por</label>
                    <select class="form-select" id="agrupar_por" name="agrupar_por">
                        <option value="dia" <?= $agrupar_por == 'dia' ? 'selected' : '' ?>>Dia</option>
                        <option value="semana" <?= $agrupar_por == 'semana' ? 'selected' : '' ?>>Semana</option>
                        <option value="mes" <?= $agrupar_por == 'mes' ? 'selected' : '' ?>>Mês</option>
                        <option value="trimestre" <?= $agrupar_por == 'trimestre' ? 'selected' : '' ?>>Trimestre</option>
                        <option value="ano" <?= $agrupar_por == 'ano' ? 'selected' : '' ?>>Ano</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="campo_data" class="form-label">Campo de Data</label>
                    <select class="form-select" id="campo_data" name="campo_data">
                        <option value="vencimento" <?= $campo_data == 'vencimento' ? 'selected' : '' ?>>Vencimento</option>
                        <option value="pagamento" <?= $campo_data == 'pagamento' ? 'selected' : '' ?>>Pagamento</option>
                        <option value="lancamento" <?= $campo_data == 'lancamento' ? 'selected' : '' ?>>Lançamento</option>
                    </select>
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> Filtrar
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Resumo -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-info-circle"></i> Resumo do Período: <?= date('d/m/Y', strtotime($data_inicio)) ?> a <?= date('d/m/Y', strtotime($data_fim)) ?>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 text-center">
                    <h5 class="text-success">Receitas</h5>
                    <h3>R$ <?= number_format($total_receitas, 2, ',', '.') ?></h3>
                </div>
                <div class="col-md-3 text-center">
                    <h5 class="text-danger">Despesas</h5>
                    <h3>R$ <?= number_format($total_despesas, 2, ',', '.') ?></h3>
                </div>
                <div class="col-md-3 text-center">
                    <h5 class="<?= $total_resultado >= 0 ? 'text-primary' : 'text-warning' ?>">Resultado</h5>
                    <h3>R$ <?= number_format($total_resultado, 2, ',', '.') ?></h3>
                </div>
                <div class="col-md-3 text-center">
                    <h5 class="<?= $margem_lucro >= 0 ? 'text-primary' : 'text-warning' ?>">Margem de Lucro</h5>
                    <h3><?= number_format($margem_lucro, 2, ',', '.') ?>%</h3>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Gráfico de Resultados -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-graph-up"></i> Evolução de Resultados
        </div>
        <div class="card-body">
            <div style="height: 400px;">
                <canvas id="resultadosChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Gráficos de Categorias -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <i class="bi bi-pie-chart"></i> Receitas por Categoria
                </div>
                <div class="card-body">
                    <?php if (count($categorias_receitas) > 0): ?>
                        <div style="height: 300px;">
                            <canvas id="receitasChart"></canvas>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há dados de receitas para exibir no período selecionado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <i class="bi bi-pie-chart"></i> Despesas por Categoria
                </div>
                <div class="card-body">
                    <?php if (count($categorias_despesas) > 0): ?>
                        <div style="height: 300px;">
                            <canvas id="despesasChart"></canvas>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            Não há dados de despesas para exibir no período selecionado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Tabela de Dados -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table"></i> Demonstrativo de Resultados por Período
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Período</th>
                            <th class="text-end">Receitas</th>
                            <th class="text-end">Despesas</th>
                            <th class="text-end">Resultado</th>
                            <th class="text-end">Margem (%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        if ($result && $result->num_rows > 0):
                            $result->data_seek(0);
                            while ($row = $result->fetch_assoc()):
                                // Formatar o período conforme o agrupamento
                                if ($agrupar_por == 'semana') {
                                    $ano = substr($row['periodo'], 0, 4);
                                    $semana = substr($row['periodo'], 4);
                                    $data_semana = new DateTime();
                                    $data_semana->setISODate($ano, $semana);
                                    $periodo_formatado = $data_semana->format($format_label);
                                } elseif ($agrupar_por == 'mes') {
                                    $periodo_formatado = date($format_label, strtotime($row['periodo']));
                                    // Traduzir mês para português
                                    $meses_en = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                                    $meses_pt = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
                                    $periodo_formatado = str_replace($meses_en, $meses_pt, $periodo_formatado);
                                } elseif ($agrupar_por == 'trimestre') {
                                    $partes = explode('-', $row['periodo']);
                                    $ano = $partes[0];
                                    $trimestre = $partes[1];
                                    $periodo_formatado = str_replace('Q', $trimestre, str_replace('Y', $ano, $format_label));
                                } else {
                                    $periodo_formatado = date($format_label, strtotime($row['periodo']));
                                }
                                
                                $receitas = floatval($row['receitas']);
                                $despesas = floatval($row['despesas']);
                                $resultado = floatval($row['resultado']);
                                $margem = $receitas > 0 ? ($resultado / $receitas) * 100 : 0;
                        ?>
                            <tr>
                                <td><?= $periodo_formatado ?></td>
                                <td class="text-end text-success">R$ <?= number_format($receitas, 2, ',', '.') ?></td>
                                <td class="text-end text-danger">R$ <?= number_format($despesas, 2, ',', '.') ?></td>
                                <td class="text-end <?= $resultado >= 0 ? 'text-primary' : 'text-warning' ?>">
                                    R$ <?= number_format($resultado, 2, ',', '.') ?>
                                </td>
                                <td class="text-end <?= $margem >= 0 ? 'text-primary' : 'text-warning' ?>">
                                    <?= number_format($margem, 2, ',', '.') ?>%
                                </td>
                            </tr>
                        <?php 
                            endwhile;
                        else:
                        ?>
                            <tr>
                                <td colspan="5" class="text-center">Nenhum dado encontrado para o período selecionado.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-dark">
                            <th>Total</th>
                            <th class="text-end text-success">R$ <?= number_format($total_receitas, 2, ',', '.') ?></th>
                            <th class="text-end text-danger">R$ <?= number_format($total_despesas, 2, ',', '.') ?></th>
                            <th class="text-end <?= $total_resultado >= 0 ? 'text-primary' : 'text-warning' ?>">
                                R$ <?= number_format($total_resultado, 2, ',', '.') ?>
                            </th>
                            <th class="text-end <?= $margem_lucro >= 0 ? 'text-primary' : 'text-warning' ?>">
                                <?= number_format($margem_lucro, 2, ',', '.') ?>%
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Scripts para os gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de Resultados
    const ctxResultados = document.getElementById('resultadosChart');
    if (ctxResultados) {
        new Chart(ctxResultados, {
            type: 'bar',
            data: {
                labels: <?= $periodos_json ?>,
                datasets: [
                    {
                        label: 'Receitas',
                        data: <?= $receitas_json ?>,
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1,
                        order: 2
                    },
                    {
                        label: 'Despesas',
                        data: <?= $despesas_json ?>,
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1,
                        order: 2
                    },
                    {
                        label: 'Resultado',
                        data: <?= $resultados_json ?>,
                        type: 'line',
                        backgroundColor: 'rgba(0, 123, 255, 0.2)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        order: 1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Valores (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    },
                    y1: {
                        position: 'right',
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Resultado (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': R$ ' + context.raw.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Gráfico de Receitas por Categoria
    const ctxReceitas = document.getElementById('receitasChart');
    if (ctxReceitas && <?= count($categorias_receitas) > 0 ? 'true' : 'false' ?>) {
        new Chart(ctxReceitas, {
            type: 'doughnut',
            data: {
                labels: <?= $categorias_receitas_json ?>,
                datasets: [{
                    data: <?= $valores_receitas_json ?>,
                    backgroundColor: <?= $cores_receitas_json ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Gráfico de Despesas por Categoria
    const ctxDespesas = document.getElementById('despesasChart');
    if (ctxDespesas && <?= count($categorias_despesas) > 0 ? 'true' : 'false' ?>) {
        new Chart(ctxDespesas, {
            type: 'doughnut',
            data: {
                labels: <?= $categorias_despesas_json ?>,
                datasets: [{
                    data: <?= $valores_despesas_json ?>,
                    backgroundColor: <?= $cores_despesas_json ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2}) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>