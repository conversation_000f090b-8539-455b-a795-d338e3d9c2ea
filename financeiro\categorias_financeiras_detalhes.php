<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se o ID foi fornecido
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id <= 0) {
    echo '<div class="alert alert-danger">ID da categoria inválido.</div>';
    exit();
}

// Buscar dados da categoria
$stmt = $conn->prepare("SELECT * FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
$stmt->bind_param("ii", $id, $pousada_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo '<div class="alert alert-danger">Categoria não encontrada.</div>';
    exit();
}

$categoria = $result->fetch_assoc();
?>

<input type="hidden" name="acao" value="editar">
<input type="hidden" name="id" value="<?php echo $categoria['id']; ?>">
<input type="hidden" name="tipo" value="<?php echo $categoria['tipo']; ?>">

<div class="mb-3">
    <label for="edit_nome" class="form-label">Nome*</label>
    <input type="text" class="form-control" id="edit_nome" name="nome" 
           value="<?php echo htmlspecialchars($categoria['nome']); ?>" required
           <?php echo $categoria['is_default'] ? 'readonly' : ''; ?>>
    <?php if ($categoria['is_default']): ?>
        <small class="text-muted">Esta é uma categoria padrão e seu nome não pode ser alterado.</small>
    <?php endif; ?>
</div>

<div class="mb-3">
    <label for="edit_cor" class="form-label">Cor</label>
    <input type="color" class="form-control form-control-color" id="edit_cor" name="cor" 
           value="<?php echo htmlspecialchars($categoria['cor']); ?>">
</div>

<div class="mb-3">
    <label for="edit_descricao" class="form-label">Descrição</label>
    <textarea class="form-control" id="edit_descricao" name="descricao" rows="3"><?php echo htmlspecialchars($categoria['descricao'] ?? ''); ?></textarea>
</div>

<?php if ($categoria['is_default']): ?>
<div class="alert alert-info">
    <i class="bi bi-info-circle"></i> Esta é uma categoria padrão do sistema e não pode ser excluída.
</div>
<?php endif; ?>