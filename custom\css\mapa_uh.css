/* Estilos básicos para a tabela */
.table th,
.table td {
    text-align: center;
    vertical-align: middle;
    padding: 6px 4px;
    /* Padding reduzido para economizar espaço */
}

/* Estilo para a primeira coluna (UH) */
.table td:first-child,
.table th:first-child {
    font-weight: bold;
    position: sticky;
    left: 0;
    background-color: #f8f9fa;
    z-index: 1;
    border-right: 2px solid #dee2e6;
}

/* Estilos específicos para células com dois ícones */
.ocupado-manha,
.ocupado-tarde {
    min-width: 40px;
    /* Largura mínima apenas para células com dois ícones */
}

/* Container flexível apenas para células com dois ícones */
.icone-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    /* Espaço entre os ícones */
}

/* Estilos para células com status específicos */
.livre {
    background-color: rgba(40, 167, 69, 0.1);
    /* Verde claro */
}

.ocupado {
    background-color: rgba(220, 53, 69, 0.1);
    /* Vermelho claro */
}

.ocupado-manha {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 50%, rgba(40, 167, 69, 0.1) 50%);
}

.ocupado-tarde {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 50%, rgba(220, 53, 69, 0.1) 50%);
}

.cell-clickable {
    cursor: pointer;
}

/* Responsividade da tabela */
@media (max-width: 768px) {
    .table-responsive {
        max-width: 100%;
        overflow-x: auto;
    }

    .table th,
    .table td {
        padding: 4px 2px;
        font-size: 0.9rem;
    }

    /* Manter largura mínima mesmo em telas pequenas */
    .ocupado-manha,
    .ocupado-tarde {
        min-width: 35px;
    }
}

/* Estilos para datas passadas */
.data-passada {
    opacity: 0.6;
    background-color: rgba(108, 117, 125, 0.1) !important;
    /* Cinza claro */
}

.data-passada i.bi-check-circle-fill {
    color: #6c757d !important;
    /* Substitui o verde por cinza */
}

.data-passada i.bi-person-fill {
    color: #6c757d !important;
    /* Substitui o vermelho por cinza */
}

/* Estilo para a data atual (hoje) */
.data-hoje {
    border: 2px solid #0d6efd;
    /* Borda azul para destacar o dia atual */
}

/* Estilos para ocupação dupla */
.ocupado-duplo {
    background-color: rgba(220, 53, 69, 0.2);
    /* Vermelho mais intenso */
}

.icone-container-duplo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2px;
    /* Espaço entre os ícones */
}

/* Estilos para manutenção */
.manutencao {
    background-color: rgba(253, 126, 20, 0.1);
    /* Laranja claro */
    border-left: 3px solid #fd7e14;
    /* Borda laranja */
}

/* Estilos para limpeza */
.limpeza {
    background-color: rgba(32, 201, 151, 0.1);
    /* Teal claro */
    border-left: 3px solid #20c997;
    /* Borda teal */
}

/* Estilos para status desconhecido */
.status-desconhecido {
    background-color: rgba(108, 117, 125, 0.1);
    /* Cinza claro */
    border-left: 3px solid #6c757d;
    /* Borda cinza */
}

.icone-container-duplo i {
    font-size: 0.9em;
    /* Ícones ligeiramente menores para caber dois */
}

/* Estilos para os cabeçalhos dos dias */
.dia-cabecalho {
    text-align: center;
    padding: 4px;
}

.letra-dia {
    font-size: 0.8em;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
}

.numero-dia {
    font-size: 1em;
}

/* Destacar fins de semana */
.fim-semana {
    background-color: #fff5f5;
}

.fim-semana .letra-dia {
    color: #dc3545;
}

/* Responsividade para cabeçalhos dos dias */
@media (max-width: 768px) {
    .letra-dia {
        font-size: 0.7em;
    }

    .numero-dia {
        font-size: 0.9em;
    }
}

/* Estilo para tarja amarela em datas anteriores a hoje */
.data-anterior-hoje {
    background-color: rgba(255, 193, 7, 0.3) !important;
    /* Amarelo translúcido */
    border: 2px solid #ffc107 !important;
    /* Borda amarela */
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5) !important;
    /* Sombra amarela */
}

.data-anterior-hoje::before {
    content: "Data Antiga";
    position: absolute;
    top: -25px;
    left: 0;
    right: 0;
    background-color: #eff150;
    color: #000;
    font-size: 0.75rem;
    font-weight: bold;
    text-align: center;
    padding: 2px 4px;
    border-radius: 3px;
    z-index: 1000;
    white-space: nowrap;
}

.data-anterior-hoje {
    position: relative;
}

/* Estilos para os números de reservas */
.numero-reserva {
    border-radius: 50%;
    font-size: 0.8em;
    font-weight: bold;
    min-width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

/* Cores específicas para cada número */
.numero-reserva-1 {
    background-color: #0910e4;
    /* Verde */
}

.numero-reserva-2 {
    background-color: #ffc107;
    /* Amarelo */
    color: #212529;
    /* Texto escuro para melhor contraste */
    text-shadow: none;
}

.numero-reserva-3 {
    background-color: #fd7e14;
    /* Laranja */
}

.numero-reserva-4plus {
    background-color: #dc3545;
    /* Vermelho */
}

/* Responsividade para os números */
@media (max-width: 768px) {
    .numero-reserva {
        font-size: 0.7em;
        min-width: 18px;
        height: 18px;
    }
}

/* Hover effect para os números */
.numero-reserva:hover {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Estilos para botões de navegação no modal */
.navigation-buttons {
    display: flex;
    gap: 5px;
    margin-left: auto;
    margin-right: 10px;
}

.navigation-buttons .btn {
    width: 35px;
    height: 35px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
}

.navigation-buttons .btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

/* Destaque para a data atual no modal */
.data-atual-destaque {
    background-color: #b3d9ff !important;
    color: #0056b3 !important;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}