<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

// Definir período padrão (mês atual)
$data_inicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : date('Y-m-01');
$data_fim = isset($_GET['data_fim']) ? $_GET['data_fim'] : date('Y-m-t');
$campo_data = isset($_GET['campo_data']) ? $_GET['campo_data'] : 'vencimento';
$agrupar_por = isset($_GET['agrupar_por']) ? $_GET['agrupar_por'] : 'mes';

// Definir o formato de agrupamento
$format_sql = '';
$format_label = '';

switch ($agrupar_por) {
    case 'dia':
        $format_sql = "DATE(data_$campo_data)";
        $format_label = 'd/m/Y';
        break;
    case 'semana':
        $format_sql = "YEARWEEK(data_$campo_data, 1)";
        $format_label = '\S\e\m\a\n\a W \d\e Y';
        break;
    case 'mes':
        $format_sql = "DATE_FORMAT(data_$campo_data, '%Y-%m-01')";
        $format_label = 'F/Y';
        break;
    case 'ano':
        $format_sql = "YEAR(data_$campo_data)";
        $format_label = 'Y';
        break;
    default:
        $format_sql = "DATE_FORMAT(data_$campo_data, '%Y-%m-01')";
        $format_label = 'F/Y';
}

// Obter dados de fluxo de caixa por período
$sql = "SELECT 
        $format_sql as periodo,
        SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) as receitas,
        SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as despesas,
        SUM(CASE WHEN tipo = 'receita' AND status = 'pago' THEN valor ELSE 0 END) - 
        SUM(CASE WHEN tipo = 'despesa' AND status = 'pago' THEN valor ELSE 0 END) as saldo
        FROM lancamentos_financeiros
        WHERE pousada_id = $pousada_id 
        AND data_$campo_data BETWEEN '$data_inicio' AND '$data_fim'
        GROUP BY periodo
        ORDER BY periodo ASC";

$result = $conn->query($sql);

// Calcular totais
$total_receitas = 0;
$total_despesas = 0;
$total_saldo = 0;

// Preparar dados para o gráfico
$periodos = [];
$receitas = [];
$despesas = [];
$saldos = [];
$saldo_acumulado = 0;
$saldos_acumulados = [];

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        // Formatar o período conforme o agrupamento
        if ($agrupar_por == 'semana') {
            // Para semanas, precisamos extrair o ano e a semana
            $ano = substr($row['periodo'], 0, 4);
            $semana = substr($row['periodo'], 4);
            $data_semana = new DateTime();
            $data_semana->setISODate($ano, $semana);
            $periodo_formatado = $data_semana->format($format_label);
        } elseif ($agrupar_por == 'mes') {
            $periodo_formatado = date($format_label, strtotime($row['periodo']));
            // Traduzir mês para português
            $meses_en = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            $meses_pt = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
            $periodo_formatado = str_replace($meses_en, $meses_pt, $periodo_formatado);
        } else {
            $periodo_formatado = date($format_label, strtotime($row['periodo']));
        }
        
        $periodos[] = $periodo_formatado;
        $receitas[] = floatval($row['receitas']);
        $despesas[] = floatval($row['despesas']);
        $saldos[] = floatval($row['saldo']);
        
        $saldo_acumulado += floatval($row['saldo']);
        $saldos_acumulados[] = $saldo_acumulado;
        
        $total_receitas += floatval($row['receitas']);
        $total_despesas += floatval($row['despesas']);
    }
    
    $total_saldo = $total_receitas - $total_despesas;
}

// Converter arrays para formato JSON para uso nos gráficos
$periodos_json = json_encode($periodos);
$receitas_json = json_encode($receitas);
$despesas_json = json_encode($despesas);
$saldos_json = json_encode($saldos);
$saldos_acumulados_json = json_encode($saldos_acumulados);
?>

<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-3">Relatório de Fluxo de Caixa</h2>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-lightbulb-fill me-2" style="font-size: 1.2em;"></i>
                <div>
                    <strong>Aqui você:</strong> Análise detalhada do fluxo de caixa por período, mostrando entradas, saídas e saldo acumulado para controle financeiro eficiente.
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <a href="index.php?page=dashboard_financeiro" class="btn btn-outline-primary me-2">
                <i class="bi bi-graph-up"></i> Dashboard
            </a>
            <a href="index.php?page=contas" class="btn btn-primary">
                <i class="bi bi-currency-dollar"></i> Gerenciar Contas
            </a>
        </div>
    </div>
    
    <!-- Filtros -->
    <div class="card mb-4 no-print">
        <div class="card-header">
            <i class="bi bi-funnel"></i> Filtros
        </div>
        <div class="card-body">
            <form method="GET" action="index.php" class="row g-3">
                <input type="hidden" name="page" value="relatorio_fluxo_caixa">
                
                <div class="col-md-3">
                    <label for="data_inicio" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?= $data_inicio ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="data_fim" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?= $data_fim ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="agrupar_por" class="form-label">Agrupar por</label>
                    <select class="form-select" id="agrupar_por" name="agrupar_por">
                        <option value="dia" <?= $agrupar_por == 'dia' ? 'selected' : '' ?>>Dia</option>
                        <option value="semana" <?= $agrupar_por == 'semana' ? 'selected' : '' ?>>Semana</option>
                        <option value="mes" <?= $agrupar_por == 'mes' ? 'selected' : '' ?>>Mês</option>
                        <option value="ano" <?= $agrupar_por == 'ano' ? 'selected' : '' ?>>Ano</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="campo_data" class="form-label">Campo de Data</label>
                    <select class="form-select" id="campo_data" name="campo_data">
                        <option value="vencimento" <?= $campo_data == 'vencimento' ? 'selected' : '' ?>>Vencimento</option>
                        <option value="pagamento" <?= $campo_data == 'pagamento' ? 'selected' : '' ?>>Pagamento</option>
                        <option value="lancamento" <?= $campo_data == 'lancamento' ? 'selected' : '' ?>>Lançamento</option>
                    </select>
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i> Filtrar
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Resumo -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-info-circle"></i> Resumo do Período: <?= date('d/m/Y', strtotime($data_inicio)) ?> a <?= date('d/m/Y', strtotime($data_fim)) ?>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 text-center">
                    <h5 class="text-success">Total de Receitas</h5>
                    <h3>R$ <?= number_format($total_receitas, 2, ',', '.') ?></h3>
                </div>
                <div class="col-md-4 text-center">
                    <h5 class="text-danger">Total de Despesas</h5>
                    <h3>R$ <?= number_format($total_despesas, 2, ',', '.') ?></h3>
                </div>
                <div class="col-md-4 text-center">
                    <h5 class="<?= $total_saldo >= 0 ? 'text-primary' : 'text-warning' ?>">Saldo do Período</h5>
                    <h3>R$ <?= number_format($total_saldo, 2, ',', '.') ?></h3>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Gráfico -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-graph-up"></i> Gráfico de Fluxo de Caixa
        </div>
        <div class="card-body">
            <div style="height: 400px;">
                <canvas id="fluxoCaixaChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Tabela de Dados -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table"></i> Dados Detalhados
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Período</th>
                            <th class="text-end">Receitas</th>
                            <th class="text-end">Despesas</th>
                            <th class="text-end">Saldo</th>
                            <th class="text-end">Saldo Acumulado</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        if ($result && $result->num_rows > 0):
                            $result->data_seek(0);
                            $saldo_acumulado = 0;
                            $i = 0;
                            while ($row = $result->fetch_assoc()):
                                // Formatar o período conforme o agrupamento
                                if ($agrupar_por == 'semana') {
                                    $ano = substr($row['periodo'], 0, 4);
                                    $semana = substr($row['periodo'], 4);
                                    $data_semana = new DateTime();
                                    $data_semana->setISODate($ano, $semana);
                                    $periodo_formatado = $data_semana->format($format_label);
                                } elseif ($agrupar_por == 'mes') {
                                    $periodo_formatado = date($format_label, strtotime($row['periodo']));
                                    // Traduzir mês para português
                                    $meses_en = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                                    $meses_pt = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];
                                    $periodo_formatado = str_replace($meses_en, $meses_pt, $periodo_formatado);
                                } else {
                                    $periodo_formatado = date($format_label, strtotime($row['periodo']));
                                }
                                
                                $saldo = floatval($row['saldo']);
                                $saldo_acumulado += $saldo;
                        ?>
                            <tr>
                                <td><?= $periodo_formatado ?></td>
                                <td class="text-end text-success">R$ <?= number_format($row['receitas'], 2, ',', '.') ?></td>
                                <td class="text-end text-danger">R$ <?= number_format($row['despesas'], 2, ',', '.') ?></td>
                                <td class="text-end <?= $saldo >= 0 ? 'text-primary' : 'text-warning' ?>">
                                    R$ <?= number_format($saldo, 2, ',', '.') ?>
                                </td>
                                <td class="text-end <?= $saldo_acumulado >= 0 ? 'text-primary' : 'text-warning' ?>">
                                    R$ <?= number_format($saldo_acumulado, 2, ',', '.') ?>
                                </td>
                            </tr>
                        <?php 
                                $i++;
                            endwhile;
                        else:
                        ?>
                            <tr>
                                <td colspan="5" class="text-center">Nenhum dado encontrado para o período selecionado.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-dark">
                            <th>Total</th>
                            <th class="text-end text-success">R$ <?= number_format($total_receitas, 2, ',', '.') ?></th>
                            <th class="text-end text-danger">R$ <?= number_format($total_despesas, 2, ',', '.') ?></th>
                            <th class="text-end <?= $total_saldo >= 0 ? 'text-primary' : 'text-warning' ?>">
                                R$ <?= number_format($total_saldo, 2, ',', '.') ?>
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Scripts para os gráficos -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gráfico de Fluxo de Caixa
    const ctxFluxo = document.getElementById('fluxoCaixaChart');
    if (ctxFluxo) {
        new Chart(ctxFluxo, {
            type: 'bar',
            data: {
                labels: <?= $periodos_json ?>,
                datasets: [
                    {
                        label: 'Receitas',
                        data: <?= $receitas_json ?>,
                        backgroundColor: 'rgba(40, 167, 69, 0.7)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 1,
                        order: 2
                    },
                    {
                        label: 'Despesas',
                        data: <?= $despesas_json ?>,
                        backgroundColor: 'rgba(220, 53, 69, 0.7)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 1,
                        order: 2
                    },
                    {
                        label: 'Saldo Acumulado',
                        data: <?= $saldos_acumulados_json ?>,
                        type: 'line',
                        backgroundColor: 'rgba(0, 123, 255, 0.2)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        order: 1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Valores (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    },
                    y1: {
                        position: 'right',
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Saldo Acumulado (R$)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': R$ ' + context.raw.toLocaleString('pt-BR', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        padding: 20px;
    }
    
    .container-fluid {
        width: 100%;
        padding: 0;
    }
    
    .card {
        border: none;
        margin-bottom: 30px;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 1px solid #ddd;
    }
    
    h2, h3, h4, h5 {
        color: #000 !important;
    }
    
    .text-success {
        color: #28a745 !important;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
    
    .text-primary {
        color: #007bff !important;
    }
    
    .text-warning {
        color: #ffc107 !important;
    }
    
    table {
        width: 100%;
        border-collapse: collapse;
    }
    
    table th, table td {
        border: 1px solid #ddd;
        padding: 8px;
    }
    
    table th {
        background-color: #f8f9fa !important;
    }
}
</style>