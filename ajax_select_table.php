<?php
session_start();
require_once 'config.php';

// Verificar se o usuário é super administrador
if (!isset($_SESSION['user_pousada_id']) || $_SESSION['user_pousada_id'] != 0) {
    http_response_code(403);
    echo json_encode(['error' => 'Acesso negado']);
    exit;
}

// Verificar se foi enviado o nome da tabela
if (!isset($_POST['table_name']) || empty($_POST['table_name'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Nome da tabela não fornecido']);
    exit;
}

$table_name = $_POST['table_name'];

// Validar nome da tabela
if (!preg_match('/^[a-zA-Z0-9_]+$/', $table_name)) {
    http_response_code(400);
    echo json_encode(['error' => 'Nome de tabela inválido']);
    exit;
}

$start_time = microtime(true);

try {
    $select_query = "SELECT * FROM `$table_name` LIMIT 100";
    $select_result = $conn->query($select_query);
    
    if ($select_result === false) {
        echo json_encode(['error' => "Erro na consulta: " . $conn->error]);
        exit;
    }
    
    $execution_time = microtime(true) - $start_time;
    
    $response = [
        'success' => true,
        'query' => $select_query,
        'affected_rows' => $select_result->num_rows,
        'execution_time' => number_format($execution_time * 1000, 2),
        'html' => ''
    ];
    
    if ($select_result->num_rows > 0) {
        $html = '<div class="alert alert-success">';
        $html .= 'Consulta executada com sucesso. ';
        $html .= $select_result->num_rows . ' registros encontrados. ';
        $html .= 'Tempo: ' . number_format($execution_time * 1000, 2) . ' ms';
        $html .= '</div>';
        
        $html .= '<div class="table-responsive">';
        $html .= '<table class="table table-striped table-bordered table-sm">';
        $html .= '<thead class="table-dark"><tr>';
        
        $fields = $select_result->fetch_fields();
        foreach ($fields as $field) {
            $html .= '<th style="font-size: 0.8rem;">' . htmlspecialchars($field->name) . '</th>';
        }
        $html .= '</tr></thead><tbody>';
        
        while ($row = $select_result->fetch_assoc()) {
            $html .= '<tr>';
            foreach ($row as $value) {
                $html .= '<td style="font-size: 0.8rem;">' . htmlspecialchars($value ?? 'NULL') . '</td>';
            }
            $html .= '</tr>';
        }
        
        $html .= '</tbody></table></div>';
    } else {
        $html = '<div class="alert alert-info">Tabela vazia - nenhum registro encontrado.</div>';
    }
    
    $response['html'] = $html;
    
    header('Content-Type: application/json');
    echo json_encode($response);
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => "Exceção: " . $e->getMessage()]);
}
?>