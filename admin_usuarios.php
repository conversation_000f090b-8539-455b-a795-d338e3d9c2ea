<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include_once("config.php");
include_once("func.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    echo "<script>window.location.href = 'login.php';</script>";
    return;
}


$isAdmin = isAdmin($conn, $_SESSION['user_id']);

if (!$isAdmin) {
    echo "<h1><center>Apenas o Administrador pode cadastrar usuários</center></h1>";
    echo "<script>
            setTimeout(function(){
                window.location.href = 'index.php';
            }, 3000); // Redireciona após 3 segundos (3000 milissegundos)
          </script>";
    exit();
}


// Obtém o ID da pousada do usuário logado
$pousada_id = $_SESSION['user_pousada_id'];

// Obtém o limite de usuários do plano atual
$stmt = prepareAndExecute($conn, 
    "SELECT p.limite_usuarios 
     FROM contratos c 
     JOIN planos p ON c.plano_id = p.id 
     WHERE c.pousada_id = ? AND c.status = 'ativo'", 
    'i', 
    $pousada_id
);
$result = $stmt->get_result();
$plano = $result->fetch_assoc();
$limite_usuarios = $plano['limite_usuarios'] ?? 0;

// Obtém o número atual de usuários
$stmt = prepareAndExecute($conn, 
    "SELECT COUNT(*) as total FROM usuarios WHERE pousada_id = ?", 
    'i', 
    $pousada_id
);
$result = $stmt->get_result();
$total_usuarios = $result->fetch_assoc()['total'];

// Processa as ações do formulário
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $action = $_POST['action'] ?? '';
    $nome = $_POST['nome'] ?? '';
    $email = $_POST['email'] ?? '';
    $senha = $_POST['senha'] ?? '';
    $id = $_POST['id'] ?? 0;

    switch ($action) {
        case 'cadastrar':
            if ($total_usuarios >= $limite_usuarios) {
                $_SESSION['error'] = "Limite de usuários atingido para o plano atual.";
                break;
            }

            // Verifica se o email já existe
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM usuarios WHERE email = ?", 
                's', 
                $email
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este email já está cadastrado.";
                break;
            }

            // Cadastra o novo usuário
            $senha_hash = password_hash($senha, PASSWORD_DEFAULT);
            $stmt = prepareAndExecute($conn, 
                "INSERT INTO usuarios (nome, email, senha, pousada_id, is_admin) VALUES (?, ?, ?, ?, 0)", 
                'sssi', 
                $nome, $email, $senha_hash, $pousada_id
            );
            
            if ($stmt->affected_rows > 0) {
                $_SESSION['success'] = "Usuário cadastrado com sucesso.";
            } else {
                $_SESSION['error'] = "Erro ao cadastrar usuário.";
            }
            break;

        case 'alterar':
            // Verifica se o email já existe para outro usuário
            $stmt = prepareAndExecute($conn, 
                "SELECT id FROM usuarios WHERE email = ? AND id != ?", 
                'si', 
                $email, $id
            );
            if ($stmt->get_result()->num_rows > 0) {
                $_SESSION['error'] = "Este email já está cadastrado para outro usuário.";
                break;
            }

            // Atualiza o usuário
            if (!empty($senha)) {
                $senha_hash = password_hash($senha, PASSWORD_DEFAULT);
                $stmt = prepareAndExecute($conn, 
                    "UPDATE usuarios SET nome = ?, email = ?, senha = ? WHERE id = ? AND pousada_id = ?", 
                    'sssii', 
                    $nome, $email, $senha_hash, $id, $pousada_id
                );
            } else {
                $stmt = prepareAndExecute($conn, 
                    "UPDATE usuarios SET nome = ?, email = ? WHERE id = ? AND pousada_id = ?", 
                    'ssii', 
                    $nome, $email, $id, $pousada_id
                );
            }
            
            if ($stmt->affected_rows > 0) {
                $_SESSION['success'] = "Usuário alterado com sucesso.";
            } else {
                $_SESSION['error'] = "Erro ao alterar usuário.";
            }
            break;

        case 'excluir':

             // Log da consulta para depuração
            $query = "DELETE FROM usuarios WHERE id = $id AND pousada_id = $pousada_id";
            error_log("Tentativa de exclusão: $query");   
            
            try {
                // Verificar se a função prepareAndExecute existe
                if (!function_exists('prepareAndExecute')) {
                    throw new Exception("Função prepareAndExecute não encontrada");
                }
                
                // Verificar se a conexão está ativa
                if (!$conn || $conn->connect_error) {
                    throw new Exception("Problema com a conexão ao banco de dados: " . ($conn ? $conn->connect_error : "Conexão nula"));
                }
                
                // Tentar executar a consulta
                $stmt = prepareAndExecute($conn, 
                    "DELETE FROM usuarios WHERE id = ? AND pousada_id = ?", 
                    'ii', 
                    $id, $pousada_id
                );
                
                error_log("Após prepareAndExecute - Chegou aqui");
                
                if ($stmt && $stmt->affected_rows > 0) {
                    error_log("Usuário excluído com sucesso. Affected rows: " . $stmt->affected_rows);
                    $_SESSION['success'] = "Usuário excluído com sucesso.";
                } else {
                    error_log("Nenhuma linha afetada. Stmt: " . ($stmt ? "Objeto válido" : "Nulo"));
                    $_SESSION['error'] = "Erro ao excluir usuário. Nenhum registro foi afetado.";
                }
            } catch (Exception $e) {
                error_log("Exceção ao excluir usuário: " . $e->getMessage());
                $_SESSION['error'] = "Erro ao excluir usuário: " . $e->getMessage();
            }
            break;
    }

    // Redireciona para evitar reenvio do formulário
    echo "<script>window.location.href = 'admin_usuarios.php';</script>";
    return;
}

// Obtém a lista de usuários da pousada (apenas não administradores)
$stmt = prepareAndExecute($conn, 
    "SELECT id, nome, email, is_admin 
     FROM usuarios 
     WHERE pousada_id = ? AND is_admin = 0
     ORDER BY nome", 
    'i', 
    $pousada_id
);
$usuarios = $stmt->get_result();

//RETIRE, APENAS TEMPORÁRIO
// Debug: Verificar os dados retornados e a sessão
error_log("Dados da sessão - User ID: " . $_SESSION['user_id'] . 
          " | User Name: " . $_SESSION['user_name'] . 
          " | User Email: " . $_SESSION['user_email'] . 
          " | User Pousada ID: " . $_SESSION['user_pousada_id'] . 
          " | User Is Admin: " . $_SESSION['user_is_admin']);

error_log("Dados dos usuários:");
$usuarios_array = array();
while ($usuario = $usuarios->fetch_assoc()) {
    $usuarios_array[] = $usuario;
    error_log("ID: " . $usuario['id'] . 
              " | Nome: " . $usuario['nome'] . 
              " | Email: " . $usuario['email'] . 
              " | is_admin: " . $usuario['is_admin']);
}
$usuarios = $usuarios_array; // Convertendo o resultado em array para evitar problemas com o ponteiro
//FINAL DO TEMPORÁRIO
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administração de Usuários</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
</head>
<body>
    <div class="container mt-4">
        <h2>Administração de Usuários</h2>
        
        <?php if (isset($_SESSION['error'])): ?>
            <div class="alert alert-danger"><?php echo $_SESSION['error']; unset($_SESSION['error']); ?></div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['success'])): ?>
            <div class="alert alert-success"><?php echo $_SESSION['success']; unset($_SESSION['success']); ?></div>
        <?php endif; ?>

        <div class="alert alert-info">
            <strong>Importante:</strong> O administrador não pode ser alterado por aqui, apenas pelo site www.claudiosegura.com
        </div>

        <div class="alert alert-warning">
            Limite de usuários: <?php echo $total_usuarios; ?>/<?php echo $limite_usuarios; ?>
        </div>
      
        <!-- Botão para abrir o modal de cadastro -->
        <div class="mb-4">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#cadastrarModal">
                <i class="bi bi-person-plus"></i> Novo Usuário
            </button>
        </div>

        <!-- Lista de Usuários -->
        <div class="card">
            <div class="card-header">
                <h5>Usuários Cadastrados</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>Permissão</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($usuarios as $usuario): 
                                // Debug temporário
                                error_log("Exibindo usuário na tabela - ID: " . $usuario['id'] . 
                                         " | Nome: " . $usuario['nome'] . 
                                         " | Email: " . $usuario['email']);
                            ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($usuario['nome']); ?></td>
                                    <td id="email-<?php echo $usuario['id']; ?>"><?php echo htmlspecialchars($usuario['email']); ?></td>
                                    <td>Usuário</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" 
                                                onclick="editarUsuario(<?php echo $usuario['id']; ?>)">
                                            Editar
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="excluirUsuario(<?php echo $usuario['id']; ?>)">
                                            Excluir
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Cadastro -->
    <div class="modal fade" id="cadastrarModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Cadastrar Novo Usuário</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="cadastrarForm" method="POST" action="">
                        <input type="hidden" name="action" value="cadastrar">
                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="nome" name="nome" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="senha" class="form-label">Senha</label>
                            <input type="password" class="form-control" id="senha" name="senha" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="cadastrarForm" class="btn btn-primary">Cadastrar</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal de Edição -->
    <div class="modal fade" id="editarModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Editar Usuário</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editarForm" method="POST" action="">
                        <input type="hidden" name="action" value="alterar">
                        <input type="hidden" name="id" id="edit_id">
                        <div class="mb-3">
                            <label for="edit_nome" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="edit_nome" name="nome" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_senha" class="form-label">Nova Senha (deixe em branco para manter a atual)</label>
                            <input type="password" class="form-control" id="edit_senha" name="senha">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="editarForm" class="btn btn-primary">Salvar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div class="modal fade" id="excluirModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir este usuário?</p>
                    <form id="excluirForm" method="POST" action="">
                        <input type="hidden" name="action" value="excluir">
                        <input type="hidden" name="id" id="excluir_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="excluirForm" class="btn btn-danger">Excluir</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarUsuario(id) {
            fetch(`pega_dados_usuario.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert(data.error);
                        return;
                    }
                    // Debug temporário
                    console.log("Dados recebidos:", data);
                    
                    // Preencher os campos do formulário
                    document.getElementById('edit_id').value = data.id;
                    document.getElementById('edit_nome').value = data.nome;
                    document.getElementById('edit_email').value = data.email;
                    
                    // Mostrar o modal
                    new bootstrap.Modal(document.getElementById('editarModal')).show();
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao carregar dados do usuário');
                });
        }

        function excluirUsuario(id) {
            document.getElementById('excluir_id').value = id;
            new bootstrap.Modal(document.getElementById('excluirModal')).show();
        }
    </script>

<?php
    echo '<br>';
    echo '<button onclick="location.href=\'index.php\';" class="btn btn-success">Home</button>';
?>

</body>
</html> 