<?php

// Reportar todos os erros do PHP
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado

    echo "<script>window.location.href = 'index.html';</script>";
    return;

}

// Verificar se há um caixa aberto
$sql = "SELECT * FROM caixa_diario 
        WHERE pousada_id = $pousada_id 
        AND status = 'aberto' 
        ORDER BY data_abertura DESC 
        LIMIT 1";
$result = $conn->query($sql);
$caixa_aberto = $result->num_rows > 0 ? $result->fetch_assoc() : null;

// Obter histórico de caixas fechados
$sql_historico = "SELECT cd.*, 
                 u_abertura.nome as usuario_abertura, 
                 u_fechamento.nome as usuario_fechamento,
                 (SELECT SUM(CASE WHEN tipo IN ('entrada', 'suprimento') THEN valor ELSE -valor END)
                  FROM movimentacoes_caixa
                  WHERE caixa_id = cd.id AND pousada_id = $pousada_id) as total_movimentacoes
                 FROM caixa_diario cd
                 JOIN usuarios u_abertura ON cd.usuario_abertura_id = u_abertura.id
                 LEFT JOIN usuarios u_fechamento ON cd.usuario_fechamento_id = u_fechamento.id
                 WHERE cd.pousada_id = $pousada_id
                 ORDER BY cd.data_abertura DESC
                 LIMIT 10";
$historico_caixas = $conn->query($sql_historico);

// Verificar se há mensagem para exibir
$mensagem = isset($_SESSION['mensagem']) ? $_SESSION['mensagem'] : '';
$tipo_mensagem = isset($_SESSION['tipo_mensagem']) ? $_SESSION['tipo_mensagem'] : 'success';

// Limpar mensagens da sessão
unset($_SESSION['mensagem']);
unset($_SESSION['tipo_mensagem']);
?>

<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h2 class="mb-3">Controle de Caixa</h2>
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <i class="bi bi-lightbulb-fill me-2" style="font-size: 1.2em;"></i>
                <div>
                    <strong>Aqui você:</strong>Gerencia as operações diárias de caixa, com registro de aberturas, fechamentos, suprimentos e sangrias para controle do dinheiro físico.
                </div>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <?php if ($caixa_aberto): ?>
                <a href="index.php?page=caixa_diario_detalhes&id=<?= $caixa_aberto['id'] ?>" class="btn btn-info me-2">
                    <i class="bi bi-eye"></i> Ver Caixa Atual
                </a>
                <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#modalSuprimento">
                    <i class="bi bi-plus-circle"></i> Suprimento
                </button>
                <button type="button" class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#modalSangria">
                    <i class="bi bi-dash-circle"></i> Sangria
                </button>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#modalFecharCaixa">
                    <i class="bi bi-lock"></i> Fechar Caixa
                </button>
            <?php else: ?>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAbrirCaixa">
                    <i class="bi bi-unlock"></i> Abrir Caixa
                </button>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (!empty($mensagem)): ?>
        <div class="alert alert-<?= $tipo_mensagem ?> alert-dismissible fade show" role="alert">
            <?= $mensagem ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
        </div>
    <?php endif; ?>
    
    <!-- Status do Caixa Atual -->
    <?php if ($caixa_aberto): ?>
        <?php
        // Calcular saldo atual
        $sql_saldo = "SELECT 
                      SUM(CASE WHEN tipo IN ('entrada', 'suprimento') THEN valor ELSE 0 END) as entradas,
                      SUM(CASE WHEN tipo IN ('saida', 'sangria') THEN valor ELSE 0 END) as saidas
                      FROM movimentacoes_caixa 
                      WHERE caixa_id = " . $caixa_aberto['id'];
        $result_saldo = $conn->query($sql_saldo);
        $saldo = $result_saldo->fetch_assoc();
        
        $entradas = $saldo['entradas'] ?? 0;
        $saidas = $saldo['saidas'] ?? 0;
        $saldo_atual = $caixa_aberto['saldo_inicial'] + $entradas - $saidas;
        
        // Obter informações do usuário que abriu o caixa
        $sql_usuario = "SELECT nome FROM usuarios WHERE id = " . $caixa_aberto['usuario_abertura_id'];
        $result_usuario = $conn->query($sql_usuario);
        $usuario = $result_usuario->fetch_assoc();
        
        // Calcular tempo de operação
        $data_abertura = new DateTime($caixa_aberto['data_abertura']);
        $agora = new DateTime();
        $intervalo = $data_abertura->diff($agora);
        $tempo_operacao = '';
        
        if ($intervalo->days > 0) {
            $tempo_operacao = $intervalo->format('%a dias, %h horas e %i minutos');
        } else {
            $tempo_operacao = $intervalo->format('%h horas e %i minutos');
        }
        ?>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="bi bi-cash-register"></i> Caixa Aberto
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Saldo Inicial</h5>
                                <h3 class="card-text">R$ <?= number_format($caixa_aberto['saldo_inicial'], 2, ',', '.') ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">Entradas</h5>
                                <h3 class="card-text">R$ <?= number_format($entradas, 2, ',', '.') ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h5 class="card-title">Saídas</h5>
                                <h3 class="card-text">R$ <?= number_format($saidas, 2, ',', '.') ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">Saldo Atual</h5>
                                <h3 class="card-text">R$ <?= number_format($saldo_atual, 2, ',', '.') ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <p><strong>Aberto em:</strong> <?= date('d/m/Y H:i', strtotime($caixa_aberto['data_abertura'])) ?></p>
                        <p><strong>Aberto por:</strong> <?= htmlspecialchars($usuario['nome']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Tempo de operação:</strong> <?= $tempo_operacao ?></p>
                        <?php if (!empty($caixa_aberto['observacao'])): ?>
                            <p><strong>Observação:</strong> <?= htmlspecialchars($caixa_aberto['observacao']) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <a href="index.php?page=caixa_diario_detalhes&id=<?= $caixa_aberto['id'] ?>" class="btn btn-primary">
                            <i class="bi bi-list-ul"></i> Ver Detalhes e Movimentações
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-warning mb-4">
            <i class="bi bi-exclamation-triangle"></i> Não há caixa aberto no momento. Abra um novo caixa para registrar movimentações.
        </div>
    <?php endif; ?>
    
    <!-- Histórico de Caixas -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-clock-history"></i> Histórico de Caixas
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Data de Abertura</th>
                            <th>Data de Fechamento</th>
                            <th>Saldo Inicial</th>
                            <th>Saldo Final</th>
                            <th>Movimentação</th>
                            <th>Usuários</th>
                            <th>Status</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($historico_caixas && $historico_caixas->num_rows > 0): ?>
                            <?php while ($caixa = $historico_caixas->fetch_assoc()): ?>
                                <tr>
                                    <td><?= date('d/m/Y H:i', strtotime($caixa['data_abertura'])) ?></td>
                                    <td>
                                        <?= $caixa['data_fechamento'] ? date('d/m/Y H:i', strtotime($caixa['data_fechamento'])) : '-' ?>
                                    </td>
                                    <td>R$ <?= number_format($caixa['saldo_inicial'], 2, ',', '.') ?></td>
                                    <td>
                                        <?= $caixa['saldo_final'] !== null ? 'R$ ' . number_format($caixa['saldo_final'], 2, ',', '.') : '-' ?>
                                    </td>
                                    <td>
                                        <?php 
                                        $movimentacao = $caixa['total_movimentacoes'] ?? 0;
                                        $classe = $movimentacao >= 0 ? 'text-success' : 'text-danger';
                                        echo '<span class="' . $classe . '">R$ ' . number_format($movimentacao, 2, ',', '.') . '</span>';
                                        ?>
                                    </td>
                                    <td>
                                        <small>
                                            <strong>Abertura:</strong> <?= htmlspecialchars($caixa['usuario_abertura']) ?><br>
                                            <?php if ($caixa['usuario_fechamento']): ?>
                                                <strong>Fechamento:</strong> <?= htmlspecialchars($caixa['usuario_fechamento']) ?>
                                            <?php endif; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($caixa['status'] == 'aberto'): ?>
                                            <span class="badge bg-success">Aberto</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Fechado</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="index.php?page=caixa_diario_detalhes&id=<?= $caixa['id'] ?>" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center">Nenhum registro de caixa encontrado.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Modal Abrir Caixa -->
    <div class="modal fade" id="modalAbrirCaixa" tabindex="-1" aria-labelledby="modalAbrirCaixaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="modalAbrirCaixaLabel">Abrir Caixa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=caixa_diario_salvar" method="POST" id="formAbrirCaixa">
                        <input type="hidden" name="acao" value="abrir">
                        
                        <div class="mb-3">
                            <label for="saldo_inicial" class="form-label">Saldo Inicial (R$)</label>
                            <input type="text" class="form-control" id="saldo_inicial" name="saldo_inicial" value="0,00" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="observacao_abertura" class="form-label">Observação (opcional)</label>
                            <textarea class="form-control" id="observacao_abertura" name="observacao" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formAbrirCaixa" class="btn btn-primary">Abrir Caixa</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Fechar Caixa -->
    <div class="modal fade" id="modalFecharCaixa" tabindex="-1" aria-labelledby="modalFecharCaixaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalFecharCaixaLabel">Fechar Caixa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=caixa_diario_salvar" method="POST" id="formFecharCaixa">
                        <input type="hidden" name="acao" value="fechar">
                        <input type="hidden" name="caixa_id" value="<?= $caixa_aberto ? $caixa_aberto['id'] : '' ?>">
                        
                        <div class="mb-3">
                            <p><strong>Saldo Inicial:</strong> R$ <?= $caixa_aberto ? number_format($caixa_aberto['saldo_inicial'], 2, ',', '.') : '0,00' ?></p>
                            <p><strong>Entradas:</strong> R$ <?= number_format($entradas, 2, ',', '.') ?></p>
                            <p><strong>Saídas:</strong> R$ <?= number_format($saidas, 2, ',', '.') ?></p>
                            <p><strong>Saldo Calculado:</strong> R$ <?= number_format($saldo_atual, 2, ',', '.') ?></p>
                        </div>
                        
                        <div class="mb-3">
                            <label for="saldo_final" class="form-label">Saldo Final em Caixa (R$)</label>
                            <input type="text" class="form-control" id="saldo_final" name="saldo_final" value="<?= number_format($saldo_atual, 2, ',', '.') ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="observacao_fechamento" class="form-label">Observação (opcional)</label>
                            <textarea class="form-control" id="observacao_fechamento" name="observacao" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formFecharCaixa" class="btn btn-danger">Fechar Caixa</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Suprimento -->
    <div class="modal fade" id="modalSuprimento" tabindex="-1" aria-labelledby="modalSuprimentoLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="modalSuprimentoLabel">Adicionar Suprimento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=caixa_diario_salvar" method="POST" id="formSuprimento">
                        <input type="hidden" name="acao" value="suprimento">
                        <input type="hidden" name="caixa_id" value="<?= $caixa_aberto ? $caixa_aberto['id'] : '' ?>">
                        
                        <div class="mb-3">
                            <label for="valor_suprimento" class="form-label">Valor (R$)</label>
                            <input type="text" class="form-control" id="valor_suprimento" name="valor" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="descricao_suprimento" class="form-label">Descrição</label>
                            <input type="text" class="form-control" id="descricao_suprimento" name="descricao" placeholder="Ex: Troco para o caixa" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formSuprimento" class="btn btn-success">Adicionar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Sangria -->
    <div class="modal fade" id="modalSangria" tabindex="-1" aria-labelledby="modalSangriaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="modalSangriaLabel">Realizar Sangria</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=caixa_diario_salvar" method="POST" id="formSangria">
                        <input type="hidden" name="acao" value="sangria">
                        <input type="hidden" name="caixa_id" value="<?= $caixa_aberto ? $caixa_aberto['id'] : '' ?>">
                        
                        <div class="mb-3">
                            <label for="valor_sangria" class="form-label">Valor (R$)</label>
                            <input type="text" class="form-control" id="valor_sangria" name="valor" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="descricao_sangria" class="form-label">Descrição</label>
                            <input type="text" class="form-control" id="descricao_sangria" name="descricao" placeholder="Ex: Retirada para depósito" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formSangria" class="btn btn-warning">Realizar Sangria</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurar máscaras para campos de valor
    const camposValor = document.querySelectorAll('input[name="saldo_inicial"], input[name="saldo_final"], input[name="valor"]');
    camposValor.forEach(function(campo) {
        campo.addEventListener('input', function(e) {
            let valor = e.target.value.replace(/\D/g, '');
            valor = (parseFloat(valor) / 100).toFixed(2).replace('.', ',');
            e.target.value = valor;
        });
    });
});
</script>

<?php
// Função para obter o nome do usuário
function obterNomeUsuario($conn, $usuario_id) {
    $stmt = $conn->prepare("SELECT nome FROM usuarios WHERE id = ?");
    $stmt->bind_param("i", $usuario_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        return $result->fetch_assoc()['nome'];
    }
    
    return "Usuário não encontrado";
}

// Função para calcular o tempo de operação
function calcularTempoOperacao($data_abertura) {
    $abertura = new DateTime($data_abertura);
    $agora = new DateTime();
    $intervalo = $abertura->diff($agora);
    
    if ($intervalo->days > 0) {
        return $intervalo->format('%a dias, %h horas e %i minutos');
    } else {
        return $intervalo->format('%h horas e %i minutos');
    }
}
?>

