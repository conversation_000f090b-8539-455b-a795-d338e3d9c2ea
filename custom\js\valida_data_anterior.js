/**
 * Função para validar se uma data/hora selecionada é anterior ao momento atual
 * Aplica tooltips e logs quando necessário
 */
// Importar gerenciador de tooltips
import { tooltipManager } from './tooltip-manager.js';
export class DateTimeValidator {
    /**
     * Valida se a data informada é anterior ao momento atual
     * @param campoData - Input de data a ser validado
     */
    verificarDataAnterior(campoData) {
        // Verifica se o campo tem valor antes de processar
        if (!campoData.value) {
            this.removerClasseDataAnterior(campoData);
            return;
        }
        try {
            // Captura o momento atual do sistema
            const agora = new Date();
            // Cria objeto Date a partir da data selecionada
            const dataSelecionada = new Date(campoData.value);
            // Valida se a data é válida
            if (isNaN(dataSelecionada.getTime())) {
                console.warn(`Data inválida detectada no campo ${campoData.name}: ${campoData.value}`);
                this.removerClasseDataAnterior(campoData);
                return;
            }
            // Busca campo de hora associado
            const campoHora = this.buscarCampoHoraAssociado(campoData.name);
            // Aplica hora específica se disponível, senão usa 00:00:00
            if (campoHora && campoHora.value) {
                if (this.validarFormatoHora(campoHora.value)) {
                    this.aplicarHoraNaData(dataSelecionada, campoHora.value);
                }
                else {
                    console.warn(`Formato de hora inválido: ${campoHora.value}. Usando 00:00:00`);
                    dataSelecionada.setHours(0, 0, 0, 0);
                }
            }
            else {
                // Se não há hora informada, considera início do dia (00:00:00)
                dataSelecionada.setHours(0, 0, 0, 0);
            }
            // Compara com momento atual
            if (dataSelecionada < agora) {
                this.marcarComoDataAnterior(campoData, dataSelecionada, agora);
            }
            else {
                this.removerClasseDataAnterior(campoData);
            }
        }
        catch (error) {
            console.error(`Erro ao validar data no campo ${campoData.name}:`, error);
            this.removerClasseDataAnterior(campoData);
        }
    }
    /**
     * Busca o campo de hora associado ao campo de data
     * @param nomeData - Nome do campo de data
     * @returns HTMLInputElement do campo de hora ou null
     */
    buscarCampoHoraAssociado(nomeData) {
        const mapeamentoHoras = {
            'dataentrada': 'horaentrada',
            'datasaida': 'horasaida'
        };
        const nomeHora = mapeamentoHoras[nomeData];
        if (!nomeHora)
            return null;
        return document.querySelector(`input[name="${nomeHora}"]`);
    }
    /**
     * Valida se o formato da hora está correto (HH:MM)
     * @param hora - String da hora a ser validada
     * @returns boolean indicando se o formato é válido
     */
    validarFormatoHora(hora) {
        const regexHora = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return regexHora.test(hora);
    }
    /**
     * Aplica a hora específica no objeto Date
     * @param data - Objeto Date a ser modificado
     * @param hora - String da hora no formato HH:MM
     */
    aplicarHoraNaData(data, hora) {
        const [horas, minutos] = hora.split(':').map(Number);
        data.setHours(horas, minutos, 0, 0);
    }
    /**
     * Marca o campo como tendo data anterior e aplica feedback visual
     * @param campo - Campo de data a ser marcado
     * @param dataSelecionada - Data selecionada pelo usuário
     * @param agora - Momento atual
     */
    marcarComoDataAnterior(campo, dataSelecionada, agora) {
        // Mostrar tooltip de data antiga
        tooltipManager.showOldDateTooltip(campo);
        // Log de aviso com contexto detalhado
        const contexto = this.obterContextoCampo(campo.name);
        console.log(`⚠️  Data anterior detectada - ${contexto}:`, {
            campo: campo.name,
            dataSelecionada: this.formatarDataParaLog(dataSelecionada),
            momentoAtual: this.formatarDataParaLog(agora),
            diferenca: `${Math.round((agora.getTime() - dataSelecionada.getTime()) / (1000 * 60))} minutos atrás`
        });
    }
    /**
     * Remove o tooltip de data anterior
     * @param campo - Campo de data
     */
    removerClasseDataAnterior(campo) {
        tooltipManager.hideTooltip(campo);
    }
    /**
     * Obtém contexto descritivo para o campo
     * @param nomeCampo - Nome do campo
     * @returns String descritiva do campo
     */
    obterContextoCampo(nomeCampo) {
        const contextos = {
            'dataentrada': 'Data de Entrada',
            'datasaida': 'Data de Saída'
        };
        return contextos[nomeCampo] || nomeCampo;
    }
    /**
     * Formata data para exibição no log
     * @param data - Data a ser formatada
     * @returns String formatada da data
     */
    formatarDataParaLog(data) {
        return data.toLocaleString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    /**
     * Método público para validar um campo específico
     * @param seletorCampo - Seletor CSS do campo a ser validado
     */
    validarCampo(seletorCampo) {
        const campo = document.querySelector(seletorCampo);
        if (campo) {
            this.verificarDataAnterior(campo);
        }
        else {
            console.warn(`Campo não encontrado: ${seletorCampo}`);
        }
    }
    /**
     * Método público para validar todos os campos de data de reserva
     */
    validarTodosCampos() {
        const campos = ['input[name="dataentrada"]', 'input[name="datasaida"]'];
        campos.forEach(seletor => {
            const campo = document.querySelector(seletor);
            if (campo) {
                this.verificarDataAnterior(campo);
            }
        });
    }
    /**
     * Inicializa listeners para validação automática
     */
    inicializarValidacao() {
        const campos = document.querySelectorAll('input[name="dataentrada"], input[name="datasaida"]');
        const camposHora = document.querySelectorAll('input[name="horaentrada"], input[name="horasaida"]');
        // Listeners para campos de data
        campos.forEach(campo => {
            campo.addEventListener('change', () => this.verificarDataAnterior(campo));
            campo.addEventListener('blur', () => this.verificarDataAnterior(campo));
        });
        // Listeners para campos de hora (revalida data associada)
        camposHora.forEach(campoHora => {
            campoHora.addEventListener('change', () => {
                const nomeCampoData = campoHora.name.replace('hora', 'data');
                const campoData = document.querySelector(`input[name="${nomeCampoData}"]`);
                if (campoData) {
                    this.verificarDataAnterior(campoData);
                }
            });
        });
    }
}
