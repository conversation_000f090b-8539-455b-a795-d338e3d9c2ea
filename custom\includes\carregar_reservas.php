<?php

session_start();



try {

    // Verificar se o usuário está logado

    if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {

        echo json_encode(['erro' => 'Usuário não autenticado']);

        exit;

    }

    

    include_once("../../config.php");

    

    $acao = $_POST['acao'] ?? '';

    

    if ($acao === 'carregar_reservas') {

        $uh = $_POST['uh'] ?? null;

        $pousada_id = $_SESSION['user_pousada_id'];

        

        $sql = "SELECT id, uh, dataentrada, horaentrada, datasaida, horasaida, hospede_nome, hospede_id 

                FROM reservas 

                WHERE pousada_id = ? 

                AND dataentrada IS NOT NULL 

                AND datasaida IS NOT NULL";

        

        if ($uh) {

            $sql .= " AND uh = ?";

        }

        

        $stmt = $conn->prepare($sql);

        

        if ($uh) {

            $stmt->bind_param("is", $pousada_id, $uh);

        } else {

            $stmt->bind_param("i", $pousada_id);

        }

        

        $stmt->execute();

        $result = $stmt->get_result();

        

        $reservas = [];

        while ($row = $result->fetch_assoc()) {

            $reservas[] = $row;

        }

        

        echo json_encode([

            'sucesso' => true,

            'reservas' => $reservas

        ]);

    } else {

        echo json_encode(['erro' => 'Ação inválida']);

    }

    

} catch (Exception $e) {

    error_log("Erro em carregar_reservas.php: " . $e->getMessage());

    echo json_encode(['erro' => 'Erro interno no servidor']);

}

?>
