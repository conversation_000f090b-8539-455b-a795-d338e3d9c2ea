<?php

echo "Teste funcionando!";

// Reportar todos os erros do PHP
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");



// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;

}

// Verificar se o ID do caixa foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['mensagem'] = "ID do caixa não fornecido.";
    $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;

}

$caixa_id = intval($_GET['id']);

// Obter informações do caixa
$sql = "SELECT cd.*, 
         u_abertura.nome as usuario_abertura, 
         u_fechamento.nome as usuario_fechamento
         FROM caixa_diario cd
         JOIN usuarios u_abertura ON cd.usuario_abertura_id = u_abertura.id
         LEFT JOIN usuarios u_fechamento ON cd.usuario_fechamento_id = u_fechamento.id
         WHERE cd.id = $caixa_id AND cd.pousada_id = $pousada_id";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    $_SESSION['mensagem'] = "Caixa não encontrado.";
    $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=caixa_diario';</script>";
    return;
}

$caixa = $result->fetch_assoc();

// Obter movimentações do caixa
$sql_movimentacoes = "SELECT mc.*,
                      u.nome as usuario_nome,
                      lf.descricao as lancamento_descricao,
                      lf.tipo as lancamento_tipo,
                      cf.nome as categoria_nome,
                      cf.cor as categoria_cor
                      FROM movimentacoes_caixa mc
                      JOIN usuarios u ON mc.usuario_id = u.id
                      LEFT JOIN lancamentos_financeiros lf ON mc.lancamento_id = lf.id
                      LEFT JOIN categorias_financeiras cf ON lf.categoria_id = cf.id
                      WHERE mc.caixa_id = $caixa_id AND mc.pousada_id = $pousada_id
                      ORDER BY mc.data_hora DESC";
$movimentacoes = $conn->query($sql_movimentacoes);

// Calcular totais
$sql_totais = "SELECT
               SUM(CASE WHEN tipo = 'entrada' THEN valor ELSE 0 END) as total_entradas,
               SUM(CASE WHEN tipo = 'saida' THEN valor ELSE 0 END) as total_saidas,
               SUM(CASE WHEN tipo = 'suprimento' THEN valor ELSE 0 END) as total_suprimentos,
               SUM(CASE WHEN tipo = 'sangria' THEN valor ELSE 0 END) as total_sangrias
               FROM movimentacoes_caixa
               WHERE caixa_id = $caixa_id AND pousada_id = $pousada_id";
$result_totais = $conn->query($sql_totais);
$totais = $result_totais->fetch_assoc();

$total_entradas = $totais['total_entradas'] ?? 0;
$total_saidas = $totais['total_saidas'] ?? 0;
$total_suprimentos = $totais['total_suprimentos'] ?? 0;
$total_sangrias = $totais['total_sangrias'] ?? 0;

// Calcular saldo final
$saldo_final = $caixa['saldo_inicial'] + $total_entradas + $total_suprimentos - $total_saidas - $total_sangrias;

// Verificar se há mensagem para exibir
$mensagem = isset($_SESSION['mensagem']) ? $_SESSION['mensagem'] : '';
$tipo_mensagem = isset($_SESSION['tipo_mensagem']) ? $_SESSION['tipo_mensagem'] : 'success';

// Limpar mensagens da sessão
unset($_SESSION['mensagem']);
unset($_SESSION['tipo_mensagem']);
?>

<?php
// Função para calcular o tempo de operação
function calcularTempoOperacao($data_abertura) {
    $abertura = new DateTime($data_abertura);
    $agora = new DateTime();
    $intervalo = $abertura->diff($agora);
    
    if ($intervalo->days > 0) {
        return $intervalo->format('%a dias, %h horas e %i minutos');
    } else {
        return $intervalo->format('%h horas e %i minutos');
    }
}
?>



<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Detalhes do Caixa</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../custom/css/form_fnrh.css">
    <link rel="stylesheet" href="../custom/css/admin-financeiro.css">
</head>
<body>
    <div class="container-fluid px-4 py-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="mb-3">Detalhes do Caixa #<?= $caixa_id ?></h2>
            </div>
            <div class="col-md-6 text-end">
                <a href="index.php?page=caixa_diario" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Voltar
                </a>
                <button onclick="window.print();" class="btn btn-primary">
                    <i class="bi bi-printer"></i> Imprimir
                </button>
            </div>
        </div>
        
        <?php if (!empty($mensagem)): ?>
            <div class="alert alert-<?= $tipo_mensagem ?> alert-dismissible fade show" role="alert">
                <?= $mensagem ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            </div>
        <?php endif; ?>
        
        <!-- Informações do Caixa -->
        <div class="card mb-4">
            <div class="card-header <?= $caixa['status'] == 'aberto' ? 'bg-success text-white' : 'bg-secondary text-white' ?>">
                <i class="bi bi-cash-coin"></i> Informações do Caixa
                <span class="badge <?= $caixa['status'] == 'aberto' ? 'bg-light text-dark' : 'bg-dark' ?> float-end">
                    <?= $caixa['status'] == 'aberto' ? 'Aberto' : 'Fechado' ?>
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Data de Abertura:</strong> <?= date('d/m/Y H:i', strtotime($caixa['data_abertura'])) ?></p>
                        <p><strong>Operador de Abertura:</strong> <?= htmlspecialchars($caixa['usuario_abertura']) ?></p>
                        <p><strong>Saldo Inicial:</strong> R$ <?= number_format($caixa['saldo_inicial'], 2, ',', '.') ?></p>
                        
                        <?php if ($caixa['status'] == 'fechado'): ?>
                            <p><strong>Data de Fechamento:</strong> <?= date('d/m/Y H:i', strtotime($caixa['data_fechamento'])) ?></p>
                            <p><strong>Operador de Fechamento:</strong> <?= htmlspecialchars($caixa['usuario_fechamento']) ?></p>
                            <p><strong>Saldo Final:</strong> R$ <?= number_format($caixa['saldo_final'], 2, ',', '.') ?></p>
                        <?php else: ?>
                            <p><strong>Tempo de Operação:</strong> <?= calcularTempoOperacao($caixa['data_abertura']) ?></p>
                            <p><strong>Saldo Atual:</strong> R$ <?= number_format($saldo_final, 2, ',', '.') ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <?php if (!empty($caixa['observacao'])): ?>
                            <div class="card">
                                <div class="card-header">Observações</div>
                                <div class="card-body">
                                    <p><?= nl2br(htmlspecialchars($caixa['observacao'])) ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Resumo das Movimentações -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title">Entradas</h5>
                        <h3 class="card-text">R$ <?= number_format($total_entradas, 2, ',', '.') ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h5 class="card-title">Saídas</h5>
                        <h3 class="card-text">R$ <?= number_format($total_saidas, 2, ',', '.') ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Suprimentos</h5>
                        <h3 class="card-text">R$ <?= number_format($total_suprimentos, 2, ',', '.') ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body">
                        <h5 class="card-title">Sangrias</h5>
                        <h3 class="card-text">R$ <?= number_format($total_sangrias, 2, ',', '.') ?></h3>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Todas as Movimentações -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-list-ul"></i> Todas as Movimentações
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Tipo</th>
                                <th>Descrição</th>
                                <th>Valor</th>
                                <th>Usuário</th>
                                <th>Lançamento</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($movimentacoes && $movimentacoes->num_rows > 0): ?>
                                <?php while ($mov = $movimentacoes->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= date('d/m/Y H:i', strtotime($mov['data_hora'])) ?></td>
                                        <td>
                                            <?php if ($mov['tipo'] == 'entrada'): ?>
                                                <span class="badge bg-success">Entrada</span>
                                            <?php elseif ($mov['tipo'] == 'saida'): ?>
                                                <span class="badge bg-danger">Saída</span>
                                            <?php elseif ($mov['tipo'] == 'suprimento'): ?>
                                                <span class="badge bg-primary">Suprimento</span>
                                            <?php elseif ($mov['tipo'] == 'sangria'): ?>
                                                <span class="badge bg-warning text-dark">Sangria</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= htmlspecialchars($mov['descricao']) ?></td>
                                        <td class="<?= in_array($mov['tipo'], ['entrada', 'suprimento']) ? 'text-success' : 'text-danger' ?> fw-bold">
                                            R$ <?= number_format($mov['valor'], 2, ',', '.') ?>
                                        </td>
                                        <td><?= htmlspecialchars($mov['usuario_nome']) ?></td>
                                        <td>
                                            <?php if ($mov['lancamento_id']): ?>
                                                <a href="index.php?page=contas&lancamento_id=<?= $mov['lancamento_id'] ?>" class="btn btn-sm btn-info">
                                                    Ver Lançamento
                                                </a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center">Nenhuma movimentação registrada.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Script para o gráfico -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        <?php if (count($labels) > 0): ?>
        // Configuração do gráfico
        const ctx = document.getElementById('graficoMovimentacoes').getContext('2d');
        const graficoMovimentacoes = new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?= json_encode($labels) ?>,
                datasets: [
                    {
                        label: 'Entradas',
                        data: <?= json_encode($valores_entradas) ?>,
                        backgroundColor: 'rgba(40, 167, 69, 0.2)',
                        borderColor: 'rgba(40, 167, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: 'Saídas',
                        data: <?= json_encode($valores_saidas) ?>,
                        backgroundColor: 'rgba(220, 53, 69, 0.2)',
                        borderColor: 'rgba(220, 53, 69, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: 'Saldo Acumulado',
                        data: <?= json_encode($saldo_acumulado) ?>,
                        backgroundColor: 'rgba(0, 123, 255, 0.2)',
                        borderColor: 'rgba(0, 123, 255, 1)',
                        borderWidth: 2,
                        tension: 0.1,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Valores (R$)'
                        }
                    },
                    y1: {
                        position: 'right',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Saldo (R$)'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Movimentações do Caixa'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    });
    </script>

    <style>
    @media print {
        .no-print, .no-print * {
            display: none !important;
        }
        
        .card {
            border: 1px solid #ddd !important;
            margin-bottom: 20px !important;
        }
        
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border-bottom: 1px solid #ddd !important;
        }
        
        .text-white {
            color: #000 !important;
        }
        
        .badge {
            border: 1px solid #000 !important;
            padding: 5px !important;
        }
        
        .bg-success {
            background-color: #f8f9fa !important;
        }
        
        .bg-danger {
            background-color: #f8f9fa !important;
        }
        
        .bg-primary {
            background-color: #f8f9fa !important;
        }
        
        .bg-warning {
            background-color: #f8f9fa !important;
        }
        
        .text-success {
            color: #000 !important;
            font-weight: bold !important;
        }
        
        .text-danger {
            color: #000 !important;
            font-weight: bold !important;
        }
        
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        
        .table th, .table td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }
    }
    </style>

    <script src="js/bootstrap.bundle.min.js"></script>
</body>
</html>