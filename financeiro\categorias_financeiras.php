<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

// Obter categorias financeiras
$sql = "SELECT * FROM categorias_financeiras WHERE pousada_id = $pousada_id ORDER BY tipo, nome";
$categorias = $conn->query($sql);

// Verificar se há mensagem para exibir
$mensagem = isset($_SESSION['mensagem']) ? $_SESSION['mensagem'] : '';
$tipo_mensagem = isset($_SESSION['tipo_mensagem']) ? $_SESSION['tipo_mensagem'] : 'success';

// Limpar mensagens da sessão
unset($_SESSION['mensagem']);
unset($_SESSION['tipo_mensagem']);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Categorias Financeiras</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../custom/css/form_fnrh.css">
    <link rel="stylesheet" href="../custom/css/admin-financeiro.css">

</head>
<body>
    <div class="container-fluid px-4 py-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="mb-3">Categorias Financeiras</h2>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#modalNovaCategoria" data-tipo="receita">
                    <i class="bi bi-plus-circle"></i> Nova Categoria de Receita
                </button>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#modalNovaCategoria" data-tipo="despesa">
                    <i class="bi bi-plus-circle"></i> Nova Categoria de Despesa
                </button>
            </div>
        </div>
        
        <?php if (!empty($mensagem)): ?>
        <div class="alert alert-<?= $tipo_mensagem ?> alert-dismissible fade show" role="alert">
            <?= $mensagem ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
        </div>
        <?php endif; ?>

        <!-- Categorias de Receita -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <i class="bi bi-tag"></i> Categorias de Receita
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Descrição</th>
                                <th>Cor</th>
                                <th>Padrão</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $categorias->data_seek(0);
                            $tem_receitas = false;
                            while ($categoria = $categorias->fetch_assoc()): 
                                if ($categoria['tipo'] == 'receita'):
                                    $tem_receitas = true;
                            ?>
                                <tr>
                                    <td><?= htmlspecialchars($categoria['nome']) ?></td>
                                    <td><?= htmlspecialchars($categoria['descricao'] ?? '') ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?= $categoria['cor'] ?>; width: 30px; height: 30px; display: inline-block;"></span>
                                        <?= $categoria['cor'] ?>
                                    </td>
                                    <td>
                                        <?php if ($categoria['is_default']): ?>
                                            <span class="badge bg-primary">Padrão</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Não</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#modalEditarCategoria"
                                                    data-id="<?= $categoria['id'] ?>"
                                                    data-nome="<?= htmlspecialchars($categoria['nome']) ?>"
                                                    data-tipo="<?= $categoria['tipo'] ?>"
                                                    data-cor="<?= $categoria['cor'] ?>"
                                                    data-descricao="<?= htmlspecialchars($categoria['descricao'] ?? '') ?>"
                                                    data-is-default="<?= $categoria['is_default'] ?>">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <?php if (!$categoria['is_default']): ?>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#modalExcluirCategoria"
                                                        data-id="<?= $categoria['id'] ?>"
                                                        data-nome="<?= htmlspecialchars($categoria['nome']) ?>">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php 
                                endif;
                            endwhile; 
                            
                            if (!$tem_receitas):
                            ?>
                                <tr>
                                    <td colspan="5" class="text-center">Nenhuma categoria de receita cadastrada.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Categorias de Despesa -->
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <i class="bi bi-tag"></i> Categorias de Despesa
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Descrição</th>
                                <th>Cor</th>
                                <th>Padrão</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $categorias->data_seek(0);
                            $tem_despesas = false;
                            while ($categoria = $categorias->fetch_assoc()): 
                                if ($categoria['tipo'] == 'despesa'):
                                    $tem_despesas = true;
                            ?>
                                <tr>
                                    <td><?= htmlspecialchars($categoria['nome']) ?></td>
                                    <td><?= htmlspecialchars($categoria['descricao'] ?? '') ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?= $categoria['cor'] ?>; width: 30px; height: 30px; display: inline-block;"></span>
                                        <?= $categoria['cor'] ?>
                                    </td>
                                    <td>
                                        <?php if ($categoria['is_default']): ?>
                                            <span class="badge bg-primary">Padrão</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Não</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#modalEditarCategoria"
                                                    data-id="<?= $categoria['id'] ?>"
                                                    data-nome="<?= htmlspecialchars($categoria['nome']) ?>"
                                                    data-tipo="<?= $categoria['tipo'] ?>"
                                                    data-cor="<?= $categoria['cor'] ?>"
                                                    data-descricao="<?= htmlspecialchars($categoria['descricao'] ?? '') ?>"
                                                    data-is-default="<?= $categoria['is_default'] ?>">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <?php if (!$categoria['is_default']): ?>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#modalExcluirCategoria"
                                                        data-id="<?= $categoria['id'] ?>"
                                                        data-nome="<?= htmlspecialchars($categoria['nome']) ?>">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php 
                                endif;
                            endwhile; 
                            
                            if (!$tem_despesas):
                            ?>
                                <tr>
                                    <td colspan="5" class="text-center">Nenhuma categoria de despesa cadastrada.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nova Categoria -->
    <div class="modal fade" id="modalNovaCategoria" tabindex="-1" aria-labelledby="modalNovaCategoriaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" id="modalNovaCategoriaHeader">
                    <h5 class="modal-title" id="modalNovaCategoriaLabel">Nova Categoria</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=categorias_financeiras_salvar" method="POST" id="formNovaCategoria">
                        <input type="hidden" name="acao" value="cadastrar">
                        <input type="hidden" name="tipo" id="nova_categoria_tipo" value="receita">
                        
                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="nome" name="nome" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="descricao" class="form-label">Descrição (opcional)</label>
                            <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="cor" class="form-label">Cor</label>
                            <input type="color" class="form-control form-control-color" id="cor" name="cor" value="#000000">
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_default" name="is_default">
                            <label class="form-check-label" for="is_default">Definir como categoria padrão</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formNovaCategoria" class="btn btn-primary">Salvar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Editar Categoria -->
    <div class="modal fade" id="modalEditarCategoria" tabindex="-1" aria-labelledby="modalEditarCategoriaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" id="modalEditarCategoriaHeader">
                    <h5 class="modal-title" id="modalEditarCategoriaLabel">Editar Categoria</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=categorias_financeiras_salvar" method="POST" id="formEditarCategoria">
                        <input type="hidden" name="acao" value="editar">
                        <input type="hidden" name="id" id="editar_id">
                        <input type="hidden" name="tipo" id="editar_tipo">
                        
                        <div class="mb-3">
                            <label for="editar_nome" class="form-label">Nome</label>
                            <input type="text" class="form-control" id="editar_nome" name="nome" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editar_descricao" class="form-label">Descrição (opcional)</label>
                            <textarea class="form-control" id="editar_descricao" name="descricao" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editar_cor" class="form-label">Cor</label>
                            <input type="color" class="form-control form-control-color" id="editar_cor" name="cor" value="#000000">
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="editar_is_default" name="is_default">
                            <label class="form-check-label" for="editar_is_default">Definir como categoria padrão</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formEditarCategoria" class="btn btn-primary">Salvar Alterações</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Excluir Categoria -->
    <div class="modal fade" id="modalExcluirCategoria" tabindex="-1" aria-labelledby="modalExcluirCategoriaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalExcluirCategoriaLabel">Excluir Categoria</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form action="index.php?page=categorias_financeiras_salvar" method="POST" id="formExcluirCategoria">
                        <input type="hidden" name="acao" value="excluir">
                        <input type="hidden" name="id" id="excluir_id">
                        
                        <p>Tem certeza que deseja excluir a categoria <strong id="excluir_nome"></strong>?</p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> Atenção: Esta ação não poderá ser desfeita. 
                            Se houver lançamentos financeiros associados a esta categoria, eles serão mantidos, 
                            mas ficarão sem categoria.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" form="formExcluirCategoria" class="btn btn-danger">Confirmar Exclusão</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.categoria-cor {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #ddd;
}

.color-option {
    margin-right: 10px;
}

.color-box {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px #ccc;
}

/* CORREÇÃO: Aplicar display:none apenas aos inputs de cor, não a todos os checkboxes */
.color-option .form-check-input {
    display: none;
}

.color-option .form-check-input:checked + .color-box {
    box-shadow: 0 0 0 2px #000;
    transform: scale(1.1);
}

/* Garantir que outros checkboxes sejam visíveis */
.form-check-input:not(.color-option .form-check-input) {
    display: inline-block;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configurar modal de edição
    const modalEditarCategoria = document.getElementById('modalEditarCategoria');
    if (modalEditarCategoria) {
        modalEditarCategoria.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const id = button.getAttribute('data-id');
            const nome = button.getAttribute('data-nome');
            const tipo = button.getAttribute('data-tipo');
            const cor = button.getAttribute('data-cor');
            const descricao = button.getAttribute('data-descricao');
            const isDefault = button.getAttribute('data-is-default') === '1';
            
            document.getElementById('editar_id').value = id;
            document.getElementById('editar_nome').value = nome;
            document.getElementById('editar_tipo').value = tipo;
            document.getElementById('editar_cor').value = cor;
            document.getElementById('editar_descricao').value = descricao;
            document.getElementById('editar_is_default').checked = isDefault;
            
            // Desabilitar alteração de tipo se for categoria padrão
            if (isDefault) {
                document.getElementById('editar_tipo').disabled = true;
            } else {
                document.getElementById('editar_tipo').disabled = false;
            }
        });
    }
    
    // Configurar modal de exclusão
    const modalExcluirCategoria = document.getElementById('modalExcluirCategoria');
    if (modalExcluirCategoria) {
        modalExcluirCategoria.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const id = button.getAttribute('data-id');
            const nome = button.getAttribute('data-nome');
            
            document.getElementById('excluir_id').value = id;
            document.getElementById('excluir_nome').textContent = nome;
        });
    }
});
</script>