-- Backup da Pousada ID: 0
-- Data: 2025-07-22 21:35:36
-- Tabelas: hospedes, reservas, usuarios, categorias_financeiras, formas_pagamento, lancamentos_financeiros, caixa_diario, movimentacoes_caixa

SET FOREIGN_KEY_CHECKS = 0;

-- Backup da tabela: hospedes
DELETE FROM hospedes WHERE pousada_id = 0;
-- Nenhum registro encontrado para a tabela hospedes

-- Backup da tabela: reservas
DELETE FROM reservas WHERE pousada_id = 0;
-- Nenhum registro encontrado para a tabela reservas

-- Backup da tabela: usuarios
DELETE FROM usuarios WHERE pousada_id = 0;
INSERT INTO usuarios (id, nome, senha, email, pousada_id, is_admin) VALUES
('33', 'a', '$2y$10$pw1rCZuCQhMnb1tX38EaO.dNDZaEWYkWK6MUa9Bup5q6MxH/JY3jG', '<EMAIL>', '0', '1');

-- Backup da tabela: categorias_financeiras
DELETE FROM categorias_financeiras WHERE pousada_id = 0;
INSERT INTO categorias_financeiras (id, pousada_id, nome, tipo, descricao, cor, is_default) VALUES
('19', '0', 'Hospedagem', 'receita', 'Receita com hospedagem', '#28a745', '1'),
('20', '0', 'Serviços Extras', 'receita', 'Lavanderia, transfer, etc', '#17a2b8', '0'),
('21', '0', 'Alimentação', 'receita', 'Café da manhã, refeições', '#ffc107', '0'),
('22', '0', 'Outras Receitas', 'receita', 'Receitas diversas', '#6f42c1', '0'),
('23', '0', 'Fornecedores', 'despesa', 'Compras de materiais e insumos', '#dc3545', '1'),
('24', '0', 'Funcionários', 'despesa', 'Salários e encargos', '#fd7e14', '0'),
('25', '0', 'Água', 'despesa', 'Conta de água', '#0dcaf0', '0'),
('26', '0', 'Energia Elétrica', 'despesa', 'Conta de luz', '#ffc107', '0'),
('27', '0', 'Internet/Telefone', 'despesa', 'Telecomunicações', '#6610f2', '0'),
('28', '0', 'Manutenção', 'despesa', 'Reparos e manutenção', '#6c757d', '0'),
('29', '0', 'Marketing', 'despesa', 'Publicidade e marketing', '#e83e8c', '0'),
('30', '0', 'Impostos', 'despesa', 'Impostos e taxas', '#dc3545', '0'),
('31', '0', 'Outras Despesas', 'despesa', 'Despesas diversas', '#495057', '0');

-- Backup da tabela: formas_pagamento
DELETE FROM formas_pagamento WHERE pousada_id = 0;
INSERT INTO formas_pagamento (id, pousada_id, nome, descricao, is_active, afeta_caixa) VALUES
('23', '0', 'Dinheiro', 'Pagamento em dinheiro', '1', '1'),
('24', '0', 'PIX', 'Transferência PIX', '1', '0'),
('25', '0', 'Cartão de Débito', 'Pagamento com cartão de débito', '1', '0'),
('26', '0', 'Cartão de Crédito', 'Pagamento com cartão de crédito', '1', '0'),
('27', '0', 'Transferência Bancária', 'TED/DOC', '1', '0'),
('28', '0', 'Cheque', 'Pagamento em cheque', '1', '0'),
('29', '0', 'Aplicativos de Pagamento', 'PayPal, PicPay, MercadoPago, Barte e outros\r\n', '1', '0');

-- Backup da tabela: lancamentos_financeiros
DELETE FROM lancamentos_financeiros WHERE pousada_id = 0;
-- Nenhum registro encontrado para a tabela lancamentos_financeiros

-- Backup da tabela: caixa_diario
DELETE FROM caixa_diario WHERE pousada_id = 0;
-- Nenhum registro encontrado para a tabela caixa_diario

-- Backup da tabela: movimentacoes_caixa
DELETE FROM movimentacoes_caixa WHERE pousada_id = 0;
-- Nenhum registro encontrado para a tabela movimentacoes_caixa

SET FOREIGN_KEY_CHECKS = 1;
