<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");
include_once("func.php");

// Verificar se o usuário está logado e é super administrador
if (!isset($_SESSION['user_id']) || $_SESSION['user_pousada_id'] != 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Não autorizado']);
    exit();
}

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'ID não fornecido']);
    exit();
}

$id = intval($_GET['id']);

// Buscar dados da pousada
$stmt = $conn->prepare("SELECT id, nome, cnpj, rua, numero, complemento, bairro, cidade, estado, cep, pais, telefone, email FROM pousadas WHERE id = ?");
$stmt->bind_param('i', $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Pousada não encontrada']);
    exit();
}

$pousada = $result->fetch_assoc();

// Retornar dados em formato JSON
header('Content-Type: application/json');
echo json_encode($pousada);