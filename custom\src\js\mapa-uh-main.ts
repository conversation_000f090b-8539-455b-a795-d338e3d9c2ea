// Adicionar esta linha no início do arquivo para transformá-lo em um módulo
export {};

// Imports ES6 para os módulos
import { ModalDetalhes } from './modal-detalhes.js';
import { ModalHospede } from './modal-hospede.js';
import { ModalReserva } from './modal-reserva.js';
import { ModalEditarReserva } from './modal-editar-reserva.js';

// Declaração de módulo para estender interfaces globais
// Corrigir as declarações globais (linhas 15-20)
declare global {
  interface Window {
    mapaUHMain: MapaUHMain;
    modalEditarReserva: any;
    salvarHospedeCompleto: (form: HTMLFormElement) => void;
    carregarFormularioCompleto: (uh: string, data: string) => void;
    carregarFormularioReserva: (uh: string, data: string, hospede?: any) => void;
    verificarDisponibilidadeModal: () => void;
    salvarReservaCompleta: (form: HTMLFormElement) => void;
    converterDataParaISO: (dataStr: string) => string | null;
  }
}

// Declaração para o Bootstrap
declare namespace bootstrap {
  class Modal {
    constructor(element: HTMLElement | null, options?: any);
    static getInstance(element: HTMLElement | null): Modal | null;
    show(): void;
    hide(): void;
  }
}

// Interface para o evento customizado
interface HospedeSelectedEvent extends CustomEvent {
  detail: {
    hospede: any;
    uh: string;
    data: string;
  };
}

interface HospedeCadastradoEvent extends CustomEvent {
  detail: {
    hospede: any;
    uh: string;
    data: string;
  };
}

// Arquivo principal para coordenar os módulos do mapa de UH
// Inicialização dos módulos quando o DOM estiver carregado
class MapaUHMain {
  modalDetalhes: ModalDetalhes | null;
  modalHospede: ModalHospede | null;
  modalReserva: ModalReserva | null;
  modalEditarReserva: ModalEditarReserva | null;

  constructor() {
    this.modalDetalhes = null;
    this.modalHospede = null;
    this.modalReserva = null;
    this.modalEditarReserva = null;
    this.init();
  }

  init(): void {
    // Aguardar o DOM estar completamente carregado
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initializeModules());
    } else {
      this.initializeModules();
    }
  }

  initializeModules(): void {
    try {
      // Inicializar os módulos diretamente (sem verificações typeof)
      this.modalDetalhes = new ModalDetalhes();
      this.modalHospede = new ModalHospede();
      this.modalReserva = new ModalReserva();
      this.modalEditarReserva = new ModalEditarReserva();
      
      window.modalEditarReserva = this.modalEditarReserva;

      this.setupModuleCommunication();

      console.log('Módulos do Mapa UH inicializados com sucesso');
    } catch (error) {
      console.error('Erro ao inicializar módulos do Mapa UH:', error);
    }
  }

  setupModuleCommunication(): void {
    // Configurar eventos customizados para comunicação entre módulos

    // Event listener para o botão Nova Reserva
    document.addEventListener('click', (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (target && target.id === 'novaReservaLink') {
        event.preventDefault();
        const uh = target.getAttribute('data-uh');
        const data = target.getAttribute('data-data');

        if (uh && data && this.modalHospede) {
          // Fechar o modal de detalhes
          const reservaModalElement = document.getElementById('reservaModal');
          if (reservaModalElement) {
            const reservaModal = bootstrap.Modal.getInstance(reservaModalElement);
            if (reservaModal) {
              reservaModal.hide();
            }
          }

          // Abrir o modal de seleção de hóspede
          this.modalHospede.abrirModalSelecaoHospede(uh, data);
        }
      }
    });

    // Quando um hóspede for selecionado no modal de hóspede,
    // abrir o modal de reserva
    document.addEventListener('hospedeSelected', ((event: HospedeSelectedEvent) => {
      const { hospede, uh, data } = event.detail;
      if (this.modalReserva) {
        this.modalReserva.carregarFormularioReserva(uh, data, hospede);
      }
    }) as EventListener);

    // Quando uma reserva for salva com sucesso, recarregar a página
    document.addEventListener('reservaSalva', () => {
      location.reload();
    });

    // Quando um hóspede for cadastrado com sucesso,
    // abrir o modal de reserva
    document.addEventListener('hospedeCadastrado', ((event: HospedeCadastradoEvent) => {
      const { hospede, uh, data } = event.detail;
      if (this.modalReserva) {
        this.modalReserva.carregarFormularioReserva(uh, data, hospede);
      }
    }) as EventListener);
  }

  // Métodos públicos para acesso aos módulos
  getModalDetalhes(): ModalDetalhes | null {
    return this.modalDetalhes;
  }

  getModalHospede(): ModalHospede | null {
    return this.modalHospede;
  }

  getModalReserva(): ModalReserva | null {
    return this.modalReserva;
  }
}

// Funções globais para manter compatibilidade com código existente
let mapaUHMain: MapaUHMain;

// Inicializar quando o script for carregado
mapaUHMain = new MapaUHMain();

// Funções globais para compatibilidade
// Corrigir as funções (linhas 166-207)
function salvarHospedeCompleto(form: HTMLFormElement): void {
  if (mapaUHMain && mapaUHMain.modalHospede) {
    return mapaUHMain.modalHospede.salvarHospedeCompleto(form);
  }
  console.error('ModalHospede não inicializado');
}

function carregarFormularioCompleto(uh: string, data: string): void {
  if (mapaUHMain && mapaUHMain.modalHospede) {
    return mapaUHMain.modalHospede.carregarFormularioCompleto(uh, data);
  }
  console.error('ModalHospede não inicializado');
}

function carregarFormularioReserva(uh: string, data: string, hospede: any = null): void {
  if (mapaUHMain && mapaUHMain.modalReserva) {
    return mapaUHMain.modalReserva.carregarFormularioReserva(uh, data, hospede);
  }
  console.error('ModalReserva não inicializado');
}

function verificarDisponibilidadeModal(): void {
  if (mapaUHMain && mapaUHMain.modalReserva) {
    return mapaUHMain.modalReserva.verificarDisponibilidadeModal();
  }
  console.error('ModalReserva não inicializado');
}

function salvarReservaCompleta(form: HTMLFormElement): void {
  if (mapaUHMain && mapaUHMain.modalReserva) {
    return mapaUHMain.modalReserva.salvarReservaCompleta(form);
  }
  console.error('ModalReserva não inicializado');
}

function converterDataParaISO(dataStr: string): string | null {
  if (mapaUHMain && mapaUHMain.modalReserva) {
    return mapaUHMain.modalReserva.converterDataParaISO(dataStr);
  }
  console.error('ModalReserva não inicializado');
  return null;
}

// Exportar para uso global
window.mapaUHMain = mapaUHMain;
window.salvarHospedeCompleto = salvarHospedeCompleto;
window.carregarFormularioCompleto = carregarFormularioCompleto;
window.carregarFormularioReserva = carregarFormularioReserva;
window.verificarDisponibilidadeModal = verificarDisponibilidadeModal;
window.salvarReservaCompleta = salvarReservaCompleta;
window.converterDataParaISO = converterDataParaISO;

export { 
  MapaUHMain,
  salvarHospedeCompleto,
  carregarFormularioCompleto,
  carregarFormularioReserva,
  verificarDisponibilidadeModal,
  salvarReservaCompleta,
  converterDataParaISO
};