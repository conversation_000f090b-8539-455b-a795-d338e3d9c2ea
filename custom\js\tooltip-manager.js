/**
 * Sistema unificado de tooltips para mensagens de validação e conflitos
 * Substitui as implementações anteriores por tooltips Bootstrap consistentes
 */
class TooltipManager {
    constructor() {
        this.DEFAULT_HIDE_DELAY = 5000; // 5 segundos
        this.activeTooltips = new Map();
        this.initializeGlobalStyles();
    }
    /**
     * Inicializa estilos globais para os tooltips customizados
     */
    initializeGlobalStyles() {
        if (!document.getElementById('tooltip-manager-styles')) {
            const style = document.createElement('style');
            style.id = 'tooltip-manager-styles';
            style.textContent = `
                /* Tooltips de erro */
                .tooltip-error .tooltip-inner {
                    background-color: #dc3545;
                    color: #fff;
                    font-weight: 500;
                    max-width: 250px;
                }
                .tooltip-error .tooltip-arrow::before {
                    border-top-color: #dc3545 !important;
                    border-bottom-color: #dc3545 !important;
                }

                /* Tooltips de aviso */
                .tooltip-warning .tooltip-inner {
                    background-color: #ffc107;
                    color: #000;
                    font-weight: 500;
                    max-width: 200px;
                }
                .tooltip-warning .tooltip-arrow::before {
                    border-top-color: #ffc107 !important;
                    border-bottom-color: #ffc107 !important;
                }

                /* Tooltips de conflito */
                .tooltip-conflict .tooltip-inner {
                    background-color: #dc3545;
                    color: #fff;
                    font-weight: 500;
                    max-width: 280px;
                    text-align: left;
                }
                .tooltip-conflict .tooltip-arrow::before {
                    border-top-color: #dc3545 !important;
                    border-bottom-color: #dc3545 !important;
                }

                /* Tooltips informativos */
                .tooltip-info .tooltip-inner {
                    background-color: #0dcaf0;
                    color: #000;
                    font-weight: 500;
                    max-width: 220px;
                }
                .tooltip-info .tooltip-arrow::before {
                    border-top-color: #0dcaf0 !important;
                    border-bottom-color: #0dcaf0 !important;
                }

                /* Estilos para campos com tooltips ativos */
                .field-with-error-tooltip {
                    border-color: #dc3545 !important;
                    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
                }

                .field-with-warning-tooltip {
                    border-color: #ffc107 !important;
                    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
                }

                .field-with-conflict-tooltip {
                    border-color: #dc3545 !important;
                    background-color: rgba(220, 53, 69, 0.05) !important;
                    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
                }
            `;
            document.head.appendChild(style);
        }
    }
    /**
     * Mostra tooltip de conflito de reserva
     */
    showConflictTooltip(element, message) {
        this.showTooltip({
            element,
            message,
            type: 'conflict',
            trigger: 'manual',
            placement: 'bottom',
            autoHide: true,
            hideDelay: 8000
        });
    }
    /**
     * Mostra tooltip de validação de data/hora
     */
    showValidationTooltip(element, message) {
        this.showTooltip({
            element,
            message,
            type: 'error',
            trigger: 'manual',
            placement: 'top',
            autoHide: true,
            hideDelay: 6000
        });
    }
    /**
     * Mostra tooltip de data antiga
     */
    showOldDateTooltip(element) {
        this.showTooltip({
            element,
            message: 'Data Antiga - Esta data é anterior ao momento atual',
            type: 'warning',
            trigger: 'hover',
            placement: 'top',
            autoHide: false
        });
    }
    /**
     * Método principal para mostrar tooltips
     */
    showTooltip(config) {
        // Remover tooltip existente se houver
        this.hideTooltip(config.element);
        // Configurar atributos do elemento
        config.element.setAttribute('data-bs-toggle', 'tooltip');
        config.element.setAttribute('data-bs-placement', config.placement || 'top');
        config.element.setAttribute('data-bs-title', config.message);
        config.element.setAttribute('data-bs-trigger', config.trigger || 'hover');
        config.element.setAttribute('data-bs-html', 'true');
        // Criar instância do tooltip Bootstrap
        const tooltip = new window.bootstrap.Tooltip(config.element, {
            container: 'body',
            customClass: `tooltip-${config.type}`,
            placement: config.placement || 'top',
            trigger: config.trigger || 'hover',
            html: true
        });
        // Aplicar estilo visual ao campo
        this.applyFieldStyle(config.element, config.type);
        // Armazenar instância
        const instance = {
            element: config.element,
            tooltip,
            type: config.type
        };
        this.activeTooltips.set(config.element, instance);
        // Mostrar tooltip se for manual
        if (config.trigger === 'manual') {
            tooltip.show();
        }
        // Configurar auto-hide se necessário
        if (config.autoHide && config.hideDelay) {
            instance.timer = window.setTimeout(() => {
                this.hideTooltip(config.element);
            }, config.hideDelay);
        }
        // Adicionar listener para remover ao clicar fora (apenas para tooltips manuais)
        if (config.trigger === 'manual') {
            this.addClickOutsideListener(config.element);
        }
    }
    /**
     * Esconde tooltip específico
     */
    hideTooltip(element) {
        const instance = this.activeTooltips.get(element);
        if (instance) {
            // Limpar timer se existir
            if (instance.timer) {
                clearTimeout(instance.timer);
            }
            // Destruir tooltip Bootstrap
            instance.tooltip.dispose();
            // Remover atributos
            element.removeAttribute('data-bs-toggle');
            element.removeAttribute('data-bs-placement');
            element.removeAttribute('data-bs-title');
            element.removeAttribute('data-bs-trigger');
            element.removeAttribute('data-bs-html');
            // Remover estilos do campo
            this.removeFieldStyle(element, instance.type);
            // Remover da lista
            this.activeTooltips.delete(element);
        }
    }
    /**
     * Esconde todos os tooltips
     */
    hideAllTooltips() {
        this.activeTooltips.forEach((_, element) => {
            this.hideTooltip(element);
        });
    }
    /**
     * Aplica estilo visual ao campo baseado no tipo
     */
    applyFieldStyle(element, type) {
        // Remover classes anteriores
        element.classList.remove('field-with-error-tooltip', 'field-with-warning-tooltip', 'field-with-conflict-tooltip');
        // Adicionar classe apropriada
        switch (type) {
            case 'error':
                element.classList.add('field-with-error-tooltip');
                break;
            case 'warning':
                element.classList.add('field-with-warning-tooltip');
                break;
            case 'conflict':
                element.classList.add('field-with-conflict-tooltip');
                break;
        }
    }
    /**
     * Remove estilo visual do campo
     */
    removeFieldStyle(element, type) {
        element.classList.remove('field-with-error-tooltip', 'field-with-warning-tooltip', 'field-with-conflict-tooltip');
        element.style.backgroundColor = '';
        element.style.boxShadow = '';
        element.style.borderColor = '';
    }
    /**
     * Adiciona listener para clicar fora
     */
    addClickOutsideListener(element) {
        const clickOutsideHandler = (event) => {
            if (!element.contains(event.target)) {
                this.hideTooltip(element);
                document.removeEventListener('click', clickOutsideHandler);
            }
        };
        // Adicionar listener após um pequeno delay para evitar conflito com o clique atual
        setTimeout(() => {
            document.addEventListener('click', clickOutsideHandler);
        }, 100);
    }
    /**
     * Obtém estatísticas dos tooltips ativos
     */
    getStats() {
        const byType = {};
        this.activeTooltips.forEach(instance => {
            byType[instance.type] = (byType[instance.type] || 0) + 1;
        });
        return {
            total: this.activeTooltips.size,
            byType
        };
    }
}
// Instância global
const tooltipManager = new TooltipManager();
// Exportações
export { TooltipManager, tooltipManager };
