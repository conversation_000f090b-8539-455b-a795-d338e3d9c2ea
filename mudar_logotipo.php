<?php
session_start();
require_once 'config.php';

// Verificar se usuário está logado
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    header("Location: login.php");
    exit();
}

// Função para atualizar o logo no banco de dados
function updateLogoInDatabase($pousadaId, $newLogoPath) {
    global $conn;

    $sql = "UPDATE pousadas SET logotipo = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);

    if ($stmt === false) {
        return false;
    }

    $stmt->bind_param('si', $newLogoPath, $pousadaId);
    $result = $stmt->execute();
    $stmt->close();

    return $result;
}

// Função para redimensionar imagem
function resizeImage($sourcePath, $targetPath, $maxWidth = 300, $maxHeight = 150) {
    // Obter informações da imagem original
    $imageInfo = getimagesize($sourcePath);
    if (!$imageInfo) {
        throw new Exception('Arquivo não é uma imagem válida.');
    }
    
    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $imageType = $imageInfo[2];
    
    // Calcular novas dimensões mantendo proporção
    $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
    $newWidth = round($originalWidth * $ratio);
    $newHeight = round($originalHeight * $ratio);
    
    // Criar imagem original baseada no tipo
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $originalImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $originalImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $originalImage = imagecreatefromgif($sourcePath);
            break;
        case IMAGETYPE_WEBP:
            $originalImage = imagecreatefromwebp($sourcePath);
            break;
        default:
            throw new Exception('Tipo de imagem não suportado.');
    }
    
    // Criar nova imagem redimensionada
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // Preservar transparência para PNG e GIF
    if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
        imagecolortransparent($newImage, imagecolorallocatealpha($newImage, 0, 0, 0, 127));
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
    }
    
    // Redimensionar
    imagecopyresampled($newImage, $originalImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
    
    // Salvar nova imagem (sempre como PNG para manter qualidade)
    $saved = imagepng($newImage, $targetPath, 9);
    
    // Limpar memória
    imagedestroy($originalImage);
    imagedestroy($newImage);
    
    if (!$saved) {
        throw new Exception('Erro ao salvar imagem redimensionada.');
    }
    
    return true;
}

// Função para fazer upload da imagem
function uploadImage($file, $pousadaId) {
    $uploadDir = 'img/logos/';

    // Cria o diretório se não existir
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Validações
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Tipo de arquivo não permitido. Use JPG, PNG, GIF ou WebP.');
    }

    if ($file['size'] > $maxSize) {
        throw new Exception('Arquivo muito grande. Máximo 5MB.');
    }

    // Remove logo antigo da pousada
    $pattern = $uploadDir . 'pousada_' . $pousadaId . '_logo.*';
    foreach (glob($pattern) as $oldFile) {
        if (file_exists($oldFile)) {
            unlink($oldFile);
        }
    }

    // Gera nome com padrão: pousada_{id}_logo.png
    $filename = 'pousada_' . $pousadaId . '_logo.png'; // Sempre PNG após redimensionamento
    $targetPath = $uploadDir . $filename;

    // Redimensiona automaticamente para tamanho de logo padrão
    if (resizeImage($file['tmp_name'], $targetPath, 300, 150)) {
        return $targetPath;
    }

    return false;
}

// Obtém o logo atual da sessão ou padrão
$currentLogo = $_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png';
$pousadaId = $_SESSION['user_pousada_id'];

$message = '';
$messageType = '';

// Processa o upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo'])) {
    try {
        if ($_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $newLogoPath = uploadImage($_FILES['logo'], $pousadaId);

            if ($newLogoPath && updateLogoInDatabase($pousadaId, $newLogoPath)) {
                // Atualizar sessão com novo logo
                $_SESSION['pousada_logo'] = $newLogoPath;
                $currentLogo = $newLogoPath;
                $message = 'Logo atualizado com sucesso!';
                $messageType = 'success';
            } else {
                $message = 'Erro ao salvar o logo.';
                $messageType = 'danger';
            }
        } else {
            $message = 'Erro no upload do arquivo.';
            $messageType = 'danger';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Logo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .logo-container {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .logo-current, .logo-preview {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: white;
            padding: 10px;
        }
        
        .preview-container {
            min-height: 220px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }
        
        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }
        
        .file-input-label {
            cursor: pointer;
            display: inline-block;
            padding: 8px 16px;
            background-color: #0d6efd;
            color: white;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .file-input-label:hover {
            background-color: #0b5ed7;
        }
    </style>
</head>
<body>
    <div class="container mt-2">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-image me-2"></i>
                            Gerenciar Logo do Sistema
                        </h4>
                    </div>

                    <div class="card-body">
                        <div class="logo-container">
                            <div class="preview-container">
                                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                                    <input type="file" id="logo" name="logo" accept="image/*" required style="display: none;">
                                    <div id="preview-area" class="text-muted">
                                        <div onclick="document.getElementById('logo').click();" style="cursor: pointer;">
                                            <i class="fas fa-cloud-upload-alt fa-3x mb-2"></i>
                                            <p>Selecione uma imagem</p>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-success mt-3" id="uploadBtn" disabled>
                                        <i class="fas fa-upload me-2"></i>
                                        Atualizar Logo
                                    </button>
                                </form>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Botão para voltar ao Home -->
                        <div class="text-center">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>
                                Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Espaço no final da página -->
    <div style="height: 50px;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        document.getElementById('logo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const previewArea = document.getElementById('preview-area');
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (file) {
                // Validação do tipo de arquivo
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Tipo de arquivo não permitido. Use JPG, PNG, GIF ou WebP.');
                    e.target.value = '';
                    return;
                }
                
                // Validação do tamanho
                if (file.size > 5 * 1024 * 1024) {
                    alert('Arquivo muito grande. Máximo 5MB.');
                    e.target.value = '';
                    return;
                }
                
                // Cria preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewArea.innerHTML = `
                        <img src="${e.target.result}" alt="Preview" class="logo-preview" style="cursor: pointer;" onclick="document.getElementById('logo').click();">
                        <p class="mt-2 text-muted small">Arquivo: ${file.name}</p>
                    `;
                };
                reader.readAsDataURL(file);

                // Habilita botão
                uploadBtn.disabled = false;
            } else {
                previewArea.innerHTML = `
                    <div onclick="document.getElementById('logo').click();" style="cursor: pointer;">
                        <i class="fas fa-cloud-upload-alt fa-3x mb-2"></i>
                        <p>Selecione uma imagem</p>
                    </div>
                `;
                uploadBtn.disabled = true;
            }
        });

        // Feedback visual no upload
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            const btn = document.querySelector('#uploadBtn');
            if (btn && !btn.disabled) {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';
                btn.disabled = true;
            }
        });

        // Se logo foi atualizado, forçar recarregamento
        <?php if ($messageType === 'success'): ?>
        setTimeout(function() {
            const novoTimestamp = Date.now();

            // Recarregar logo atual na página
            const logoAtual = document.querySelector('img[src*="logo"]');
            if (logoAtual) {
                logoAtual.src = logoAtual.src.split('?')[0] + '?v=' + novoTimestamp;
            }

            // Salvar timestamp no localStorage para outras páginas
            localStorage.setItem('logo_timestamp', novoTimestamp);
        }, 500);
        <?php endif; ?>
    </script>
</body>
</html>