#!/bin/bash
DB_NAME="claudio2_pousada"
CONFIG_FILE="/home/<USER>/.my-pousada.cnf"
BACKUP_DIR="/home/<USER>/pousada.claudiosegura.com/autobackup"
LOG_FILE="$BACKUP_DIR/backup.log"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="backup_${DB_NAME}_${DATE}.sql"
GZ_FILE="${BACKUP_FILE}.gz"
MD5_FILE="backup_${DB_NAME}_${DATE}.md5"

{
    echo "[$DATE] Iniciando backup..."

    # Faz o dump
    mysqldump --defaults-extra-file="$CONFIG_FILE" --no-tablespaces "$DB_NAME" > "$BACKUP_DIR/$BACKUP_FILE"
    DUMP_STATUS=$?

    if [ $DUMP_STATUS -eq 0 ]; then
        echo "[$DATE] Backup realizado com sucesso: $BACKUP_FILE"

        # Gera hash MD5 do arquivo SQL
        md5sum "$BACKUP_DIR/$BACKUP_FILE" > "$BACKUP_DIR/$MD5_FILE"

        # Comprime mantendo o original
        gzip -k "$BACKUP_DIR/$BACKUP_FILE"

        # Verifica integridade da versão compactada
        gunzip -c "$BACKUP_DIR/$GZ_FILE" | md5sum --check --status "$BACKUP_DIR/$MD5_FILE"

        if [ $? -eq 0 ]; then
            echo "[$DATE] Verificação MD5 bem-sucedida. Removendo arquivo original..."
            rm "$BACKUP_DIR/$BACKUP_FILE"
			rm "$BACKUP_DIR/$MD5_FILE"
        else
            echo "[$DATE] ERRO na verificação de integridade! Backup original mantido."
        fi

        # Limpa arquivos antigos com mais de 30 dias
        find "$BACKUP_DIR" -name "backup_${DB_NAME}_*.sql.gz" -mtime +30 -delete
    else
        echo "[$DATE] ERRO ao realizar backup!"
    fi
} >> "$LOG_FILE" 2>&1