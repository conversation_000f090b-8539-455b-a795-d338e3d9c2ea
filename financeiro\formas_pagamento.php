<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

// Obter formas de pagamento - VERIFICAR se inclui o novo campo
$sql = "SELECT id, nome, descricao, afeta_caixa, is_active FROM formas_pagamento WHERE pousada_id = $pousada_id ORDER BY nome";
$formas_pagamento = $conn->query($sql);

// Verificar se há mensagem para exibir
$mensagem = isset($_SESSION['mensagem']) ? $_SESSION['mensagem'] : '';
$tipo_mensagem = isset($_SESSION['tipo_mensagem']) ? $_SESSION['tipo_mensagem'] : 'success';

// Limpar mensagens da sessão
unset($_SESSION['mensagem']);
unset($_SESSION['tipo_mensagem']);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Formas de Pagamento</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../custom/css/form_fnrh.css">
    <link rel="stylesheet" href="../custom/css/admin-financeiro.css">

</head>
<body>
    <div class="container-fluid px-4 py-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="mb-3">Formas de Pagamento</h2>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modalNovaFormaPagamento">
                    <i class="bi bi-plus-circle"></i> Nova Forma de Pagamento
                </button>
            </div>
        </div>
        
        <?php if (!empty($mensagem)): ?>
        <div class="alert alert-<?= $tipo_mensagem ?> alert-dismissible fade show" role="alert">
            <?= $mensagem ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
        </div>
        <?php endif; ?>
        
        <!-- Lista de Formas de Pagamento -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="bi bi-credit-card"></i> Formas de Pagamento Cadastradas
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Descrição</th>
                                <th>Afeta Caixa</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($formas_pagamento && $formas_pagamento->num_rows > 0): ?>
                                <?php while ($forma = $formas_pagamento->fetch_assoc()): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($forma['nome']) ?></td>
                                        <td><?= htmlspecialchars($forma['descricao'] ?? '') ?></td>
                                        <td>
                                            <?php if ($forma['afeta_caixa']): ?>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check-circle"></i> Sim
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">
                                                    <i class="bi bi-x-circle"></i> Não
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($forma['is_active']): ?>
                                                <span class="badge bg-success">Ativo</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inativo</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-primary" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#modalEditarFormaPagamento"
                                                        data-id="<?= $forma['id'] ?>"
                                                        data-nome="<?= htmlspecialchars($forma['nome']) ?>"
                                                        data-descricao="<?= htmlspecialchars($forma['descricao'] ?? '') ?>"
                                                        data-is-active="<?= $forma['is_active'] ?>"
                                                        data-afeta-caixa="<?= $forma['afeta_caixa'] ?>">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#modalExcluirFormaPagamento"
                                                        data-id="<?= $forma['id'] ?>"
                                                        data-nome="<?= htmlspecialchars($forma['nome']) ?>">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center">Nenhuma forma de pagamento cadastrada.</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Modal Nova Forma de Pagamento -->
        <div class="modal fade" id="modalNovaFormaPagamento" tabindex="-1" aria-labelledby="modalNovaFormaPagamentoLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="modalNovaFormaPagamentoLabel">Nova Forma de Pagamento</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <form action="index.php?page=formas_pagamento_salvar" method="POST" id="formNovaFormaPagamento">
                            <input type="hidden" name="acao" value="cadastrar">
                            
                            <div class="mb-3">
                                <label for="nome" class="form-label">Nome</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="descricao" class="form-label">Descrição (opcional)</label>
                                <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="is_active">Ativo</label>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="afeta_caixa" name="afeta_caixa" value="1">
                                <label class="form-check-label" for="afeta_caixa">Afeta Caixa</label>
                                <small class="form-text text-muted">Marque se esta forma de pagamento movimenta o caixa físico</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" form="formNovaFormaPagamento" class="btn btn-primary">Salvar</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Modal Editar Forma de Pagamento -->
        <div class="modal fade" id="modalEditarFormaPagamento" tabindex="-1" aria-labelledby="modalEditarFormaPagamentoLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="modalEditarFormaPagamentoLabel">Editar Forma de Pagamento</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <form action="index.php?page=formas_pagamento_salvar" method="POST" id="formEditarFormaPagamento">
                            <input type="hidden" name="acao" value="editar">
                            <input type="hidden" name="id" id="editar_id">
                            
                            <div class="mb-3">
                                <label for="editar_nome" class="form-label">Nome</label>
                                <input type="text" class="form-control" id="editar_nome" name="nome" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="editar_descricao" class="form-label">Descrição (opcional)</label>
                                <textarea class="form-control" id="editar_descricao" name="descricao" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="editar_is_active" name="is_active" value="1">
                                <label class="form-check-label" for="editar_is_active">Ativo</label>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="editar_afeta_caixa" name="afeta_caixa" value="1">
                                <label class="form-check-label" for="editar_afeta_caixa">Afeta Caixa</label>
                                <small class="form-text text-muted">Marque se esta forma de pagamento movimenta o caixa físico</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" form="formEditarFormaPagamento" class="btn btn-primary">Salvar Alterações</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Modal Excluir Forma de Pagamento -->
        <div class="modal fade" id="modalExcluirFormaPagamento" tabindex="-1" aria-labelledby="modalExcluirFormaPagamentoLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="modalExcluirFormaPagamentoLabel">Excluir Forma de Pagamento</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <form action="index.php?page=formas_pagamento_salvar" method="POST" id="formExcluirFormaPagamento">
                            <input type="hidden" name="acao" value="excluir">
                            <input type="hidden" name="id" id="excluir_id">
                            
                            <p>Tem certeza que deseja excluir a forma de pagamento <strong id="excluir_nome"></strong>?</p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> Atenção: Formas de pagamento já utilizadas em lançamentos serão apenas desativadas, não excluídas.
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" form="formExcluirFormaPagamento" class="btn btn-danger">Confirmar Exclusão</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar modal de edição
            const modalEditarFormaPagamento = document.getElementById('modalEditarFormaPagamento');
            if (modalEditarFormaPagamento) {
                modalEditarFormaPagamento.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const id = button.getAttribute('data-id');
                    const nome = button.getAttribute('data-nome');
                    const descricao = button.getAttribute('data-descricao');
                    const isActive = button.getAttribute('data-is-active') === '1';
                    const afetaCaixa = button.getAttribute('data-afeta-caixa') === '1';
                    
                    document.getElementById('editar_id').value = id;
                    document.getElementById('editar_nome').value = nome;
                    document.getElementById('editar_descricao').value = descricao;
                    document.getElementById('editar_is_active').checked = isActive;
                    document.getElementById('editar_afeta_caixa').checked = afetaCaixa;
                });
            }
            
            // Configurar modal de exclusão
            const modalExcluirFormaPagamento = document.getElementById('modalExcluirFormaPagamento');
            if (modalExcluirFormaPagamento) {
                modalExcluirFormaPagamento.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const id = button.getAttribute('data-id');
                    const nome = button.getAttribute('data-nome');
                    
                    document.getElementById('excluir_id').value = id;
                    document.getElementById('excluir_nome').textContent = nome;
                });
            }
        });
    </script>
</body>
</html>