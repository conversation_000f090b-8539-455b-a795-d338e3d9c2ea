<?php
require_once 'config.php';

/**
 * Função para validar CPF
 * @param string $cpf
 * @return bool
 */
function validarCPF($cpf) {
    // Remove caracteres não numéricos
    $cpf = preg_replace('/[^0-9]/', '', $cpf);
    
    // Verifica se tem 11 dígitos
    if (strlen($cpf) != 11) {
        return false;
    }
    
    // Verifica se todos os dígitos são iguais
    if (preg_match('/(\d)\1{10}/', $cpf)) {
        return false;
    }
    
    // Calcula os dígitos verificadores
    for ($t = 9; $t < 11; $t++) {
        for ($d = 0, $c = 0; $c < $t; $c++) {
            $d += $cpf[$c] * (($t + 1) - $c);
        }
        $d = ((10 * $d) % 11) % 10;
        if ($cpf[$c] != $d) {
            return false;
        }
    }
    
    return true;
}

/**
 * Função para formatar CPF para exibição
 * @param string $cpf
 * @return string
 */
function formatarCPF($cpf) {
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

// Arrays para armazenar os resultados
$hospedes_cpf_incorreto = [];
$hospedes_cpf_repetido = [];
$cpfs_processados = [];

echo "<h2>Script de Limpeza de CPFs - Modo de Teste</h2>\n";
echo "<p><strong>ATENÇÃO:</strong> Este script está em modo de teste. As alterações estão comentadas.</p>\n";
echo "<hr>\n";

try {
    // Busca todos os hóspedes com CPF preenchido
    $sql = "SELECT id, nome, cpf, pousada_id FROM hospedes WHERE cpf IS NOT NULL AND cpf != '' ORDER BY id ASC";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo "<h3>Analisando " . $result->num_rows . " hóspedes com CPF...</h3>\n";
        
        while ($row = $result->fetch_assoc()) {
            $id = $row['id'];
            $nome = $row['nome'];
            $cpf = trim($row['cpf']);
            $pousada_id = $row['pousada_id'];
            
            // Remove caracteres não numéricos para análise
            $cpf_numerico = preg_replace('/[^0-9]/', '', $cpf);
            
            // Verifica se o CPF é válido
            if (!validarCPF($cpf)) {
                $hospedes_cpf_incorreto[] = [
                    'id' => $id,
                    'nome' => $nome,
                    'cpf' => $cpf,
                    'cpf_formatado' => formatarCPF($cpf_numerico),
                    'pousada_id' => $pousada_id,
                    'motivo' => strlen($cpf_numerico) != 11 ? 'CPF com tamanho incorreto' : 'CPF inválido'
                ];
                
                // COMANDO PARA LIMPAR CPF INCORRETO (COMENTADO PARA TESTE)
                $update_sql = "UPDATE hospedes SET cpf = NULL WHERE id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("i", $id);
                $stmt->execute();
                
            } else {
                // CPF válido - verifica se já foi processado (duplicata)
                if (isset($cpfs_processados[$cpf_numerico])) {
                    $hospedes_cpf_repetido[] = [
                        'id' => $id,
                        'nome' => $nome,
                        'cpf' => formatarCPF($cpf_numerico),
                        'pousada_id' => $pousada_id,
                        'primeiro_id' => $cpfs_processados[$cpf_numerico]['id'],
                        'primeiro_nome' => $cpfs_processados[$cpf_numerico]['nome']
                    ];
                    
                    // COMANDO PARA LIMPAR CPF REPETIDO (COMENTADO PARA TESTE)
                    $update_sql = "UPDATE hospedes SET cpf = NULL WHERE id = ?";
                    $stmt = $conn->prepare($update_sql);
                    $stmt->bind_param("i", $id);
                    $stmt->execute();
                    
                } else {
                    // Primeira ocorrência deste CPF - armazena para controle
                    $cpfs_processados[$cpf_numerico] = [
                        'id' => $id,
                        'nome' => $nome
                    ];
                }
            }
        }
    }
    
    // Exibe relatório de CPFs incorretos
    if (!empty($hospedes_cpf_incorreto)) {
        echo "<h3 style='color: red;'>📋 CPFs Incorretos Encontrados (" . count($hospedes_cpf_incorreto) . ")</h3>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<thead style='background-color: #f0f0f0;'>\n";
        echo "<tr><th>ID</th><th>Nome</th><th>CPF Original</th><th>Pousada ID</th><th>Motivo</th></tr>\n";
        echo "</thead>\n";
        echo "<tbody>\n";
        
        foreach ($hospedes_cpf_incorreto as $hospede) {
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($hospede['id']) . "</td>\n";
            echo "<td>" . htmlspecialchars($hospede['nome']) . "</td>\n";
            echo "<td>" . htmlspecialchars($hospede['cpf']) . "</td>\n";
            echo "<td>" . htmlspecialchars($hospede['pousada_id']) . "</td>\n";
            echo "<td style='color: red;'>" . htmlspecialchars($hospede['motivo']) . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</tbody>\n";
        echo "</table>\n";
        echo "<p><strong>Ação:</strong> CPFs incorretos serão limpos (campo definido como NULL).</p>\n";
    } else {
        echo "<h3 style='color: green;'>✅ Nenhum CPF incorreto encontrado!</h3>\n";
    }
    
    echo "<hr>\n";
    
    // Exibe relatório de CPFs repetidos
    if (!empty($hospedes_cpf_repetido)) {
        echo "<h3 style='color: orange;'>📋 CPFs Repetidos Encontrados (" . count($hospedes_cpf_repetido) . ")</h3>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<thead style='background-color: #f0f0f0;'>\n";
        echo "<tr><th>ID</th><th>Nome</th><th>CPF</th><th>Pousada ID</th><th>Primeira Ocorrência</th></tr>\n";
        echo "</thead>\n";
        echo "<tbody>\n";
        
        foreach ($hospedes_cpf_repetido as $hospede) {
            echo "<tr>\n";
            echo "<td>" . htmlspecialchars($hospede['id']) . "</td>\n";
            echo "<td>" . htmlspecialchars($hospede['nome']) . "</td>\n";
            echo "<td>" . htmlspecialchars($hospede['cpf']) . "</td>\n";
            echo "<td>" . htmlspecialchars($hospede['pousada_id']) . "</td>\n";
            echo "<td>ID: " . htmlspecialchars($hospede['primeiro_id']) . " - " . htmlspecialchars($hospede['primeiro_nome']) . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</tbody>\n";
        echo "</table>\n";
        echo "<p><strong>Ação:</strong> CPFs duplicados serão limpos, mantendo apenas a primeira ocorrência.</p>\n";
    } else {
        echo "<h3 style='color: green;'>✅ Nenhum CPF repetido encontrado!</h3>\n";
    }
    
    echo "<hr>\n";
    
    // Resumo final
    $total_afetados = count($hospedes_cpf_incorreto) + count($hospedes_cpf_repetido);
    echo "<h3>📊 Resumo Final</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>CPFs incorretos:</strong> " . count($hospedes_cpf_incorreto) . "</li>\n";
    echo "<li><strong>CPFs repetidos:</strong> " . count($hospedes_cpf_repetido) . "</li>\n";
    echo "<li><strong>Total de registros afetados:</strong> " . $total_afetados . "</li>\n";
    echo "<li><strong>CPFs válidos únicos:</strong> " . count($cpfs_processados) . "</li>\n";
    echo "</ul>\n";
    
    if ($total_afetados > 0) {
        echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0;'>\n";
        echo "<h4>⚠️ Para executar as alterações:</h4>\n";
        echo "<p>1. Descomente as linhas que contêm os comandos UPDATE no código</p>\n";
        echo "<p>2. Execute o script novamente</p>\n";
        echo "<p>3. Faça backup do banco antes de executar!</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Erro durante a execução:</h3>\n";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Fecha a conexão
$conn->close();

echo "<hr>\n";
echo "<p><small>Script executado em: " . date('d/m/Y H:i:s') . "</small></p>\n";
?>