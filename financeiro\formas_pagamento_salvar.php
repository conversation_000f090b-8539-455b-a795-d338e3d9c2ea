<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se o método de requisição é POST
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    $_SESSION['mensagem'] = "Método de requisição inválido.";
    $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
    return;

}

// Obter a ação a ser executada
$acao = $_POST['acao'] ?? '';

// Processar de acordo com a ação
switch ($acao) {
    case 'cadastrar':
        cadastrarFormaPagamento($conn, $pousada_id);
        break;
    case 'editar':
        editarFormaPagamento($conn, $pousada_id);
        break;
    case 'excluir':
        excluirFormaPagamento($conn, $pousada_id);
        break;
    default:
        $_SESSION['mensagem'] = "Ação inválida.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
}

// Função para cadastrar uma nova forma de pagamento
function cadastrarFormaPagamento($conn, $pousada_id) {
    // Obter e validar os dados do formulário
    $nome = $_POST['nome'] ?? '';
    $descricao = $_POST['descricao'] ?? '';
    $afeta_caixa = isset($_POST['afeta_caixa']) ? 1 : 0;
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validar campos obrigatórios
    if (empty($nome)) {
        $_SESSION['mensagem'] = "Nome é um campo obrigatório.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Verificar se já existe uma forma de pagamento com o mesmo nome
    $stmt = $conn->prepare("SELECT id FROM formas_pagamento WHERE pousada_id = ? AND nome = ?");
    $stmt->bind_param("is", $pousada_id, $nome);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $_SESSION['mensagem'] = "Já existe uma forma de pagamento com este nome.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Inserir a nova forma de pagamento no banco de dados
    $stmt = $conn->prepare("INSERT INTO formas_pagamento (pousada_id, nome, descricao, afeta_caixa, is_active) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("issii", $pousada_id, $nome, $descricao, $afeta_caixa, $is_active);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Forma de pagamento cadastrada com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao cadastrar forma de pagamento: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
}

// Função para editar uma forma de pagamento existente
function editarFormaPagamento($conn, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $nome = $_POST['nome'] ?? '';
    $descricao = $_POST['descricao'] ?? '';
    $afeta_caixa = isset($_POST['afeta_caixa']) ? 1 : 0;
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validar ID
    if ($id <= 0) {
        $_SESSION['mensagem'] = "ID da forma de pagamento inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Validar campos obrigatórios
    if (empty($nome)) {
        $_SESSION['mensagem'] = "Nome é um campo obrigatório.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Verificar se a forma de pagamento existe e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM formas_pagamento WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Forma de pagamento não encontrada.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Verificar se já existe outra forma de pagamento com o mesmo nome
    $stmt = $conn->prepare("SELECT id FROM formas_pagamento WHERE pousada_id = ? AND nome = ? AND id != ?");
    $stmt->bind_param("isi", $pousada_id, $nome, $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $_SESSION['mensagem'] = "Já existe outra forma de pagamento com este nome.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Atualizar a forma de pagamento no banco de dados
    $stmt = $conn->prepare("UPDATE formas_pagamento SET nome = ?, descricao = ?, afeta_caixa = ?, is_active = ? WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ssiiii", $nome, $descricao, $afeta_caixa, $is_active, $id, $pousada_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Forma de pagamento atualizada com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao atualizar forma de pagamento: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
    return;
}

// Função para excluir uma forma de pagamento
function excluirFormaPagamento($conn, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    // Validar ID
    if ($id <= 0) {
        $_SESSION['mensagem'] = "ID da forma de pagamento inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Verificar se a forma de pagamento existe e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM formas_pagamento WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Forma de pagamento não encontrada.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Verificar se há lançamentos usando esta forma de pagamento
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM lancamentos_financeiros WHERE pousada_id = ? AND forma_pagamento = (SELECT nome FROM formas_pagamento WHERE id = ?)");
    $stmt->bind_param("ii", $pousada_id, $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['total'] > 0) {
        // Em vez de impedir a exclusão, apenas desativar a forma de pagamento
        $stmt = $conn->prepare("UPDATE formas_pagamento SET is_active = 0 WHERE id = ? AND pousada_id = ?");
        $stmt->bind_param("ii", $id, $pousada_id);
        
        if ($stmt->execute()) {
            $_SESSION['mensagem'] = "Esta forma de pagamento está sendo utilizada em lançamentos. Ela foi desativada em vez de excluída.";
            $_SESSION['tipo_mensagem'] = "warning";
        } else {
            $_SESSION['mensagem'] = "Erro ao desativar forma de pagamento: " . $conn->error;
            $_SESSION['tipo_mensagem'] = "danger";
        }
        
        echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
        return;
    }
    
    // Excluir a forma de pagamento
    $stmt = $conn->prepare("DELETE FROM formas_pagamento WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Forma de pagamento excluída com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao excluir forma de pagamento: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=formas_pagamento';</script>";
    return;
}
?>