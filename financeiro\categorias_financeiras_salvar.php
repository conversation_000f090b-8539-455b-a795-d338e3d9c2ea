<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se o método de requisição é POST
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    $_SESSION['mensagem'] = "Método de requisição inválido.";
    $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
    return;

}

// Obter a ação a ser executada
$acao = $_POST['acao'] ?? '';

// Processar de acordo com a ação
switch ($acao) {
    case 'cadastrar':
        cadastrarCategoria($conn, $pousada_id);
        break;
    case 'editar':
        editarCategoria($conn, $pousada_id);
        break;
    case 'excluir':
        excluirCategoria($conn, $pousada_id);
        break;
    default:
        $_SESSION['mensagem'] = "Ação inválida.";
        $_SESSION['tipo_mensagem'] = "danger";
    echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
    return;
}

// Função para cadastrar uma nova categoria
function cadastrarCategoria($conn, $pousada_id) {
    // Obter e validar os dados do formulário
    $nome = $_POST['nome'] ?? '';
    $tipo = $_POST['tipo'] ?? '';
    $descricao = $_POST['descricao'] ?? '';
    $cor = $_POST['cor'] ?? '#000000';
    $is_default = isset($_POST['is_default']) ? 1 : 0;
    
    // Validar campos obrigatórios
    if (empty($nome) || empty($tipo)) {
        $_SESSION['mensagem'] = "Nome e tipo são campos obrigatórios.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Validar tipo
    if ($tipo != 'receita' && $tipo != 'despesa') {
        $_SESSION['mensagem'] = "Tipo de categoria inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Validar cor
    if (!preg_match('/^#[a-f0-9]{6}$/i', $cor)) {
        $cor = '#000000'; // Cor padrão se inválida
    }
    
    // Se for definida como padrão, remover o status de padrão de outras categorias do mesmo tipo
    if ($is_default) {
        $stmt = $conn->prepare("UPDATE categorias_financeiras SET is_default = 0 WHERE pousada_id = ? AND tipo = ?");
        $stmt->bind_param("is", $pousada_id, $tipo);
        $stmt->execute();
    }
    
    // Inserir a nova categoria no banco de dados
    $stmt = $conn->prepare("INSERT INTO categorias_financeiras (pousada_id, nome, tipo, descricao, cor, is_default) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("issssi", $pousada_id, $nome, $tipo, $descricao, $cor, $is_default);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Categoria cadastrada com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao cadastrar categoria: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
    return;
}

// Função para editar uma categoria existente
function editarCategoria($conn, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $nome = $_POST['nome'] ?? '';
    $tipo = $_POST['tipo'] ?? '';
    $descricao = $_POST['descricao'] ?? '';
    $cor = $_POST['cor'] ?? '#000000';
    $is_default = isset($_POST['is_default']) ? 1 : 0;
    
    // Validar ID
    if ($id <= 0) {
        $_SESSION['mensagem'] = "ID da categoria inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Validar campos obrigatórios
    if (empty($nome) || empty($tipo)) {
        $_SESSION['mensagem'] = "Nome e tipo são campos obrigatórios.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Validar tipo
    if ($tipo != 'receita' && $tipo != 'despesa') {
        $_SESSION['mensagem'] = "Tipo de categoria inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Validar cor
    if (!preg_match('/^#[a-f0-9]{6}$/i', $cor)) {
        $cor = '#000000'; // Cor padrão se inválida
    }
    
    // Verificar se a categoria existe e pertence à pousada
    $stmt = $conn->prepare("SELECT * FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Categoria não encontrada.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    $categoria = $result->fetch_assoc();
    
    // Se for definida como padrão, remover o status de padrão de outras categorias do mesmo tipo
    if ($is_default) {
        $stmt = $conn->prepare("UPDATE categorias_financeiras SET is_default = 0 WHERE pousada_id = ? AND tipo = ?");
        $stmt->bind_param("is", $pousada_id, $tipo);
        $stmt->execute();
    }
    
    // Atualizar a categoria no banco de dados
    $stmt = $conn->prepare("UPDATE categorias_financeiras SET nome = ?, descricao = ?, cor = ?, is_default = ? WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("sssiii", $nome, $descricao, $cor, $is_default, $id, $pousada_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Categoria atualizada com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao atualizar categoria: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
    return;
}

// Função para excluir uma categoria
function excluirCategoria($conn, $pousada_id) {
    // Obter e validar os dados do formulário
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    
    // Validar ID
    if ($id <= 0) {
        $_SESSION['mensagem'] = "ID da categoria inválido.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Verificar se a categoria existe, pertence à pousada e não é padrão
    $stmt = $conn->prepare("SELECT * FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows == 0) {
        $_SESSION['mensagem'] = "Categoria não encontrada.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    $categoria = $result->fetch_assoc();
    
    if ($categoria['is_default']) {
        $_SESSION['mensagem'] = "Não é possível excluir uma categoria padrão.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Verificar se há lançamentos associados a esta categoria
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM lancamentos_financeiros WHERE categoria_id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['total'] > 0) {
        $_SESSION['mensagem'] = "Não é possível excluir esta categoria pois existem lançamentos associados a ela.";
        $_SESSION['tipo_mensagem'] = "danger";
        echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
        return;
    }
    
    // Excluir a categoria
    $stmt = $conn->prepare("DELETE FROM categorias_financeiras WHERE id = ? AND pousada_id = ?");
    $stmt->bind_param("ii", $id, $pousada_id);
    
    if ($stmt->execute()) {
        $_SESSION['mensagem'] = "Categoria excluída com sucesso.";
        $_SESSION['tipo_mensagem'] = "success";
    } else {
        $_SESSION['mensagem'] = "Erro ao excluir categoria: " . $conn->error;
        $_SESSION['tipo_mensagem'] = "danger";
    }
    
    echo "<script>window.location.href = 'index.php?page=categorias_financeiras';</script>";
    return;
}
?>