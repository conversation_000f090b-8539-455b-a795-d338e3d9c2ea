<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    // Redireciona para o login se não estiver logado
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

include_once("config.php");

// Verificar se o ID foi fornecido
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id <= 0) {
    echo '<div class="alert alert-danger">ID do lançamento inválido.</div>';
    exit();
}

// Buscar dados do lançamento
$stmt = $conn->prepare("SELECT l.*, c.nome as categoria_nome, c.tipo as categoria_tipo 
                       FROM lancamentos_financeiros l 
                       LEFT JOIN categorias_financeiras c ON l.categoria_id = c.id 
                       WHERE l.id = ? AND l.pousada_id = ?");
$stmt->bind_param("ii", $id, $pousada_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo '<div class="alert alert-danger">Lançamento não encontrado.</div>';
    exit();
}

$lancamento = $result->fetch_assoc();

// Buscar categorias do mesmo tipo do lançamento
$tipo = $lancamento['tipo'];
$stmt_categorias = $conn->prepare("SELECT id, nome FROM categorias_financeiras 
                                  WHERE pousada_id = ? AND tipo = ? 
                                  ORDER BY nome");
$stmt_categorias->bind_param("is", $pousada_id, $tipo);
$stmt_categorias->execute();
$categorias = $stmt_categorias->get_result();

// Buscar formas de pagamento
$stmt_formas = $conn->prepare("SELECT id, nome FROM formas_pagamento 
                              WHERE pousada_id = ? AND is_active = 1 
                              ORDER BY nome");
$stmt_formas->bind_param("i", $pousada_id);
$stmt_formas->execute();
$formas_pagamento = $stmt_formas->get_result();
?>

<input type="hidden" name="acao" value="editar">
<input type="hidden" name="id" value="<?php echo $lancamento['id']; ?>">
<input type="hidden" name="tipo" value="<?php echo $lancamento['tipo']; ?>">

<div class="row">
    <div class="col-md-8">
        <div class="mb-3">
            <label for="edit_descricao" class="form-label">Descrição*</label>
            <input type="text" class="form-control" id="edit_descricao" name="descricao" 
                   value="<?php echo htmlspecialchars($lancamento['descricao']); ?>" required>
        </div>
    </div>
    <div class="col-md-4">
        <div class="mb-3">
            <label for="edit_valor" class="form-label">Valor*</label>
            <div class="input-group">
                <span class="input-group-text">R$</span>
                <input type="number" class="form-control" id="edit_valor" name="valor" 
                       step="0.01" min="0.01" value="<?php echo $lancamento['valor']; ?>" required>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_categoria_id" class="form-label">Categoria*</label>
            <select class="form-select" id="edit_categoria_id" name="categoria_id" required>
                <?php while ($categoria = $categorias->fetch_assoc()): ?>
                    <option value="<?php echo $categoria['id']; ?>" 
                            <?php echo ($categoria['id'] == $lancamento['categoria_id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($categoria['nome']); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
    </div>
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_data_vencimento" class="form-label">Data de Vencimento*</label>
            <input type="date" class="form-control" id="edit_data_vencimento" name="data_vencimento" 
                   value="<?php echo $lancamento['data_vencimento']; ?>" required>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_status" class="form-label">Status*</label>
            <select class="form-select" id="edit_status" name="status" required>
                <option value="pendente" <?php echo ($lancamento['status'] == 'pendente') ? 'selected' : ''; ?>>Pendente</option>
                <option value="pago" <?php echo ($lancamento['status'] == 'pago') ? 'selected' : ''; ?>>Pago</option>
                <option value="cancelado" <?php echo ($lancamento['status'] == 'cancelado') ? 'selected' : ''; ?>>Cancelado</option>
            </select>
        </div>
    </div>
    <div class="col-md-6 div-edit-data-pagamento" style="<?php echo ($lancamento['status'] != 'pago') ? 'display: none;' : ''; ?>">
        <div class="mb-3">
            <label for="edit_data_pagamento" class="form-label">Data de Pagamento*</label>
            <input type="date" class="form-control" id="edit_data_pagamento" name="data_pagamento" 
                   value="<?php echo $lancamento['data_pagamento'] ?? date('Y-m-d'); ?>">
        </div>
    </div>
</div>

<div class="row div-edit-forma-pagamento" style="<?php echo ($lancamento['status'] != 'pago') ? 'display: none;' : ''; ?>">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="edit_forma_pagamento" class="form-label">Forma de Pagamento*</label>
            <select class="form-select" id="edit_forma_pagamento" name="forma_pagamento">
                <option value="">Selecione</option>
                <?php 
                while ($forma = $formas_pagamento->fetch_assoc()): 
                    $selected = ($forma['nome'] == $lancamento['forma_pagamento']) ? 'selected' : '';
                ?>
                    <option value="<?php echo htmlspecialchars($forma['nome']); ?>" <?php echo $selected; ?>>
                        <?php echo htmlspecialchars($forma['nome']); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
    </div>
</div>

<div class="mb-3">
    <label for="edit_observacao" class="form-label">Observação</label>
    <textarea class="form-control" id="edit_observacao" name="observacao" rows="3"><?php echo htmlspecialchars($lancamento['observacao'] ?? ''); ?></textarea>
</div>