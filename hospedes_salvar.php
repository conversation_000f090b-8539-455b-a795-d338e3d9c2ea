<?php
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

switch ($_REQUEST["acao"]) {
    case 'cadastrar':
        $nome = $_POST["nome"];

        case 'cadastrar':
            $nome = $_POST["nome"];
            
            $cpf = is_null($_POST["cpf"]) ? '' : preg_replace('/[^0-9]/', '', $_POST["cpf"]);
            
            // Verificar se CPF já existe
            if (!empty($cpf)) {
                $sql_check = "SELECT nome FROM hospedes WHERE cpf = ? AND pousada_id = ?";
                $stmt_check = $conn->prepare($sql_check);
                $stmt_check->bind_param("si", $cpf, $pousada_id);
                $stmt_check->execute();
                $result_check = $stmt_check->get_result();
                
                if ($result_check->num_rows > 0) {
                    $hospede_existente = $result_check->fetch_assoc();
                    
                    if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false, 
                            'message' => 'CPF já cadastrado para: ' . $hospede_existente['nome']
                        ]);
                        exit;
                    } else {
                        // Preservar dados do POST para reexibir no formulário
                        $_SESSION['form_data'] = $_POST;
                        $_SESSION['error_message'] = 'CPF já cadastrado para: ' . $hospede_existente['nome'];
                        echo "<script>window.location.href = 'hospedes_novo.php?erro=cpf_duplicado';</script>";
                        exit;
                    }
                }
                $stmt_check->close();
            }
        
            $sql = "INSERT INTO hospedes (nome, nasc, idade, profissao, nacionalidade, sexo, cpf, documento, tipo, expedidor, endereco, telefone, cep, cidade, uf, pais, email, pousada_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        $sql = "INSERT INTO hospedes (nome, nasc, idade, profissao, nacionalidade, sexo, cpf, documento, tipo, expedidor, endereco, telefone, cep, cidade, uf, pais, email, pousada_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            // Se for AJAX, retornar JSON
            if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Erro ao preparar consulta: ' . $conn->error]);
                exit;
            }
            die('Erro ao preparar a consulta: ' . $conn->error);
        }
        
        $stmt->bind_param("ssissssssssssssssi", 
            $_POST["nome"],
            $_POST["nasc"],
            $_POST["idade"],
            $_POST["profissao"],
            $_POST["nacionalidade"],
            $_POST["sexo"],
            $cpf,
            $_POST["documento"],
            $_POST["tipo"],
            $_POST["expedidor"],
            $_POST["endereco"],
            $_POST["telefone"],
            $_POST["cep"],
            $_POST["cidade"],
            $_POST["uf"],
            $_POST["pais"],
            $_POST["email"],
            $pousada_id
        );

        if ($stmt->execute()) {
            $hospede_id = $conn->insert_id;
            
            // DETECTAR SE É REQUISIÇÃO AJAX
            if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
                // RESPOSTA PARA MAPA_UH (AJAX)
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'hospede_id' => $hospede_id,
                    'hospede_nome' => $nome,
                    'uh' => $_POST["uh_reserva"] ?? '',
                    'data_reserva' => $_POST["data_reserva"] ?? ''
                ]);
                exit;
            } else {
                // RESPOSTA PARA HOSPEDES_NOVO (REDIRECIONAMENTO)
                print "<script>location.href='index.php?page=listar&tipo_pesquisa=nome&termo_pesquisa=" . urlencode($nome) . "';</script>";
            }
        } else {
            if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
                // ERRO AJAX
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Não foi possível cadastrar']);
                exit;
            } else {
                // ERRO NORMAL
                echo "<script>alert('Não foi possível cadastrar');</script>";
            }
        }
        
        $stmt->close();
        break;

    case 'editar':
        $nome = $_POST["nome"];
        $sql = "UPDATE hospedes SET nome=?, nasc=?, idade=?, profissao=?, nacionalidade=?, sexo=?, cpf=?, documento=?, tipo=?, expedidor=?, endereco=?, telefone=?, cep=?, cidade=?, uf=?, pais=?, email=?, pousada_id=? WHERE id=?";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            die('Erro ao preparar a consulta: ' . $conn->error);
        }

        $cpf = is_null($_POST["cpf"]) ? '' : preg_replace('/[^0-9]/', '', $_POST["cpf"]);
        //$cpf = preg_replace('/[^0-9]/', '', $_POST["cpf"]);

        $stmt->bind_param("ssissssssssssssssii", 
            $_POST["nome"],
            $_POST["nasc"],
            $_POST["idade"],
            $_POST["profissao"],
            $_POST["nacionalidade"],
            $_POST["sexo"],
            $cpf,
            $_POST["documento"],
            $_POST["tipo"],
            $_POST["expedidor"],
            $_POST["endereco"],
            $_POST["telefone"],
            $_POST["cep"],
            $_POST["cidade"],
            $_POST["uf"],
            $_POST["pais"],
            $_POST["email"],
            $pousada_id, // Usando o ID da pousada da sessão
            $_POST["id"] // ID do hospede a ser atualizado
        );

        if (!$stmt->execute()) {
            echo "<script>alert('Não foi possível editar');</script>";
        }
		
		
        print "<script>location.href='index.php?page=listar&tipo_pesquisa=nome&termo_pesquisa=" . urlencode($nome) . "';</script>";
        
        $stmt->close(); // Libera recursos
        break;

    case 'excluir':
        
        $nome = $_GET["hospede_nome"];
        $hospede_id = $_GET["hospede_id"];
    
        // Delete dependent records from reservas table
        $sql = "DELETE FROM reservas WHERE hospede_id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $hospede_id);//vem pelo metodo GET
        $stmt->execute();
        $stmt->close(); // Libera recursos
        
        // Now delete the record from hospedes table
        $sql = "DELETE FROM hospedes WHERE id=?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_Param("i", $hospede_id);

        if ($stmt->execute()) {
            echo "<script>alert('Hospede excluído com sucesso');</script>";
        } else {
            echo "<script>alert('Não foi possível excluir o hospede.');</script>";
        }

        print "<script>location.href='index.php?page=listar';</script>";

        $stmt->close(); // Libera recursos
        break;
        
        
        
}//switch
?>
