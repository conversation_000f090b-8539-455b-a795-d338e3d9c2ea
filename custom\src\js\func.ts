// Adicionar esta linha no início do arquivo para transformá-lo em um módulo
export {};

// Interfaces para as respostas da API
interface CPFResponse {
  existe: boolean;
  nome_hospede?: string;
}

interface DisponibilidadeResponse {
  disponivel: boolean;
  mensagem?: string;
  erro?: string;
}

// Calcular idade
const nascElement: HTMLInputElement | null = document.getElementsByName('nasc')[0] as HTMLInputElement;
if (nascElement) {
    nascElement.addEventListener('change', function(this: HTMLInputElement) {
        const dataNascimento: Date = new Date(this.value);
        const hoje: Date = new Date();
        let idade: number = hoje.getFullYear() - dataNascimento.getFullYear();
        const mes: number = hoje.getMonth() - dataNascimento.getMonth();
        if (mes < 0 || (mes === 0 && hoje.getDate() < dataNascimento.getDate())) {
            idade--;
        }
        const idadeInput = document.getElementsByName('idade')[0] as HTMLInputElement;
        if (idadeInput) {
            idadeInput.value = idade.toString();
        }
    });
}

// Formatar CPF e validar
const cpfElement: HTMLInputElement | null = document.getElementsByName('cpf')[0] as HTMLInputElement;
if (cpfElement) {
    cpfElement.addEventListener('input', function(this: HTMLInputElement) {
        formatarEValidarCPF(this);
    });

    // Verificação completa ao sair do campo
    cpfElement.addEventListener('blur', function(this: HTMLInputElement) {
        const cpf: string = this.value.replace(/\D/g, '');

        // Verificar tamanho
        if (cpf.length > 0 && cpf.length < 11) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            this.title = 'CPF incompleto. Digite todos os 11 dígitos.';

            // Mostrar mensagem de CPF inválido para CPF incompleto
            const feedbackDiv: HTMLElement | null = document.getElementById('cpf-feedback');
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
            }
            return;
        } else if (cpf.length > 11) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            this.title = 'CPF com muitos dígitos. Deve ter apenas 11 dígitos.';

            // Mostrar mensagem de CPF inválido para CPF com muitos dígitos
            const feedbackDiv: HTMLElement | null = document.getElementById('cpf-feedback');
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
            }
            return;
        }

        // Verificar se CPF já existe (apenas se tiver 11 dígitos E for válido)
        if (cpf.length === 11 && validarCPF(this.value)) {
            verificarCPFDuplicado(cpf, this);
        }
    });
}

// Nova função para verificar CPF duplicado com controle do botão
function verificarCPFDuplicado(cpf: string, inputElement: HTMLInputElement): void {
    // Detectar contexto baseado no título da página
    const tituloElement: HTMLElement | null = document.querySelector('h3');
    const isEdicao: boolean = tituloElement ? tituloElement.textContent?.includes('Editar Hospede') || false : false;
    const submitButton: HTMLButtonElement | null = document.querySelector('button[type="submit"]');
    const feedbackDiv: HTMLElement | null = document.getElementById('cpf-feedback');

    console.log('Contexto detectado:', isEdicao ? 'Edição' : 'Novo cadastro');
    console.log('Título encontrado:', tituloElement ? tituloElement.textContent : 'Nenhum');

    // Só verificar duplicidade se CPF for válido e tiver 11 dígitos
    if (cpf.length < 11) {
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'CPF Incompleto';
            submitButton.classList.add('btn-secondary');
            submitButton.classList.remove('btn-primary');
        }
        return;
    }

    if (!validarCPF(cpf)) {
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'CPF Inválido';
            submitButton.classList.add('btn-secondary');
            submitButton.classList.remove('btn-primary');
        }
        return;
    }

    // Construir URL da requisição
    let url: string = 'verificar_cpf.php?cpf=' + encodeURIComponent(cpf);

    console.log('URL da requisição:', url);

    // Se estiver na tela de edição, adicionar o hospede_id para excluir da verificação
    const formElement: HTMLFormElement | null = inputElement.closest('form');
    console.log('Form element:', formElement);
    console.log('Form ID:', formElement ? formElement.id : 'não encontrado');

    if (formElement && formElement.id === 'editarHospede') {
        const hospedeIdInput: HTMLInputElement | null = formElement.querySelector('input[name="id"]');
        console.log('Hospede ID input:', hospedeIdInput);
        console.log('Hospede ID value:', hospedeIdInput ? hospedeIdInput.value : 'não encontrado');

        if (hospedeIdInput && hospedeIdInput.value) {
            url += '&hospede_id=' + encodeURIComponent(hospedeIdInput.value);
        }
    }

    console.log('URL final:', url);

    fetch(url)
        .then(response => response.json())
        .then((data: CPFResponse) => {
            console.log('Resposta do servidor:', data);

            if (data.existe) {
                // CPF duplicado - desabilitar botão e mostrar erro
                inputElement.classList.remove('is-valid');
                inputElement.classList.add('is-invalid');
                inputElement.title = 'CPF já cadastrado para: ' + (data.nome_hospede || '');

                if (feedbackDiv) {
                    feedbackDiv.innerHTML = '<b>CPF já cadastrado para: ' + (data.nome_hospede || '') + '</b>';
                    feedbackDiv.style.color = 'red';
                }

                if (submitButton) {
                    submitButton.disabled = true;
                    submitButton.textContent = 'CPF Duplicado - Não é possível registrar';
                    submitButton.classList.add('btn-danger');
                    submitButton.classList.remove('btn-primary', 'btn-secondary');
                }
            } else {
                // CPF disponível - habilitar botão
                inputElement.classList.remove('is-invalid');
                inputElement.classList.add('is-valid');
                inputElement.title = '';

                if (feedbackDiv) {
                    feedbackDiv.innerHTML = '<b>CPF disponível</b>';
                    feedbackDiv.style.color = 'green';
                }

                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Registrar';
                    submitButton.classList.add('btn-primary');
                    submitButton.classList.remove('btn-danger', 'btn-secondary');
                }
            }
        })
        .catch(error => {
            console.error('Erro ao verificar CPF:', error);
            // Em caso de erro, permitir o envio (fallback)
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Registrar';
                submitButton.classList.add('btn-primary');
                submitButton.classList.remove('btn-danger', 'btn-secondary');
            }
        });
}

// Função para formatar e validar CPF
function formatarEValidarCPF(input: HTMLInputElement): void {
    let cpf: string = input.value.replace(/\D/g, ''); // Remove caracteres não numéricos
    if (cpf.length <= 11) {
        cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
        cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
        cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
    }
    input.value = cpf;

    const feedbackDiv: HTMLElement | null = document.getElementById('cpf-feedback');
    const submitButton: HTMLButtonElement | null = document.querySelector('button[type="submit"]');

    if (cpf.length === 14) { // Verifica se o CPF está completo
        if (validarCPF(cpf)) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            input.title = '';

            // CPF válido, mas ainda precisa verificar duplicidade
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>Verificando disponibilidade...</b>';
                feedbackDiv.style.color = 'orange';
            }
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            input.title = 'CPF inválido. Verifique a digitação.';

            // CPF inválido - desabilitar botão
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                feedbackDiv.style.color = 'red';
            }

            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'CPF Inválido';
                submitButton.classList.add('btn-secondary');
                submitButton.classList.remove('btn-primary', 'btn-danger');
            }
        }
    } else {
        input.classList.remove('is-valid', 'is-invalid');

        // Campo vazio ou incompleto
        if (cpf.length === 0) {
            // Campo vazio - habilitar botão e limpar feedback
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '';
            }

            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Registrar';
                submitButton.classList.add('btn-primary');
                submitButton.classList.remove('btn-danger', 'btn-secondary');
            }
        } else {
            // Campo incompleto - desabilitar botão
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF incompleto</b>.';
                feedbackDiv.style.color = 'orange';
            }

            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'CPF Incompleto';
                submitButton.classList.add('btn-secondary');
                submitButton.classList.remove('btn-primary', 'btn-danger');
            }
        }
    }
}

// Validar CPF
function validarCPF(cpf: string): boolean {
    cpf = cpf.replace(/[^\d]+/g, '');
    if (cpf === '') return false;
    // Elimina CPFs invalidos conhecidos
    if (cpf.length !== 11 ||
        cpf === "00000000000" ||
        cpf === "11111111111" ||
        cpf === "22222222222" ||
        cpf === "33333333333" ||
        cpf === "44444444444" ||
        cpf === "55555555555" ||
        cpf === "66666666666" ||
        cpf === "77777777777" ||
        cpf === "88888888888" ||
        cpf === "99999999999")
        return false;
    // Valida 1o digito
    let add: number = 0;
    for (let i = 0; i < 9; i++)
        add += parseInt(cpf.charAt(i)) * (10 - i);
    let rev: number = 11 - (add % 11);
    if (rev === 10 || rev === 11)
        rev = 0;
    if (rev !== parseInt(cpf.charAt(9)))
        return false;
    // Valida 2o digito
    add = 0;
    for (let i = 0; i < 10; i++)
        add += parseInt(cpf.charAt(i)) * (11 - i);
    rev = 11 - (add % 11);
    if (rev === 10 || rev === 11)
        rev = 0;
    if (rev !== parseInt(cpf.charAt(10)))
        return false;
    return true;
}

// Formatar CPF ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    const cpfInput: HTMLInputElement | null = document.getElementsByName('cpf')[0] as HTMLInputElement;
    if (cpfInput && cpfInput.value) {
        formatarEValidarCPF(cpfInput);
    }
});

const printButton: HTMLElement | null = document.getElementById('print-button');
if (printButton) {
    printButton.addEventListener('click', function() {
        window.print();
    });
}

// Função para fazer Enter funcionar como Tab nos formulários
function setupEnterAsTab(): void {
    // Seleciona todos os inputs, selects e textareas dos formulários
    const formElements: NodeListOf<HTMLElement> = document.querySelectorAll('form input, form select, form textarea');

    formElements.forEach(function(element) {
        element.addEventListener('keydown', function(event: KeyboardEvent) {
            // Verifica se a tecla pressionada é Enter (código 13)
            if (event.key === 'Enter' || event.keyCode === 13) {
                // Previne o comportamento padrão do Enter
                event.preventDefault();

                // Encontra todos os elementos focáveis do formulário atual
                const form: HTMLFormElement | null = (element as HTMLElement).closest('form');
                if (!form) return;
                
                const focusableElements: NodeListOf<HTMLElement> = form.querySelectorAll(
                    'input:not([type="hidden"]):not([disabled]):not([readonly]), ' +
                    'select:not([disabled]), ' +
                    'textarea:not([disabled]):not([readonly]), ' +
                    'button:not([disabled])'
                );

                // Converte NodeList para Array para usar indexOf
                const focusableArray: HTMLElement[] = Array.from(focusableElements);
                const currentIndex: number = focusableArray.indexOf(element as HTMLElement);

                // Move para o próximo elemento focável
                if (currentIndex > -1 && currentIndex < focusableArray.length - 1) {
                    const nextElement: HTMLElement = focusableArray[currentIndex + 1];
                    nextElement.focus();

                    // Se for um input de texto, seleciona todo o conteúdo
                    if ((nextElement as HTMLInputElement).type === 'text' || 
                        (nextElement as HTMLInputElement).type === 'email' ||
                        (nextElement as HTMLInputElement).type === 'tel' || 
                        nextElement.tagName === 'TEXTAREA') {
                        (nextElement as HTMLInputElement).select();
                    }
                } else if (currentIndex === focusableArray.length - 1) {
                    // Se estiver no último campo, pode submeter o formulário ou voltar ao primeiro
                    // Descomente a linha abaixo se quiser submeter automaticamente:
                    // form.submit();

                    // Ou voltar ao primeiro campo:
                    if (focusableArray.length > 0) {
                        focusableArray[0].focus();
                    }
                }
            }
        });
    });
}

// Executa a função quando o DOM estiver carregado
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupEnterAsTab);
} else {
    setupEnterAsTab();
}

// Também executa quando novos formulários são carregados via AJAX
// (útil para formulários carregados dinamicamente)
function reinitializeEnterAsTab(): void {
    setupEnterAsTab();
}

// FUNÇÃO REMOVIDA: verificarDisponibilidadeUH
// A nova implementação está em verificacao-disponibilidade-local.ts

// Função para mostrar aviso de conflito de reserva usando popover
function mostrarAvisoConflito(elemento: HTMLElement, mensagem: string): void {
    // Remove avisos anteriores
    const avisosAnteriores: NodeListOf<Element> = document.querySelectorAll('.aviso-conflito');
    avisosAnteriores.forEach(aviso => aviso.remove());

    // Criar popover para aviso de conflito
    elemento.setAttribute('data-bs-toggle', 'popover');
    elemento.setAttribute('data-bs-placement', 'bottom');
    elemento.setAttribute('data-bs-content', mensagem);
    elemento.setAttribute('data-bs-trigger', 'manual');
    elemento.setAttribute('data-bs-html', 'true');
    elemento.classList.add('aviso-conflito');
    
    // Configurar popover Bootstrap
    const popover = new (window as any).bootstrap.Popover(elemento, {
        container: 'body',
        customClass: 'popover-conflito',
        placement: 'bottom'
    });
    
    // Mostrar popover
    popover.show();
    
    // Destacar o campo de forma mais sutil
    elemento.classList.add('border-danger');
    elemento.style.backgroundColor = 'rgba(220, 53, 69, 0.05)';
    elemento.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
    
    // Auto-remover após 8 segundos
    setTimeout(() => {
        popover.hide();
        removerAvisoConflito(elemento);
    }, 8000);
    
    // Remover ao clicar fora
    const removerAoClicarFora = (event: Event) => {
        if (!elemento.contains(event.target as Node)) {
            popover.hide();
            removerAvisoConflito(elemento);
            document.removeEventListener('click', removerAoClicarFora);
        }
    };
    
    setTimeout(() => {
        document.addEventListener('click', removerAoClicarFora);
    }, 100);
    

}

// Função para remover aviso de conflito
function removerAvisoConflito(elemento: HTMLElement): void {
    // Destruir popover se existir
    const popoverInstance = (window as any).bootstrap.Popover.getInstance(elemento);
    if (popoverInstance) {
        popoverInstance.dispose();
    }

    // Remover atributos do popover
    elemento.removeAttribute('data-bs-toggle');
    elemento.removeAttribute('data-bs-placement');
    elemento.removeAttribute('data-bs-content');
    elemento.removeAttribute('data-bs-trigger');
    elemento.removeAttribute('data-bs-html');
    elemento.classList.remove('aviso-conflito');

    // Remover estilos do elemento
    elemento.classList.remove('border-danger');
    elemento.style.backgroundColor = '';
    elemento.style.boxShadow = '';
}

// Função para verificar conflitos de reserva (reutilizável)
function verificarConflitosReserva(
    uhInput: HTMLInputElement, 
    dataEntradaInput: HTMLInputElement, 
    horaEntradaInput: HTMLInputElement, 
    dataSaidaInput?: HTMLInputElement, 
    horaSaidaInput?: HTMLInputElement, 
    container?: HTMLElement, 
    callback?: (data: DisponibilidadeResponse) => void
): void {
    const uh: string = uhInput.value;
    const dataEntrada: string = dataEntradaInput.value;
    const horaEntrada: string = horaEntradaInput.value;
    const dataSaida: string = dataSaidaInput ? dataSaidaInput.value : dataEntrada; // IMPLEMENTAR: usar data de saída
    const horaSaida: string = horaSaidaInput ? horaSaidaInput.value : '12:00';     // IMPLEMENTAR: usar hora de saída

    if (!uh || !dataEntrada) {
        return;
    }

    // IMPLEMENTAR: Incluir data e hora de saída na requisição
    fetch('verificar_disponibilidade.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `uh=${encodeURIComponent(uh)}&dataEntrada=${encodeURIComponent(dataEntrada)}&horaEntrada=${encodeURIComponent(horaEntrada)}&dataSaida=${encodeURIComponent(dataSaida)}&horaSaida=${encodeURIComponent(horaSaida)}`
    })
        .then(response => response.json())
        .then((data: DisponibilidadeResponse) => {
            if (data.erro) {
                console.error('Erro:', data.erro);
                return;
            }

            if (callback) {
                callback(data);
            }

            // Mostrar/ocultar aviso de conflito
            if (container) {
                mostrarAvisoConflito(container, data.mensagem || 'Conflito detectado');
            }
        })
        .catch(error => {
            console.error('3_Erro na verificação:', error);
        });
}

// Função para configurar verificação automática de conflitos
function configurarVerificacaoConflitos(
    uhInput: HTMLInputElement, 
    dataEntradaInput: HTMLInputElement, 
    horaEntradaInput: HTMLInputElement, 
    container: HTMLElement
): void {
    function verificar(): void {
        verificarConflitosReserva(uhInput, dataEntradaInput, horaEntradaInput, undefined, undefined, container);
    }

    // Adicionar event listeners
    if (dataEntradaInput) {
        dataEntradaInput.addEventListener('change', verificar);
    }

    if (uhInput) {
        uhInput.addEventListener('change', verificar);
        uhInput.addEventListener('blur', verificar);
    }

    if (horaEntradaInput) {
        horaEntradaInput.addEventListener('change', verificar);
    }

    // Verificar imediatamente
    verificar();
}

// Exportar funções para uso global
declare global {
  interface Window {
    // REMOVIDO: verificarDisponibilidadeUH
    mostrarAvisoConflito: typeof mostrarAvisoConflito;
    removerAvisoConflito: typeof removerAvisoConflito;
    verificarConflitosReserva: typeof verificarConflitosReserva;
    configurarVerificacaoConflitos: typeof configurarVerificacaoConflitos;
    reinitializeEnterAsTab: typeof reinitializeEnterAsTab;
    validarCPF: typeof validarCPF;
    formatarEValidarCPF: typeof formatarEValidarCPF;
    verificarCPFDuplicado: typeof verificarCPFDuplicado;
    // Novas funções melhoradas
    verificarDisponibilidadeUHMelhorada: any;
    VerificacaoDisponibilidadeLocal: any;
    verificadorLocal: any;
  }
}

// REMOVIDO: Exportação da função antiga
// REMOVIDO: Declaração duplicada das interfaces Window

// Exportar funções para o window
// Remover estas linhas:
// window.mostrarAvisoConflito = mostrarAvisoConflito;
// window.removerAvisoConflito = removerAvisoConflito;
// window.verificarConflitosReserva = verificarConflitosReserva;
// window.configurarVerificacaoConflitos = configurarVerificacaoConflitos;
// window.reinitializeEnterAsTab = reinitializeEnterAsTab;
// window.validarCPF = validarCPF;
// window.formatarEValidarCPF = formatarEValidarCPF;
// window.verificarCPFDuplicado = verificarCPFDuplicado;

// Substituir export {}; por:
export { 
  validarCPF, 
  formatarEValidarCPF, 
  verificarCPFDuplicado,
  mostrarAvisoConflito,
  removerAvisoConflito,
  verificarConflitosReserva,
  configurarVerificacaoConflitos,
  reinitializeEnterAsTab
};
