<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar autenticação
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit;
}

// Definir variáveis para o formulário no modal
$form_action = 'hospedes_salvar.php'; // MUDANÇA: chamar direto o arquivo
$form_method = 'POST';
$hidden_fields = '<input type="hidden" name="acao" value="cadastrar">
                  <input type="hidden" name="ajax" value="1">
                  <input type="hidden" name="uh_reserva" id="uh_reserva_hidden">
                  <input type="hidden" name="data_reserva" id="data_reserva_hidden">';
$form_id = 'formHospedeCompleto';
$titulo_hospede = 'Cadastrar Novo Hóspede'; // Definir título específico para cadastro

// Capturar o conteúdo do formulário
ob_start();
include 'formulario_hospede.php';
$form_html = ob_get_clean();

// Retornar JSON com o HTML do formulário
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'html' => $form_html
]);
?>