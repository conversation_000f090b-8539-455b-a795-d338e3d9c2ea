<?php
// Arquivo dedicado para deletar consultas SQL do histórico
// Evita problemas de JSON parsing ao não passar pelo index.php

session_start();
include_once("config.php");

// Verificar se é uma requisição AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
    http_response_code(400);
    exit('Requisição inválida');
}

// Verificar se a ação é delete_query
if (!isset($_POST['action']) || $_POST['action'] !== 'delete_query') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Ação inválida']);
    exit;
}

// Verificar se o query_id foi fornecido
if (!isset($_POST['query_id']) || empty($_POST['query_id'])) {
    echo json_encode(['success' => false, 'message' => 'ID da consulta não fornecido']);
    exit;
}

$query_id = $_POST['query_id'];

try {
    // Preparar e executar a query de exclusão
    $stmt = $conn->prepare("DELETE FROM sql_query_history WHERE id = ?");
    $stmt->bind_param("i", $query_id);
    
    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Consulta excluída com sucesso']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Consulta não encontrada']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao executar exclusão']);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erro: ' . $e->getMessage()]);
}

$conn->close();
?>