<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar autenticação
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit;
}

// RECEBER DADOS DO HÓSPEDE VIA POST
$hospede_nome = $_POST['hospede_nome'] ?? null;

// CONFIGURAÇÃO PARA CONTEXTO AJAX
$ajax_context = true; // Ativa detecção de contexto AJAX

// Definir variáveis para o formulário no modal
$form_action = 'reservas_salvar.php'; // Chamada direta
$form_method = 'POST';
$hidden_fields = '<input type="hidden" name="acao" value="cadastrar">
                  <input type="hidden" name="ajax" value="1">
                  <input type="hidden" name="hospede_id" id="hospede_id_hidden">
                  <input type="hidden" name="hospede_nome" id="hospede_nome_hidden">
                  <input type="hidden" name="uh_selecionada" id="uh_selecionada_hidden">
                  <input type="hidden" name="data_selecionada" id="data_selecionada_hidden">';
$form_id = 'formReservaCompleto';

// Configurações específicas para modal
$show_hospede_info = false; // Não mostrar cabeçalho no modal
$readonly_uh = true; // UH readonly no modal (já selecionada)
$titulo_reserva = 'Nova Reserva para:'; // Adicionar título

// Capturar o conteúdo do formulário
ob_start();
include 'formulario_reserva.php';
$form_html = ob_get_clean();

// Retornar JSON com o HTML do formulário
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'html' => $form_html
]);
?>