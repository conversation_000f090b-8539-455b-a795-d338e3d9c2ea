body {
    background-color: #d5eed5;
    font-family: Arial, sans-serif;
}

/* Aplicar tema personalizado ao body */
body.tema-personalizado {
    background-color: rgba(var(--cor-primaria-rgb), 0.1);
    transition: background-color 0.5s ease;
}



* {
    margin: 0;
    padding: 0
}

.header-container {
    width: 100%;
}

.topo-area {
    width: 100%;
    height: calc(30vh/2);
    background-image: url('../../img/cssist.gif') !important;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 10px;
    box-sizing: border-box;
}


.topo-area h1 {
    margin: 0;
    padding: 0;
}

.topo-area img {
    max-width: 100%;
    width: auto;
    height: auto;
    max-height: 60px;
    display: block;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* Opcional: ajuste para telas menores */
@media (max-width: 768px) {

    .banner-area {
        height: 25vh;
        /* Altura menor em telas pequenas */
    }

    .topo-area {
        height: calc(25vh / 2);
        /* Mantém a proporção de metade */
    }

    .topo-area img {
        max-width: 150px;
        max-height: 50px;
        /* Reduz a altura máxima em telas menores */
    }
}

.banner-area {
    width: 100%;
    height: 30vh;
    background-image: url('../../img/fundo_vale.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.header-title {
    background-color: rgba(32, 178, 170, 0.5);
    padding: 10px 20px;
    border-radius: 10px;
}

.header-menu {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.header-menu a {
    text-decoration: none;
    font-size: 1.2rem;
    color: #3e643e;
    transition: color 0.3s ease;
}

.header-menu a:hover {
    color: #1f05b6;
}


.modal-dialog {
    max-width: 400px;
}

.header-title {
    background-color: rgba(32, 178, 170, 0.5);
    padding: 10px 20px;
    border-radius: 10px;
}

.main-container {
    display: flex;
    flex-wrap: nowrap;
    min-height: 100vh;
    /* 80% da altura da tela restante */
    display: flex;

}

.login-column {
    flex: 0.6;
    padding: 20px;
    background-color: #66ffff;
}

.content-column {
    flex: 2;
    padding: 40px;
    text-align: center;
}

.success-stories-column {
    flex: 1;
    padding: 20px;
    background-color: #f8f9fa;
}

.advantage-section,
.plans-section,
.success-stories {
    margin-bottom: 40px;
}

.btn-plan {
    margin-top: 20px;
}